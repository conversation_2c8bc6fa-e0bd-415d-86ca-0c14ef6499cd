{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\underline.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\underline.js", "mtime": 1749109533897}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NhbGxTdXBlci5qcyIpKTsKdmFyIF9pbmhlcml0czIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIF9pbmxpbmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2Jsb3RzL2lubGluZS5qcyIpKTsKdmFyIFVuZGVybGluZSA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX0lubGluZSkgewogIGZ1bmN0aW9uIFVuZGVybGluZSgpIHsKICAgICgwLCBfY2xhc3NDYWxsQ2hlY2syLmRlZmF1bHQpKHRoaXMsIFVuZGVybGluZSk7CiAgICByZXR1cm4gKDAsIF9jYWxsU3VwZXIyLmRlZmF1bHQpKHRoaXMsIFVuZGVybGluZSwgYXJndW1lbnRzKTsKICB9CiAgKDAsIF9pbmhlcml0czIuZGVmYXVsdCkoVW5kZXJsaW5lLCBfSW5saW5lKTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoVW5kZXJsaW5lKTsKfShfaW5saW5lLmRlZmF1bHQpOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShVbmRlcmxpbmUsICJibG90TmFtZSIsICd1bmRlcmxpbmUnKTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoVW5kZXJsaW5lLCAidGFnTmFtZSIsICdVJyk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IFVuZGVybGluZTs="}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "Underline", "_Inline", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "Inline", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/underline.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Underline extends Inline {\n  static blotName = 'underline';\n  static tagName = 'U';\n}\n\nexport default Underline;\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,SAAS,0BAAAC,OAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,SAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,SAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,SAAA,EAAAC,OAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,SAAA;AAAA,EAASQ,eAAM;AAAA,IAAAC,gBAAA,CAAAN,OAAA,EAAxBH,SAAS,cACK,WAAW;AAAA,IAAAS,gBAAA,CAAAN,OAAA,EADzBH,SAAS,aAEI,GAAG;AAAA,IAAAU,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAGPH,SAAS", "ignoreList": []}]}