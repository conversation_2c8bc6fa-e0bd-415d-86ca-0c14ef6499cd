{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/histogram/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5F,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,YAAY,EAAE,MAAM,iCAAiC,CAAC;AAC/D,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAGlE;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAgC;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA4E,OAAO,KAAnF,EAAE,QAAQ,GAAkE,OAAO,SAAzE,EAAE,SAAS,GAAuD,OAAO,UAA9D,EAAE,QAAQ,GAA6C,OAAO,SAApD,EAAE,KAAK,GAAsC,OAAO,MAA7C,EAAE,UAAU,GAA0B,OAAO,WAAjC,EAAE,MAAM,GAAkB,OAAO,OAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAEhG,OAAO;IACP,IAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IAE/E,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAErB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,WAAW,EAAE,UAAU;YACvB,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,EAAE,WAAW;aACnB;SACF;KACF,CAAC,CAAC;IAEH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEZ,KAAK;IACL,IAAI,MAAM,IAAI,UAAU,EAAE;QACxB,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;KAClC;SAAM;QACL,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAgC;;IACpC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjC,OAAO,IAAI,CACT,KAAK;QACH,GAAC,iBAAiB,IAAG,KAAK;QAC1B,GAAC,iBAAiB,IAAG,KAAK;YAC1B,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAgC;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjC,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;KACtC;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;KACtC;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;KACtC;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;KACtC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAAgC;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1B,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEjD,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,CAAC,iBAAiB,CAAC;YAC3B,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC;IACtD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,OAAO,CAAC,aAAa,CAAC,EACtB,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,OAAO,EACP,WAAW,EACX,SAAS,CACV,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { animation, interaction, scale, state, theme, tooltip } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { pattern } from '../../adaptor/pattern';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { binHistogram } from '../../utils/transform/histogram';\nimport { HISTOGRAM_X_FIELD, HISTOGRAM_Y_FIELD } from './constant';\nimport { HistogramOptions } from './types';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<HistogramOptions>): Params<HistogramOptions> {\n  const { chart, options } = params;\n  const { data, binField, binNumber, binWidth, color, stackField, legend, columnStyle } = options;\n\n  // 处理数据\n  const plotData = binHistogram(data, binField, binWidth, binNumber, stackField);\n\n  chart.data(plotData);\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: HISTOGRAM_X_FIELD,\n      yField: HISTOGRAM_Y_FIELD,\n      seriesField: stackField,\n      isStack: true,\n      interval: {\n        color,\n        style: columnStyle,\n      },\n    },\n  });\n\n  interval(p);\n\n  // 图例\n  if (legend && stackField) {\n    chart.legend(stackField, legend);\n  } else {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<HistogramOptions>): Params<HistogramOptions> {\n  const { options } = params;\n  const { xAxis, yAxis } = options;\n\n  return flow(\n    scale({\n      [HISTOGRAM_X_FIELD]: xAxis,\n      [HISTOGRAM_Y_FIELD]: yAxis,\n    })\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<HistogramOptions>): Params<HistogramOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(HISTOGRAM_X_FIELD, false);\n  } else {\n    chart.axis(HISTOGRAM_X_FIELD, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(HISTOGRAM_Y_FIELD, false);\n  } else {\n    chart.axis(HISTOGRAM_Y_FIELD, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * label 配置\n * @param params\n */\nfunction label(params: Params<HistogramOptions>): Params<HistogramOptions> {\n  const { chart, options } = params;\n  const { label } = options;\n\n  const geometry = findGeometry(chart, 'interval');\n\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    geometry.label({\n      fields: [HISTOGRAM_Y_FIELD],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * 直方图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<HistogramOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    pattern('columnStyle'),\n    geometry,\n    meta,\n    axis,\n    state,\n    label,\n    tooltip,\n    interaction,\n    animation\n  )(params);\n}\n"]}