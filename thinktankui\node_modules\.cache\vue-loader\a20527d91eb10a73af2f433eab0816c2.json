{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Breadcrumb\\index.vue?vue&type=style&index=0&id=b50ef614&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1749109381322}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1icmVhZGNydW1iLmVsLWJyZWFkY3J1bWIgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDUwcHg7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQogIC5uby1yZWRpcmVjdCB7DQogICAgY29sb3I6ICM5N2E4YmU7DQogICAgY3Vyc29yOiB0ZXh0Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\r\n        <span v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\" class=\"no-redirect\">{{ item.meta.title }}</span>\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith('/redirect/')) {\r\n        return\r\n      }\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = []\r\n      const router = this.$route\r\n      const pathNum = this.findPathNum(router.path)\r\n      // multi-level menu\r\n      if (pathNum > 2) {\r\n        const reg = /\\/\\w+/gi\r\n        const pathList = router.path.match(reg).map((item, index) => {\r\n          if (index !== 0) item = item.slice(1)\r\n          return item\r\n        })\r\n        this.getMatched(pathList, this.$store.getters.defaultRoutes, matched)\r\n      } else {\r\n        matched = router.matched.filter(item => item.meta && item.meta.title)\r\n      }\r\n      // 判断是否为首页\r\n      if (!this.isDashboard(matched[0])) {\r\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched)\r\n      }\r\n      this.levelList = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)\r\n    },\r\n    findPathNum(str, char = \"/\") {\r\n      let index = str.indexOf(char)\r\n      let num = 0\r\n      while (index !== -1) {\r\n        num++\r\n        index = str.indexOf(char, index + 1)\r\n      }\r\n      return num\r\n    },\r\n    getMatched(pathList, routeList, matched) {\r\n      let data = routeList.find(item => item.path == pathList[0] || (item.name += '').toLowerCase() == pathList[0])\r\n      if (data) {\r\n        matched.push(data)\r\n        if (data.children && pathList.length) {\r\n          pathList.shift()\r\n          this.getMatched(pathList, data.children, matched)\r\n        }\r\n      }\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim() === 'Index'\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      this.$router.push(path)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}