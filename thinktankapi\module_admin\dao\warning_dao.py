from datetime import datetime, time
from sqlalchemy import and_, desc, func, select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from module_admin.entity.warning_entity import WarningRecord, WarningScheme, WarningSettings
from module_admin.entity.vo.warning_vo import (
    WarningRecordPageQueryModel, WarningSchemePageQueryModel
)
from utils.page_util import PageUtil


class WarningRecordDao:
    """
    预警记录数据访问层
    """

    @classmethod
    async def get_warning_record_list(cls, db: AsyncSession, query_object: WarningRecordPageQueryModel, is_page: bool = False):
        """
        根据查询参数获取预警记录列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警记录列表信息对象
        """
        query = select(WarningRecord).options(selectinload(WarningRecord.scheme))
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_id:
            conditions.append(WarningRecord.scheme_id == query_object.scheme_id)
            
        if query_object.warning_type:
            conditions.append(WarningRecord.warning_type.like(f'%{query_object.warning_type}%'))
            
        if query_object.content:
            conditions.append(WarningRecord.content.like(f'%{query_object.content}%'))
            
        if query_object.keywords:
            conditions.append(WarningRecord.keywords.like(f'%{query_object.keywords}%'))
            
        if query_object.status is not None:
            conditions.append(WarningRecord.status == query_object.status)
            
        if query_object.begin_time and query_object.end_time:
            conditions.append(
                WarningRecord.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.order_by(desc(WarningRecord.create_time))
        
        if is_page:
            # 获取总数
            count_query = select(func.count(WarningRecord.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await db.execute(count_query)
            total = count_result.scalar()
            
            # 分页查询
            offset = (query_object.page_num - 1) * query_object.page_size
            query = query.offset(offset).limit(query_object.page_size)
            
            result = await db.execute(query)
            records = result.scalars().all()
            
            return PageUtil.get_page_result([record.__dict__ for record in records], total, query_object.page_num, query_object.page_size)
        else:
            result = await db.execute(query)
            records = result.scalars().all()
            return [record.__dict__ for record in records]

    @classmethod
    async def get_warning_record_by_id(cls, db: AsyncSession, record_id: int):
        """
        根据记录ID获取预警记录详细信息

        :param db: orm对象
        :param record_id: 记录ID
        :return: 预警记录详细信息对象
        """
        query = select(WarningRecord).options(selectinload(WarningRecord.scheme)).where(WarningRecord.id == record_id)
        result = await db.execute(query)
        record = result.scalar_one_or_none()
        return record

    @classmethod
    async def add_warning_record(cls, db: AsyncSession, record_data: dict):
        """
        新增预警记录

        :param db: orm对象
        :param record_data: 记录数据
        :return: 新增结果
        """
        new_record = WarningRecord(**record_data)
        db.add(new_record)
        await db.commit()
        await db.refresh(new_record)
        return new_record

    @classmethod
    async def edit_warning_record(cls, db: AsyncSession, record_data: dict):
        """
        编辑预警记录

        :param db: orm对象
        :param record_data: 记录数据
        :return: 编辑结果
        """
        query = update(WarningRecord).where(WarningRecord.id == record_data['id']).values(**record_data)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    @classmethod
    async def delete_warning_records(cls, db: AsyncSession, record_ids: list):
        """
        删除预警记录

        :param db: orm对象
        :param record_ids: 记录ID列表
        :return: 删除结果
        """
        query = delete(WarningRecord).where(WarningRecord.id.in_(record_ids))
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    @classmethod
    async def get_warning_statistics(cls, db: AsyncSession):
        """
        获取预警统计信息

        :param db: orm对象
        :return: 统计信息
        """
        # 总记录数
        total_query = select(func.count(WarningRecord.id))
        total_result = await db.execute(total_query)
        total_records = total_result.scalar()
        
        # 按状态统计
        status_query = select(
            WarningRecord.status,
            func.count(WarningRecord.id).label('count')
        ).group_by(WarningRecord.status)
        status_result = await db.execute(status_query)
        status_counts = {row.status: row.count for row in status_result.fetchall()}
        
        # 今日新增
        today = datetime.now().date()
        today_query = select(func.count(WarningRecord.id)).where(
            func.date(WarningRecord.create_time) == today
        )
        today_result = await db.execute(today_query)
        today_count = today_result.scalar()
        
        return {
            'total_records': total_records,
            'unprocessed_count': status_counts.get(0, 0),
            'processed_count': status_counts.get(1, 0),
            'ignored_count': status_counts.get(2, 0),
            'today_count': today_count
        }


class WarningSchemeDao:
    """
    预警方案数据访问层
    """

    @classmethod
    async def get_warning_scheme_list(cls, db: AsyncSession, query_object: WarningSchemePageQueryModel, is_page: bool = False):
        """
        根据查询参数获取预警方案列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警方案列表信息对象
        """
        query = select(WarningScheme)
        
        # 构建查询条件
        conditions = []
        
        if query_object.scheme_name:
            conditions.append(WarningScheme.scheme_name.like(f'%{query_object.scheme_name}%'))
            
        if query_object.scheme_type:
            conditions.append(WarningScheme.scheme_type == query_object.scheme_type)
            
        if query_object.is_active is not None:
            conditions.append(WarningScheme.is_active == query_object.is_active)
            
        if query_object.begin_time and query_object.end_time:
            conditions.append(
                WarningScheme.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.order_by(desc(WarningScheme.create_time))
        
        if is_page:
            # 获取总数
            count_query = select(func.count(WarningScheme.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await db.execute(count_query)
            total = count_result.scalar()
            
            # 分页查询
            offset = (query_object.page_num - 1) * query_object.page_size
            query = query.offset(offset).limit(query_object.page_size)
            
            result = await db.execute(query)
            schemes = result.scalars().all()
            
            return PageUtil.get_page_result([scheme.__dict__ for scheme in schemes], total, query_object.page_num, query_object.page_size)
        else:
            result = await db.execute(query)
            schemes = result.scalars().all()
            return [scheme.__dict__ for scheme in schemes]

    @classmethod
    async def get_warning_scheme_by_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据方案ID获取预警方案详细信息

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 预警方案详细信息对象
        """
        query = select(WarningScheme).where(WarningScheme.id == scheme_id)
        result = await db.execute(query)
        scheme = result.scalar_one_or_none()
        return scheme

    @classmethod
    async def add_warning_scheme(cls, db: AsyncSession, scheme_data: dict):
        """
        新增预警方案

        :param db: orm对象
        :param scheme_data: 方案数据
        :return: 新增结果
        """
        new_scheme = WarningScheme(**scheme_data)
        db.add(new_scheme)
        await db.commit()
        await db.refresh(new_scheme)
        return new_scheme

    @classmethod
    async def edit_warning_scheme(cls, db: AsyncSession, scheme_data: dict):
        """
        编辑预警方案

        :param db: orm对象
        :param scheme_data: 方案数据
        :return: 编辑结果
        """
        query = update(WarningScheme).where(WarningScheme.id == scheme_data['id']).values(**scheme_data)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    @classmethod
    async def delete_warning_schemes(cls, db: AsyncSession, scheme_ids: list):
        """
        删除预警方案

        :param db: orm对象
        :param scheme_ids: 方案ID列表
        :return: 删除结果
        """
        query = delete(WarningScheme).where(WarningScheme.id.in_(scheme_ids))
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    @classmethod
    async def get_scheme_statistics(cls, db: AsyncSession):
        """
        获取方案统计信息

        :param db: orm对象
        :return: 统计信息
        """
        # 总方案数
        total_query = select(func.count(WarningScheme.id))
        total_result = await db.execute(total_query)
        scheme_count = total_result.scalar()
        
        # 启用方案数
        active_query = select(func.count(WarningScheme.id)).where(WarningScheme.is_active == True)
        active_result = await db.execute(active_query)
        active_scheme_count = active_result.scalar()
        
        return {
            'scheme_count': scheme_count,
            'active_scheme_count': active_scheme_count
        }


class WarningSettingsDao:
    """
    预警设置数据访问层
    """

    @classmethod
    async def get_warning_settings_by_scheme_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据方案ID获取预警设置

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 预警设置对象
        """
        query = select(WarningSettings).where(WarningSettings.scheme_id == scheme_id)
        result = await db.execute(query)
        settings = result.scalar_one_or_none()
        return settings

    @classmethod
    async def save_warning_settings(cls, db: AsyncSession, settings_data: dict):
        """
        保存预警设置（新增或更新）

        :param db: orm对象
        :param settings_data: 设置数据
        :return: 保存结果
        """
        # 先查询是否已存在
        existing_query = select(WarningSettings).where(WarningSettings.scheme_id == settings_data['scheme_id'])
        existing_result = await db.execute(existing_query)
        existing_settings = existing_result.scalar_one_or_none()

        if existing_settings:
            # 更新现有设置
            query = update(WarningSettings).where(
                WarningSettings.scheme_id == settings_data['scheme_id']
            ).values(**settings_data)
            result = await db.execute(query)
            await db.commit()
            return result.rowcount
        else:
            # 新增设置
            new_settings = WarningSettings(**settings_data)
            db.add(new_settings)
            await db.commit()
            await db.refresh(new_settings)
            return new_settings

    @classmethod
    async def delete_warning_settings_by_scheme_id(cls, db: AsyncSession, scheme_id: int):
        """
        根据方案ID删除预警设置

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 删除结果
        """
        query = delete(WarningSettings).where(WarningSettings.scheme_id == scheme_id)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount
