{"version": 3, "file": "highlight-util.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/highlight-util.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAGlC,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAEtC,IAAM,eAAe,GAAG,UAAU,CAAC;AACnC,IAAM,aAAa,GAAG,QAAQ,CAAC;AAE/B;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,IAAU;IACvC,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IACnC,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAE;QAChB,IAAI,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC9B,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SACnC;QACD,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;YAChC,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;SACrC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAID;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAAC,QAAmB,EAAE,QAAuB,EAAE,MAAe;IAC1F,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAE;QAChB,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE;YAChB,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAChC,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;SACpC;aAAM;YACL,IAAI,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;gBAC9B,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACnC;YACD,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;SACtC;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { View } from '../../../chart';\nimport Element from '../../../geometry/element/';\nimport { getElements } from '../util';\n\nconst STATUS_UNACTIVE = 'inactive';\nconst STATUS_ACTIVE = 'active';\n\n/**\n * @ignore\n * 清理 highlight 效果\n * @param view View 或者 Chart\n */\nexport function clearHighlight(view: View) {\n  const elements = getElements(view);\n  each(elements, (el) => {\n    if (el.hasState(STATUS_ACTIVE)) {\n      el.setState(STATUS_ACTIVE, false);\n    }\n    if (el.hasState(STATUS_UNACTIVE)) {\n      el.setState(STATUS_UNACTIVE, false);\n    }\n  });\n}\n\ntype MatchCallback = (el: Element) => boolean;\n\n/**\n * @ignore\n * 设置多个元素的 highlight\n * @param elements 元素集合\n * @param callback 设置回调函数\n * @param enable 设置或者取消\n */\nexport function setHighlightBy(elements: Element[], callback: MatchCallback, enable: boolean) {\n  each(elements, (el) => {\n    // 需要处理 active 和 unactive 的互斥\n    if (callback(el)) {\n      if (el.hasState(STATUS_UNACTIVE)) {\n        el.setState(STATUS_UNACTIVE, false);\n      }\n      el.setState(STATUS_ACTIVE, enable);\n    } else {\n      if (el.hasState(STATUS_ACTIVE)) {\n        el.setState(STATUS_ACTIVE, false);\n      }\n      el.setState(STATUS_UNACTIVE, enable);\n    }\n  });\n}\n"]}