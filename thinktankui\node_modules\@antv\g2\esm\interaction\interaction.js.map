{"version": 3, "file": "interaction.js", "sourceRoot": "", "sources": ["../../src/interaction/interaction.ts"], "names": [], "mappings": "AAKA;;GAEG;AACH;IAME,qBAAY,IAAU,EAAE,GAAgB;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,0BAAI,GAAX;QACE,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG;IACO,gCAAU,GAApB,cAAwB,CAAC;IAEzB;;OAEG;IACO,iCAAW,GAArB,cAAyB,CAAC;IAE1B;;OAEG;IACI,6BAAO,GAAd;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IACH,kBAAC;AAAD,CAAC,AAlCD,IAkCC", "sourcesContent": ["import { View } from '../chart';\nimport { LooseObject } from '../interface';\n\nexport type InteractionConstructor = new (view: View, cfg: LooseObject) => Interaction;\n\n/**\n * 交互的基类。\n */\nexport default class Interaction {\n  /** view 或者 chart */\n  protected view: View;\n  /** 配置项 */\n  protected cfg: LooseObject;\n\n  constructor(view: View, cfg: LooseObject) {\n    this.view = view;\n    this.cfg = cfg;\n  }\n\n  /**\n   * 初始化。\n   */\n  public init() {\n    this.initEvents();\n  }\n\n  /**\n   * 绑定事件\n   */\n  protected initEvents() {}\n\n  /**\n   * 销毁事件\n   */\n  protected clearEvents() {}\n\n  /**\n   * 销毁。\n   */\n  public destroy() {\n    this.clearEvents();\n  }\n}\n"]}