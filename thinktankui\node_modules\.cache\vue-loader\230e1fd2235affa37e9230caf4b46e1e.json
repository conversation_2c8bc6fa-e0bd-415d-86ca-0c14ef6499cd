{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\BarChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\BarChart.vue", "mtime": 1749109381342}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BarChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BarChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nconst animationDuration = 6000\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { // 坐标轴指示器，坐标轴触发有效\r\n            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'\r\n          }\r\n        },\r\n        grid: {\r\n          top: 10,\r\n          left: '2%',\r\n          right: '2%',\r\n          bottom: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          axisTick: {\r\n            alignWithLabel: true\r\n          }\r\n        }],\r\n        yAxis: [{\r\n          type: 'value',\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        }],\r\n        series: [{\r\n          name: 'pageA',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [79, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageB',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [80, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }, {\r\n          name: 'pageC',\r\n          type: 'bar',\r\n          stack: 'vistors',\r\n          barWidth: '60%',\r\n          data: [30, 52, 200, 334, 390, 330, 220],\r\n          animationDuration\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}