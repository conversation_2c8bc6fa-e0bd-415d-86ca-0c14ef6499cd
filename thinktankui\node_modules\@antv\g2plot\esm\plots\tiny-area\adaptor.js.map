{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-area/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC7F,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAGjD;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA+B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA+D,OAAO,KAAtE,EAAE,KAAK,GAAwD,OAAO,MAA/D,EAAE,SAAS,GAA6C,OAAO,UAApD,EAAS,YAAY,GAAwB,OAAO,MAA/B,EAAQ,WAAW,GAAK,OAAO,KAAZ,CAAa;IACnF,IAAM,UAAU,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAC;IAEvC,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvB,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrC,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,EAAE,KAAK,OAAA,EAAE,KAAK,EAAE,SAAS,EAAE;YACjC,IAAI,EAAE,WAAW;YACjB,KAAK,EAAE,YAAY;SACpB;KACF,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;IACxE,IAAM,WAAW,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAEhG,mBAAmB;IACnB,IAAI,CAAC,OAAO,CAAC,CAAC;IACd,IAAI,CAAC,MAAM,CAAC,CAAC;IACb,KAAK,CAAC,WAAW,CAAC,CAAC;IAEnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;;IAC1C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAkB,OAAO,MAAzB,EAAE,KAAK,GAAW,OAAO,MAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IACvC,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,OAAO,IAAI,CACT,KAAK;QAED,GAAC,OAAO,IAAG,KAAK;QAChB,GAAC,OAAO,IAAG,KAAK;;QAGhB,GAAC,OAAO,IAAG;YACT,IAAI,EAAE,KAAK;SACZ;QACD,GAAC,OAAO,IAAG,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC;YAEpD,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACrG,CAAC", "sourcesContent": ["import { animation, annotation, pattern, scale, theme, tooltip } from '../../adaptor/common';\nimport { area, line, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { adjustYMetaByZero } from '../../utils/data';\nimport { X_FIELD, Y_FIELD } from '../tiny-line/constants';\nimport { getTinyData } from '../tiny-line/utils';\nimport { TinyAreaOptions } from './types';\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<TinyAreaOptions>): Params<TinyAreaOptions> {\n  const { chart, options } = params;\n  const { data, color, areaStyle, point: pointOptions, line: lineOptions } = options;\n  const pointState = pointOptions?.state;\n\n  const seriesData = getTinyData(data);\n\n  chart.data(seriesData);\n\n  const primary = deepAssign({}, params, {\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      area: { color, style: areaStyle },\n      line: lineOptions,\n      point: pointOptions,\n    },\n  });\n  const second = deepAssign({}, primary, { options: { tooltip: false } });\n  const pointParams = deepAssign({}, primary, { options: { tooltip: false, state: pointState } });\n\n  // area geometry 处理\n  area(primary);\n  line(second);\n  point(pointParams);\n\n  chart.axis(false);\n  chart.legend(false);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<TinyAreaOptions>): Params<TinyAreaOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, data } = options;\n  const seriesData = getTinyData(data);\n\n  return flow(\n    scale(\n      {\n        [X_FIELD]: xAxis,\n        [Y_FIELD]: yAxis,\n      },\n      {\n        [X_FIELD]: {\n          type: 'cat',\n        },\n        [Y_FIELD]: adjustYMetaByZero(seriesData, Y_FIELD),\n      }\n    )\n  )(params);\n}\n\n/**\n * 迷你面积图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<TinyAreaOptions>) {\n  return flow(pattern('areaStyle'), geometry, meta, tooltip, theme, animation, annotation())(params);\n}\n"]}