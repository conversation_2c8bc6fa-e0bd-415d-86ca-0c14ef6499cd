{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/circle-packing/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,EACL,SAAS,EACT,UAAU,EACV,WAAW,IAAI,eAAe,EAC9B,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,GACN,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,KAAK,EAAE,MAAM,gCAAgC,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAExC,OAAO,EAAE,uBAAuB,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAEjE;;;GAGG;AACH,SAAS,cAAc,CAAC,MAAoC;IAClD,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAEvE,OAAO,UAAU,CACf;QACE,OAAO,EAAE;YACP,IAAI,EAAE,UAAC,EAAK;oBAAH,CAAC,OAAA;gBAAO,OAAA,CAAC,GAAG,QAAQ;YAAZ,CAAY,EAAE,0BAA0B;SAC1D;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CAAC,MAAoC;IAC3C,IAAA,OAAO,GAAY,MAAM,QAAlB,EAAE,KAAK,GAAK,MAAM,MAAX,CAAY;IAClC,mCAAmC;IACnC,IAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC7B,IAAA,OAAO,GAA+B,OAAO,QAAtC,EAAE,aAAa,GAAgB,OAAO,cAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAEtD,IAAI,iBAAiB,GAAG,aAAa,CAAC;IACtC,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE;QACtB,IAAM,wBAAwB,GAAG,sBAAsB,CACrD,KAAK,CAAC,aAAa,EACnB,GAAG,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAC3C,CAAC;QACF,iBAAiB,GAAG,iBAAiB,CAAC,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC,CAAC;KAClF;IAEO,IAAA,YAAY,GAAK,uBAAuB,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,CAAC,aAAvE,CAAwE;IAC5F,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC;IAC7B,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC;IAExB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAoC;IAC5C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAoB,KAAK,QAAzB,EAAE,aAAa,GAAK,KAAK,cAAV,CAAW;IACjC,IAAA,KAAK,GAAoF,OAAO,MAA3F,EAAE,UAAU,GAAwE,OAAO,WAA/E,EAAE,UAAU,GAA4D,OAAO,WAAnE,EAAE,eAAe,GAA2C,OAAO,gBAAlD,EAAE,SAAS,GAAgC,OAAO,UAAvC,EAAE,KAA8B,OAAO,UAAvB,EAAd,SAAS,mBAAG,EAAE,KAAA,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAEzG,IAAM,IAAI,GAAG,aAAa,CAAC;QACzB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,eAAe,iBAAA;QACf,eAAe,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO;QACnC,SAAS,WAAA;KACV,CAAC,CAAC;IACH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,IAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC7B,IAAA,SAAS,GAAK,uBAAuB,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC,UAAnE,CAAoE;IACrF,oDAAoD;IACpD,IAAI,UAAU,GAAG,UAAC,EAAK;YAAH,CAAC,OAAA;QAAO,OAAA,CAAC,GAAG,SAAS;IAAb,CAAa,CAAC,CAAC,OAAO;IAElD,IAAI,SAAS,EAAE;QACb,UAAU,GAAG,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,SAAS,CAAC,GAAG,SAAS,EAAxB,CAAwB,CAAC,CAAC,mBAAmB;KAClE;IAED,WAAW;IACX,KAAK,CACH,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,UAAU;YACvB,SAAS,WAAA;YACT,SAAS,kCAAM,UAAU,SAAK,SAAS,OAAC;YACxC,KAAK,EAAE;gBACL,KAAK,OAAA;gBACL,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,QAAQ;gBACf,IAAI,EAAE,UAAU;aACjB;SACF;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAAoC;IACvD,OAAO,IAAI,CACT,KAAK,CACH,EAAE,EACF;QACE,aAAa;QACb,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;QAC3D,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;KAC5D,CACF,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CAAC,MAAoC;IAC3C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM;QACL,IAAI,cAAc,GAAG,OAAO,CAAC;QAC7B,4DAA4D;QAC5D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YAC3B,cAAc,GAAG,UAAU,CACzB,EAAE,EACF;gBACE,WAAW,EAAE,UAAC,KAA0B;oBACtC,OAAA,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;wBACb,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;wBACjD,IAAM,aAAa,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;wBACnE,IAAM,cAAc,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;wBACrE,6BACK,IAAI,KACP,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,KAAK,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IACtC;oBACJ,CAAC,CAAC;gBATF,CASE;aACL,EACD,cAAc,CACf,CAAC;SACH;QACD,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;KAC/B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAoC;IACxC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,OAA6B;IAC/C,IAAA,SAAS,GAAwB,OAAO,UAA/B,EAAE,KAAsB,OAAO,aAAZ,EAAjB,YAAY,mBAAG,EAAE,KAAA,CAAa;IAEjD,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE;QACtB,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE;YAC7B,YAAY,kCACP,YAAY;gBACf;oBACE,IAAI,EAAE,YAAY;oBAClB,GAAG,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,eAAA,EAAE,eAAe,EAAE,IAAI,EAAE;iBAC1E;qBACF;SACF,CAAC,CAAC;KACJ;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAAC,MAAoC;IAC/C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAElC,eAAe,CAAC;QACd,KAAK,OAAA;QACL,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC;KACrC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAoC;IAC1D,OAAO,IAAI,CACT,OAAO,CAAC,YAAY,CAAC,EACrB,cAAc,EACd,OAAO,EACP,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,SAAS,EACT,UAAU,EAAE,CACb,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { get } from '@antv/util';\nimport {\n  animation,\n  annotation,\n  interaction as baseInteraction,\n  legend,\n  pattern,\n  scale,\n  theme,\n} from '../../adaptor/common';\nimport { point } from '../../adaptor/geometries/point';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { getAdjustAppendPadding, resolveAllPadding } from '../../utils/padding';\nimport { RAW_FIELDS } from './constant';\nimport { CirclePackingOptions } from './types';\nimport { resolvePaddingForCircle, transformData } from './utils';\n\n/**\n * 获取默认 option\n * @param params\n */\nfunction defaultOptions(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { chart } = params;\n  const diameter = Math.min(chart.viewBBox.width, chart.viewBBox.height);\n\n  return deepAssign(\n    {\n      options: {\n        size: ({ r }) => r * diameter, // 当autofit：false时，默认给固定半径\n      },\n    },\n    params\n  );\n}\n\n/**\n * padding 配置\n * @param params\n */\nfunction padding(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { options, chart } = params;\n  // 通过改变 padding，修改 coordinate 的绘制区域\n  const containerSize = chart.viewBBox;\n  const { padding, appendPadding, drilldown } = options;\n\n  let tempAppendPadding = appendPadding;\n  if (drilldown?.enabled) {\n    const appendPaddingByDrilldown = getAdjustAppendPadding(\n      chart.appendPadding,\n      get(drilldown, ['breadCrumb', 'position'])\n    );\n    tempAppendPadding = resolveAllPadding([appendPaddingByDrilldown, appendPadding]);\n  }\n\n  const { finalPadding } = resolvePaddingForCircle(padding, tempAppendPadding, containerSize);\n  chart.padding = finalPadding;\n  chart.appendPadding = 0;\n\n  return params;\n}\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { chart, options } = params;\n  const { padding, appendPadding } = chart;\n  const { color, colorField, pointStyle, hierarchyConfig, sizeField, rawFields = [], drilldown } = options;\n\n  const data = transformData({\n    data: options.data,\n    hierarchyConfig,\n    enableDrillDown: drilldown?.enabled,\n    rawFields,\n  });\n  chart.data(data);\n\n  const containerSize = chart.viewBBox;\n  const { finalSize } = resolvePaddingForCircle(padding, appendPadding, containerSize);\n  // 有sizeField的时候，例如 value ，可以选择映射 size 函数，自己计算出映射的半径\n  let circleSize = ({ r }) => r * finalSize; // 默认配置\n\n  if (sizeField) {\n    circleSize = (d) => d[sizeField] * finalSize; // 目前只有 r 通道映射效果会正常\n  }\n\n  // geometry\n  point(\n    deepAssign({}, params, {\n      options: {\n        xField: 'x',\n        yField: 'y',\n        seriesField: colorField,\n        sizeField,\n        rawFields: [...RAW_FIELDS, ...rawFields],\n        point: {\n          color,\n          style: pointStyle,\n          shape: 'circle',\n          size: circleSize,\n        },\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  return flow(\n    scale(\n      {},\n      {\n        // 必须强制为 nice\n        x: { min: 0, max: 1, minLimit: 0, maxLimit: 1, nice: true },\n        y: { min: 0, max: 1, minLimit: 0, maxLimit: 1, nice: true },\n      }\n    )\n  )(params);\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nfunction tooltip(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  if (tooltip === false) {\n    chart.tooltip(false);\n  } else {\n    let tooltipOptions = tooltip;\n    // 设置了 fields，就不进行 customItems 了; 设置 formatter 时，需要搭配 fields\n    if (!get(tooltip, 'fields')) {\n      tooltipOptions = deepAssign(\n        {},\n        {\n          customItems: (items: Types.TooltipItem[]) =>\n            items.map((item) => {\n              const scales = get(chart.getOptions(), 'scales');\n              const nameFormatter = get(scales, ['name', 'formatter'], (v) => v);\n              const valueFormatter = get(scales, ['value', 'formatter'], (v) => v);\n              return {\n                ...item,\n                name: nameFormatter(item.data.name),\n                value: valueFormatter(item.data.value),\n              };\n            }),\n        },\n        tooltipOptions\n      );\n    }\n    chart.tooltip(tooltipOptions);\n  }\n\n  return params;\n}\n\n/**\n * 坐标轴, 默认关闭\n * @param params\n */\nfunction axis(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { chart } = params;\n  chart.axis(false);\n  return params;\n}\n\nfunction adaptorInteraction(options: CirclePackingOptions): CirclePackingOptions {\n  const { drilldown, interactions = [] } = options;\n\n  if (drilldown?.enabled) {\n    return deepAssign({}, options, {\n      interactions: [\n        ...interactions,\n        {\n          type: 'drill-down',\n          cfg: { drillDownConfig: drilldown, transformData, enableDrillDown: true },\n        },\n      ],\n    });\n  }\n  return options;\n}\n\n/**\n * 交互配置\n * @param params\n * @returns\n */\nfunction interaction(params: Params<CirclePackingOptions>): Params<CirclePackingOptions> {\n  const { chart, options } = params;\n\n  baseInteraction({\n    chart,\n    options: adaptorInteraction(options),\n  });\n\n  return params;\n}\n\n/**\n * 矩形树图\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<CirclePackingOptions>) {\n  return flow(\n    pattern('pointStyle'),\n    defaultOptions,\n    padding,\n    theme,\n    meta,\n    geometry,\n    axis,\n    legend,\n    tooltip,\n    interaction,\n    animation,\n    annotation()\n  )(params);\n}\n"]}