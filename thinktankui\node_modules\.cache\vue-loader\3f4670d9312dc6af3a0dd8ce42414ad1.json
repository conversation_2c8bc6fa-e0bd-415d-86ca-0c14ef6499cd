{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\report-center\\index.vue", "mtime": 1749109381350}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJSZXBvcnRMaXN0IiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIGZpbHRlclR5cGU6ICdhbGwnLA0KICAgICAgc2VhcmNoS2V5d29yZDogJycsDQogICAgICByZXBvcnRMaXN0OiBbXSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICBhY3RpdmVUYWI6ICdub3JtYWwnLCAvLyDlvZPliY3mv4DmtLvnmoTpgInpobnljaHvvJpub3JtYWwt5pmu6YCa5oql5ZGK77yMY29tcGV0aXRvci3nq57lr7nmiqXlkYoNCiAgICAgIGN1cnJlbnRWaWV3OiAncmVwb3J0JywgLy8g5b2T5YmN6KeG5Zu+77yacmVwb3J0LeaKpeWRiuWIl+ihqO+8jHRlbXBsYXRlLeaooeadv+euoeeQhg0KICAgICAgdGVtcGxhdGVUeXBlOiAnbm9ybWFsJywgLy8g5b2T5YmN5qih5p2/57G75Z6L77yabm9ybWFsLeaZrumAmuaooeadv++8jGNvbXBldGl0b3It56ue5a+55qih5p2/DQogICAgICAvLyDmqKHmnb/liJfooajmlbDmja4NCiAgICAgIHRlbXBsYXRlTGlzdDogWw0KICAgICAgICB7IG5hbWU6ICflk4HniYwt54Ot6K6u6K+d6aKYJywgY3JlYXRlVGltZTogJzIwMTktMTEtMTggMTQ6MDI6MDgnLCBvcGVyYXRpb246ICcnIH0sDQogICAgICAgIHsgbmFtZTogJ+WTgeeJjC3oiIborronLCBjcmVhdGVUaW1lOiAnMjAxOS0xMS0xOCAxNDowNjo1MicsIG9wZXJhdGlvbjogJycgfSwNCiAgICAgICAgeyBuYW1lOiAn5ZOB54mMLeernuWTgScsIGNyZWF0ZVRpbWU6ICcyMDIxLTA0LTA3IDE1OjE1OjAwJywgb3BlcmF0aW9uOiAnJyB9DQogICAgICBdLA0KICAgICAgLy8g5L6n6L655qCP5pWw5o2uDQogICAgICBzaWRlYmFyQ29sbGFwc2VkOiBmYWxzZSwNCiAgICAgIHNpZGViYXJTZWFyY2hUZXh0OiAnJywNCiAgICAgIGFjdGl2ZU1lbnVJdGVtOiAn5pa55aSqJywNCiAgICAgIG1lbnVDYXRlZ29yaWVzOiBbDQogICAgICAgIHsgbmFtZTogJ+aAu+ebkScsIGNvdW50OiAxLCBjaGlsZHJlbjogW10gfSwNCiAgICAgICAgeyBuYW1lOiAn5ZOB54mMJywgY291bnQ6IDEsIGNoaWxkcmVuOiBbXSB9LA0KICAgICAgICB7IG5hbWU6ICfmlrnlpKonLCBjb3VudDogMCwgaXNJdGVtOiB0cnVlIH0sDQogICAgICAgIHsgbmFtZTogJ+S6uueJqScsIGNvdW50OiAwLCBjaGlsZHJlbjogW10gfSwNCiAgICAgICAgeyBuYW1lOiAn5py65p6EJywgY291bnQ6IDAsIGNoaWxkcmVuOiBbXSB9LA0KICAgICAgICB7IG5hbWU6ICfkuqflk4EnLCBjb3VudDogMCwgY2hpbGRyZW46IFtdIH0sDQogICAgICAgIHsgbmFtZTogJ+S6i+S7ticsIGNvdW50OiAwLCBjaGlsZHJlbjogW10gfSwNCiAgICAgICAgeyBuYW1lOiAn6K+d6aKYJywgY291bnQ6IDAsIGNoaWxkcmVuOiBbXSB9DQogICAgICBdDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOS+p+i+ueagj+ebuOWFs+aWueazlQ0KICAgIHRvZ2dsZVNpZGViYXIoKSB7DQogICAgICB0aGlzLnNpZGViYXJDb2xsYXBzZWQgPSAhdGhpcy5zaWRlYmFyQ29sbGFwc2VkOw0KICAgIH0sDQogICAgaGFuZGxlTWVudVNlbGVjdChpbmRleCkgew0KICAgICAgdGhpcy5hY3RpdmVNZW51SXRlbSA9IGluZGV4Ow0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5YiH5o2i6I+c5Y2V6aG55ZCO55qE6YC76L6R77yM5aaC6YeN5paw6I635Y+W5pWw5o2u562JDQogICAgICB0aGlzLmZldGNoUmVwb3J0TGlzdCgpOw0KICAgIH0sDQogICAgY3JlYXRlTmV3U2NoZW1lKCkgew0KICAgICAgLy8g5paw5bu65pa55qGI55qE6YC76L6RDQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgbWVzc2FnZTogJ+aWsOW7uuaWueahiOWKn+iDveW+heWunueOsCcsDQogICAgICAgIHR5cGU6ICdpbmZvJw0KICAgICAgfSk7DQogICAgfSwNCiAgICBzZWFyY2hTaWRlYmFyKCkgew0KICAgICAgLy8g5L6n6L655qCP5pCc57Si6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5pCc57Si5YWz6ZSu6K+N77yaJywgdGhpcy5zaWRlYmFyU2VhcmNoVGV4dCk7DQogICAgICAvLyDlrp7njrDmkJzntKLpgLvovpENCiAgICB9LA0KICAgIC8vIOaooeadv+euoeeQhg0KICAgIGVkaXRSZXBvcnQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRWaWV3ID0gJ3RlbXBsYXRlJzsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5YiH5o2i5Yiw5qih5p2/566h55CGIik7DQogICAgfSwNCiAgICAvLyDlr7zlh7rmiqXlkYoNCiAgICBleHBvcnRSZXBvcnQoKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWumuaXtuS7u+WKoeeuoeeQhiIpOw0KICAgIH0sDQogICAgLy8g5Yib5bu65paw5oql5ZGKDQogICAgY3JlYXRlUmVwb3J0KCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmlrDlu7rmiqXlkYoiKTsNCiAgICB9LA0KICAgIC8vIOafpeeci+aKpeWRig0KICAgIHZpZXdSZXBvcnQocm93KSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOafpeeci+aKpeWRijogJHtyb3cubmFtZX1gKTsNCiAgICB9LA0KICAgIC8vIOe8lui+keaKpeWRiumhuQ0KICAgIGVkaXRSZXBvcnRJdGVtKHJvdykgew0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDnvJbovpHmiqXlkYo6ICR7cm93Lm5hbWV9YCk7DQogICAgfSwNCiAgICAvLyDliKDpmaTmiqXlkYoNCiAgICBkZWxldGVSZXBvcnQocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTmiqXlkYogIiR7cm93Lm5hbWV9Ij9gLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOWIoOmZpOaKpeWRijogJHtyb3cubmFtZX1gKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLlt7Llj5bmtojliKDpmaQiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSE55CG6aG156CB5Y+Y5YyWDQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSB2YWw7DQogICAgICB0aGlzLmZldGNoUmVwb3J0TGlzdCgpOw0KICAgIH0sDQogICAgLy8g6I635Y+W5oql5ZGK5YiX6KGoDQogICAgZmV0Y2hSZXBvcnRMaXN0KCkgew0KICAgICAgLy8g5a6e6ZmF5bqU55So5Lit6L+Z6YeM6ZyA6KaB6LCD55So5o6l5Y+j6I635Y+W5pWw5o2uDQogICAgICAvLyDov5nph4zmqKHmi5/nqbrmlbDmja4NCiAgICAgIHRoaXMucmVwb3J0TGlzdCA9IFtdOw0KICAgICAgdGhpcy50b3RhbCA9IDA7DQogICAgfSwNCiAgICAvLyDmn6Xor6LmjInpkq7ngrnlh7vkuovku7YNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5omn6KGM5p+l6K+i5pON5L2cIik7DQogICAgICB0aGlzLmZldGNoUmVwb3J0TGlzdCgpOw0KICAgIH0sDQogICAgLy8g6YeN572u5oyJ6ZKu54K55Ye75LqL5Lu2DQogICAgaGFuZGxlUmVzZXQoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5maWx0ZXJUeXBlID0gJ2FsbCc7DQogICAgICB0aGlzLnNlYXJjaEtleXdvcmQgPSAnJzsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi6YeN572u562b6YCJ5p2h5Lu2Iik7DQogICAgfSwNCiAgICAvLyDliIfmjaLpgInpobnljaENCiAgICBzd2l0Y2hUYWIodGFiKSB7DQogICAgICB0aGlzLmFjdGl2ZVRhYiA9IHRhYjsNCiAgICAgIGlmICh0YWIgPT09ICdub3JtYWwnKSB7DQogICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaGFuZGxlUmVzZXQoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWIm+W7uuaooeadvw0KICAgIGNyZWF0ZVRlbXBsYXRlKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLliJvlu7rmlrDmqKHmnb8iKTsNCiAgICB9LA0KICAgIC8vIOWIm+W7uuS6p+WTgeaooeadvw0KICAgIGNyZWF0ZVByb2R1Y3RUZW1wbGF0ZSgpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yib5bu65Lqn5ZOB5qih5p2/Iik7DQogICAgfSwNCiAgICAvLyDmn6XnnIvmqKHmnb8NCiAgICB2aWV3VGVtcGxhdGUocm93KSB7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOafpeeci+aooeadvzogJHtyb3cubmFtZX1gKTsNCiAgICB9LA0KICAgIC8vIOe8lui+keaooeadvw0KICAgIGVkaXRUZW1wbGF0ZShyb3cpIHsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg57yW6L6R5qih5p2/OiAke3Jvdy5uYW1lfWApOw0KICAgIH0sDQogICAgLy8g5Yig6Zmk5qih5p2/DQogICAgZGVsZXRlVGVtcGxhdGUocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTmqKHmnb8gIiR7cm93Lm5hbWV9Ij9gLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOWIoOmZpOaooeadvzogJHtyb3cubmFtZX1gKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLlt7Llj5bmtojliKDpmaQiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6L+U5Zue5oyJ6ZKu54K55Ye75LqL5Lu2DQogICAgZ29CYWNrKCkgew0KICAgICAgdGhpcy5jdXJyZW50VmlldyA9ICdyZXBvcnQnOw0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLov5Tlm57miqXlkYrliJfooagiKTsNCiAgICB9LA0KICAgIC8vIOWIh+aNouaooeadv+exu+Weiw0KICAgIHN3aXRjaFRlbXBsYXRlVHlwZSh0eXBlKSB7DQogICAgICB0aGlzLnRlbXBsYXRlVHlwZSA9IHR5cGU7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOWIh+aNouWIsCR7dHlwZSA9PT0gJ25vcm1hbCcgPyAn5pmu6YCa5qih5p2/JyA6ICfnq57lr7nmqKHmnb8nfWApOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmZldGNoUmVwb3J0TGlzdCgpOw0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/report-center", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <div class=\"report-container\">\r\n          <div class=\"report-header\">\r\n            <div class=\"title\">方案报告</div>\r\n            <div class=\"actions\">\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-edit\" @click=\"editReport\">模板管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-document\" @click=\"exportReport\">定时任务管理</el-button>\r\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"createReport\">新建报告</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 报告列表视图 -->\r\n          <div v-if=\"currentView === 'report'\">\r\n            <div class=\"filter-options\">\r\n              <div class=\"filter-row\">\r\n                <div class=\"filter-item\">\r\n                  <span>时间范围：</span>\r\n                  <el-date-picker\r\n                    v-model=\"dateRange\"\r\n                    type=\"daterange\"\r\n                    range-separator=\"-\"\r\n                    start-placeholder=\"开始日期\"\r\n                    end-placeholder=\"结束日期\"\r\n                    size=\"small\"\r\n                    style=\"width: 240px;\"\r\n                  ></el-date-picker>\r\n                </div>\r\n                <div class=\"filter-item\">\r\n                  <el-select v-model=\"filterType\" placeholder=\"全部\" size=\"small\" style=\"width: 100px;\">\r\n                    <el-option label=\"全部\" value=\"all\"></el-option>\r\n                    <el-option label=\"名称\" value=\"name\"></el-option>\r\n                    <el-option label=\"类型\" value=\"type\"></el-option>\r\n                  </el-select>\r\n                  <el-input\r\n                    v-model=\"searchKeyword\"\r\n                    placeholder=\"请输入内容\"\r\n                    size=\"small\"\r\n                    style=\"width: 200px; margin-left: 5px;\"\r\n                  ></el-input>\r\n                </div>\r\n                <div class=\"tab-container\">\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'normal' }\"\r\n                    @click=\"switchTab('normal')\"\r\n                  >\r\n                    普通报告\r\n                  </div>\r\n                  <div\r\n                    class=\"tab-item\"\r\n                    :class=\"{ 'active': activeTab === 'competitor' }\"\r\n                    @click=\"switchTab('competitor')\"\r\n                  >\r\n                    竞对报告\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"report-table\">\r\n              <el-table\r\n                :data=\"reportList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"配置名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"type\"\r\n                  label=\"配置类型\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"status\"\r\n                  label=\"状态\"\r\n                  width=\"100\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"reportStatus\"\r\n                  label=\"报告状态\"\r\n                  width=\"120\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewReport(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editReportItem(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteReport(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n\r\n              <!-- 空数据显示 -->\r\n              <div v-if=\"reportList.length === 0\" class=\"empty-data\">\r\n                <i class=\"el-icon-data-analysis empty-icon\"></i>\r\n                <p>暂无数据</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 模板管理视图 -->\r\n          <div v-if=\"currentView === 'template'\">\r\n            <div class=\"back-button\">\r\n              <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\">返回</el-button>\r\n            </div>\r\n            <div class=\"template-header\">\r\n              <div class=\"template-title\">\r\n                <i class=\"el-icon-document\"></i> 报告模板\r\n              </div>\r\n              <div class=\"template-actions\">\r\n                <div class=\"template-tab-container\">\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'normal' }\"\r\n                    @click=\"switchTemplateType('normal')\"\r\n                  >\r\n                    普通模板\r\n                  </div>\r\n                  <div\r\n                    class=\"template-tab\"\r\n                    :class=\"{ 'active': templateType === 'competitor' }\"\r\n                    @click=\"switchTemplateType('competitor')\"\r\n                  >\r\n                    竞对模板\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"create-template-btn\">\r\n              <el-button type=\"primary\" plain @click=\"createProductTemplate\">\r\n                <i class=\"el-icon-plus\"></i> 创建产品模板\r\n              </el-button>\r\n            </div>\r\n\r\n            <div class=\"template-table\">\r\n              <el-table\r\n                :data=\"templateList\"\r\n                style=\"width: 100%\"\r\n                border\r\n                stripe\r\n                :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n              >\r\n                <el-table-column\r\n                  prop=\"name\"\r\n                  label=\"模板名称\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  prop=\"createTime\"\r\n                  label=\"创建时间\"\r\n                  width=\"180\"\r\n                ></el-table-column>\r\n                <el-table-column\r\n                  label=\"操作\"\r\n                  align=\"center\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-view\"\r\n                      @click=\"viewTemplate(scope.row)\"\r\n                    >查看</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      @click=\"editTemplate(scope.row)\"\r\n                    >编辑</el-button>\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-delete\"\r\n                      class=\"delete-btn\"\r\n                      @click=\"deleteTemplate(scope.row)\"\r\n                    >删除</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"pagination-container\">\r\n            <el-pagination\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              :total=\"total\"\r\n              :current-page.sync=\"currentPage\"\r\n              :page-size=\"pageSize\"\r\n              @current-change=\"handleCurrentChange\"\r\n            ></el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"ReportList\",\r\n  data() {\r\n    return {\r\n      dateRange: [],\r\n      filterType: 'all',\r\n      searchKeyword: '',\r\n      reportList: [],\r\n      total: 0,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      activeTab: 'normal', // 当前激活的选项卡：normal-普通报告，competitor-竞对报告\r\n      currentView: 'report', // 当前视图：report-报告列表，template-模板管理\r\n      templateType: 'normal', // 当前模板类型：normal-普通模板，competitor-竞对模板\r\n      // 模板列表数据\r\n      templateList: [\r\n        { name: '品牌-热议话题', createTime: '2019-11-18 14:02:08', operation: '' },\r\n        { name: '品牌-舆论', createTime: '2019-11-18 14:06:52', operation: '' },\r\n        { name: '品牌-竞品', createTime: '2021-04-07 15:15:00', operation: '' }\r\n      ],\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [] },\r\n        { name: '品牌', count: 1, children: [] },\r\n        { name: '方太', count: 0, isItem: true },\r\n        { name: '人物', count: 0, children: [] },\r\n        { name: '机构', count: 0, children: [] },\r\n        { name: '产品', count: 0, children: [] },\r\n        { name: '事件', count: 0, children: [] },\r\n        { name: '话题', count: 0, children: [] }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.fetchReportList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 模板管理\r\n    editReport() {\r\n      this.currentView = 'template';\r\n      this.$message.success(\"切换到模板管理\");\r\n    },\r\n    // 导出报告\r\n    exportReport() {\r\n      this.$message.success(\"定时任务管理\");\r\n    },\r\n    // 创建新报告\r\n    createReport() {\r\n      this.$message.success(\"新建报告\");\r\n    },\r\n    // 查看报告\r\n    viewReport(row) {\r\n      this.$message.success(`查看报告: ${row.name}`);\r\n    },\r\n    // 编辑报告项\r\n    editReportItem(row) {\r\n      this.$message.success(`编辑报告: ${row.name}`);\r\n    },\r\n    // 删除报告\r\n    deleteReport(row) {\r\n      this.$confirm(`确认删除报告 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除报告: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      this.fetchReportList();\r\n    },\r\n    // 获取报告列表\r\n    fetchReportList() {\r\n      // 实际应用中这里需要调用接口获取数据\r\n      // 这里模拟空数据\r\n      this.reportList = [];\r\n      this.total = 0;\r\n    },\r\n    // 查询按钮点击事件\r\n    handleQuery() {\r\n      this.$message.success(\"执行查询操作\");\r\n      this.fetchReportList();\r\n    },\r\n    // 重置按钮点击事件\r\n    handleReset() {\r\n      this.dateRange = [];\r\n      this.filterType = 'all';\r\n      this.searchKeyword = '';\r\n      this.$message.success(\"重置筛选条件\");\r\n    },\r\n    // 切换选项卡\r\n    switchTab(tab) {\r\n      this.activeTab = tab;\r\n      if (tab === 'normal') {\r\n        this.handleQuery();\r\n      } else {\r\n        this.handleReset();\r\n      }\r\n    },\r\n    // 创建模板\r\n    createTemplate() {\r\n      this.$message.success(\"创建新模板\");\r\n    },\r\n    // 创建产品模板\r\n    createProductTemplate() {\r\n      this.$message.success(\"创建产品模板\");\r\n    },\r\n    // 查看模板\r\n    viewTemplate(row) {\r\n      this.$message.success(`查看模板: ${row.name}`);\r\n    },\r\n    // 编辑模板\r\n    editTemplate(row) {\r\n      this.$message.success(`编辑模板: ${row.name}`);\r\n    },\r\n    // 删除模板\r\n    deleteTemplate(row) {\r\n      this.$confirm(`确认删除模板 \"${row.name}\"?`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        this.$message.success(`删除模板: ${row.name}`);\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 返回按钮点击事件\r\n    goBack() {\r\n      this.currentView = 'report';\r\n      this.$message.success(\"返回报告列表\");\r\n    },\r\n    // 切换模板类型\r\n    switchTemplateType(type) {\r\n      this.templateType = type;\r\n      this.$message.success(`切换到${type === 'normal' ? '普通模板' : '竞对模板'}`);\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchReportList();\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 报告中心样式 */\r\n.report-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  height: 100%;\r\n  overflow-y: auto;\r\n}\r\n\r\n.report-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.report-header .title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.report-header .actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-options {\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n  margin-left: auto;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.tab-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.tab-item.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.filter-item span {\r\n  margin-right: 10px;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.report-table {\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  min-height: 300px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n}\r\n\r\n.report-table ::v-deep .el-table th {\r\n  background-color: #f5f7fa;\r\n  color: #606266;\r\n  font-weight: 500;\r\n  text-align: center;\r\n}\r\n\r\n.report-table ::v-deep .el-table td {\r\n  text-align: center;\r\n}\r\n\r\n.empty-data {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  text-align: center;\r\n}\r\n\r\n.empty-data .empty-icon {\r\n  font-size: 60px;\r\n  color: #c0c4cc;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.empty-data p {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.delete-btn {\r\n  color: #f56c6c;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 模板管理样式 */\r\n.template-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.template-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.template-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.template-tab-container {\r\n  display: flex;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  width: fit-content;\r\n}\r\n\r\n.template-tab {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  background-color: #fff;\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n  border-right: 1px solid #dcdfe6;\r\n  min-width: 100px;\r\n}\r\n\r\n.template-tab:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.template-tab.active {\r\n  background-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.template-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.create-template-btn {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.create-template-btn .el-button {\r\n  border-color: #f56c6c;\r\n  color: #f56c6c;\r\n}\r\n\r\n.create-template-btn .el-button:hover {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.back-button {\r\n  text-align: right;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.back-button .el-button {\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n  padding: 0;\r\n}\r\n\r\n.back-button .el-button:hover {\r\n  color: #66b1ff;\r\n}\r\n</style>\r\n"]}]}