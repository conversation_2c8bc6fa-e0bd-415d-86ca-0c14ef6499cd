{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/radar/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACzG,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAG7E;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA4B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA+D,OAAO,KAAtE,EAAE,SAAS,GAAoD,OAAO,UAA3D,EAAE,KAAK,GAA6C,OAAO,MAApD,EAAS,YAAY,GAAwB,OAAO,MAA/B,EAAQ,WAAW,GAAK,OAAO,KAAZ,CAAa;IAEnF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,iBAAiB;IACjB,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrC,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,KAAK,EAAE,SAAS;gBAChB,KAAK,OAAA;aACN;YACD,KAAK,EAAE,YAAY;gBACjB,CAAC,YACG,KAAK,OAAA,IACF,YAAY,EAEnB,CAAC,CAAC,YAAY;YAChB,IAAI,EAAE,WAAW;gBACf,CAAC,YACG,KAAK,OAAA,IACF,WAAW,EAElB,CAAC,CAAC,WAAW;YACf,mDAAmD;YACnD,KAAK,EAAE,SAAS;SACjB;KACF,CAAC,CAAC;IACH,aAAa;IACb,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE;QACrC,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;SACf;KACF,CAAC,CAAC;IACH,yCAAyC;IACzC,IAAM,UAAU,GAAG,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,KAAI,OAAO,CAAC,KAAK,CAAC;IACxD,IAAM,WAAW,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAEhG,IAAI,CAAC,OAAO,CAAC,CAAC;IACd,KAAK,CAAC,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC,MAAM,CAAC,CAAC;IAEb,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA4B;;IAChC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA4B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA2B,OAAO,OAAlC,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAEjD,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;QACxB,MAAM,QAAA;QACN,UAAU,YAAA;QACV,QAAQ,UAAA;KACT,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA4B;IAChC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA2B,OAAO,OAAlC,EAAE,KAAK,GAAoB,OAAO,MAA3B,EAAE,MAAM,GAAY,OAAO,OAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE1B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA4B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,MAAM,GAAuB,KAAK,OAA5B,EAAE,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAApC,sBAA4B,CAAF,CAAW;QAC3C,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YAC1B,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAClD,0BAA0B;IAC1B,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACxH,CAAC", "sourcesContent": ["import { animation, annotation, interaction, legend, scale, theme, tooltip } from '../../adaptor/common';\nimport { area, line, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { RadarOptions } from './types';\n\n/**\n * geometry 配置处理\n * @param params\n */\nfunction geometry(params: Params<RadarOptions>): Params<RadarOptions> {\n  const { chart, options } = params;\n  const { data, lineStyle, color, point: pointOptions, area: areaOptions } = options;\n\n  chart.data(data);\n\n  // 雷达图 主 geometry\n  const primary = deepAssign({}, params, {\n    options: {\n      line: {\n        style: lineStyle,\n        color,\n      },\n      point: pointOptions\n        ? {\n            color,\n            ...pointOptions,\n          }\n        : pointOptions,\n      area: areaOptions\n        ? {\n            color,\n            ...areaOptions,\n          }\n        : areaOptions,\n      // label 不传递给各个 geometry adaptor，由 label adaptor 处理\n      label: undefined,\n    },\n  });\n  // 副 Geometry\n  const second = deepAssign({}, primary, {\n    options: {\n      tooltip: false,\n    },\n  });\n  // 优先使用 point.state, 其次取主元素的 state 状态样式配置\n  const pointState = pointOptions?.state || options.state;\n  const pointParams = deepAssign({}, primary, { options: { tooltip: false, state: pointState } });\n\n  line(primary);\n  point(pointParams);\n  area(second);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<RadarOptions>): Params<RadarOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(params);\n}\n\n/**\n * coord 配置\n * @param params\n */\nfunction coord(params: Params<RadarOptions>): Params<RadarOptions> {\n  const { chart, options } = params;\n  const { radius, startAngle, endAngle } = options;\n\n  chart.coordinate('polar', {\n    radius,\n    startAngle,\n    endAngle,\n  });\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<RadarOptions>): Params<RadarOptions> {\n  const { chart, options } = params;\n  const { xField, xAxis, yField, yAxis } = options;\n\n  chart.axis(xField, xAxis);\n  chart.axis(yField, yAxis);\n\n  return params;\n}\n\n/**\n * label 配置\n * @param params\n */\nfunction label(params: Params<RadarOptions>): Params<RadarOptions> {\n  const { chart, options } = params;\n  const { label, yField } = options;\n\n  const geometry = findGeometry(chart, 'line');\n\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { fields, callback, ...cfg } = label;\n    geometry.label({\n      fields: fields || [yField],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * 雷达图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<RadarOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(geometry, meta, theme, coord, axis, legend, tooltip, label, interaction, animation, annotation())(params);\n}\n"]}