{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue?vue&type=style&index=0&id=7e98e9a4&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\search-results\\index.vue", "mtime": 1749109381351}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmRA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/search-results", "sourcesContent": ["<template>\r\n  <div class=\"search-results-container\">\r\n    <!-- 搜索区域 -->\r\n    <div class=\"search-section\">\r\n      <div class=\"search-header\">\r\n        <el-button type=\"text\" icon=\"el-icon-arrow-left\" @click=\"goBack\" class=\"back-button\">\r\n          返回\r\n        </el-button>\r\n        <div class=\"search-tabs\">\r\n          <div class=\"tab-item active\">全文检索</div>\r\n          <div class=\"tab-item\">可视化</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"search-box\">\r\n        <el-input\r\n          v-model=\"searchQuery\"\r\n          placeholder=\"请输入搜索关键词\"\r\n          class=\"search-input\"\r\n          @keyup.enter=\"handleSearch\"\r\n        >\r\n          <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handleSearch\"></el-button>\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 筛选条件区域 -->\r\n    <div class=\"filter-section\">\r\n      <!-- 时间筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">时间范围:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"time in timeOptions\"\r\n            :key=\"time.value\"\r\n            :type=\"selectedTime === time.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectTime(time.value)\"\r\n          >\r\n            {{ time.label }}\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 平台筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">平台类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"platform in platformOptions\"\r\n            :key=\"platform.value\"\r\n            :type=\"selectedPlatform === platform.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectPlatform(platform.value)\"\r\n          >\r\n            {{ platform.label }}\r\n            <span v-if=\"platform.count\" class=\"count\">({{ platform.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 情感筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">情感类型:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button\r\n            v-for=\"emotion in emotionOptions\"\r\n            :key=\"emotion.value\"\r\n            :type=\"selectedEmotion === emotion.value ? 'primary' : ''\"\r\n            size=\"small\"\r\n            @click=\"selectEmotion(emotion.value)\"\r\n          >\r\n            {{ emotion.label }}\r\n            <span v-if=\"emotion.count\" class=\"count\">({{ emotion.count }})</span>\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 其他筛选 -->\r\n      <div class=\"filter-row\">\r\n        <span class=\"filter-label\">其他筛选:</span>\r\n        <div class=\"filter-options\">\r\n          <el-button size=\"small\">作者类型</el-button>\r\n          <el-button size=\"small\">地域</el-button>\r\n          <el-button size=\"small\">影响力</el-button>\r\n          <el-button size=\"small\">传播量</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 结果统计 -->\r\n    <div class=\"result-stats\">\r\n      <span>共{{ totalResults }}条结果</span>\r\n      <div class=\"action-buttons\">\r\n        <el-button size=\"small\">导出</el-button>\r\n        <el-button type=\"primary\" size=\"small\">分析</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜索结果列表 -->\r\n    <div class=\"results-list\">\r\n      <div v-for=\"(item, index) in searchResults\" :key=\"index\" class=\"result-item\">\r\n        <div class=\"result-header\">\r\n          <h3 class=\"result-title\">{{ item.title }}</h3>\r\n          <div class=\"result-actions\">\r\n            <el-button type=\"text\" icon=\"el-icon-view\"></el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"result-meta\">\r\n          <span class=\"meta-item\">{{ item.source }}</span>\r\n          <span class=\"meta-item\">{{ item.publishTime }}</span>\r\n          <span class=\"meta-item\">{{ item.author }}</span>\r\n          <span class=\"meta-item\">{{ item.platform }}</span>\r\n          <span class=\"meta-item\">阅读量: {{ item.readCount }}</span>\r\n          <span class=\"meta-item\">{{ item.location }}</span>\r\n          <span class=\"meta-item\">{{ item.category }}</span>\r\n        </div>\r\n\r\n        <div class=\"result-content\">\r\n          {{ item.content }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"pagination-container\">\r\n      <el-pagination\r\n        background\r\n        layout=\"prev, pager, next\"\r\n        :total=\"totalResults\"\r\n        :current-page.sync=\"currentPage\"\r\n        :page-size=\"pageSize\"\r\n        @current-change=\"handlePageChange\"\r\n      ></el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"SearchResults\",\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined,\r\n      searchQuery: \"今日 万元\",\r\n      selectedTime: \"24h\",\r\n      selectedPlatform: \"all\",\r\n      selectedEmotion: \"all\",\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalResults: 10000,\r\n\r\n      timeOptions: [\r\n        { label: \"24小时\", value: \"24h\" },\r\n        { label: \"一周\", value: \"1w\" },\r\n        { label: \"半年\", value: \"6m\" },\r\n        { label: \"一年\", value: \"1y\" },\r\n        { label: \"自定义\", value: \"custom\" }\r\n      ],\r\n\r\n      platformOptions: [\r\n        { label: \"全部\", value: \"all\", count: 10540 },\r\n        { label: \"微信\", value: \"wechat\", count: 1847 },\r\n        { label: \"微博\", value: \"weibo\", count: 2008 },\r\n        { label: \"客户端\", value: \"app\", count: 1748 },\r\n        { label: \"论坛\", value: \"forum\", count: 673 }\r\n      ],\r\n\r\n      emotionOptions: [\r\n        { label: \"全部\", value: \"all\" },\r\n        { label: \"正面\", value: \"positive\" },\r\n        { label: \"负面\", value: \"negative\" },\r\n        { label: \"中性\", value: \"neutral\" }\r\n      ],\r\n\r\n      searchResults: [\r\n        {\r\n          title: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\",\r\n          source: \"新华网\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"新闻\",\r\n          content: \"从政府部门44三个个体的热点问题，到媒体1，覆盖的（含义），斗争主义口号，牛年主要的问题，共同人工智能的问题人员，如山山的问题的主要问题，新工人，打工，用友三家...\"\r\n        },\r\n        {\r\n          title: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文\",\r\n          source: \"中大论文发表\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"平台来源\",\r\n          readCount: \"无\",\r\n          location: \"无所在地\",\r\n          category: \"论文\",\r\n          content: \"中大-论文发表(2025年中大论文发表)自然指数中大-论文发表世界地位论坛-2025年的价值中国论文...\"\r\n        },\r\n        {\r\n          title: \"转发微博#中#大学生，人情世故。\",\r\n          source: \"微博\",\r\n          publishTime: \"2022-06-29 20:07:04\",\r\n          author: \"77人讨论\",\r\n          platform: \"微博\",\r\n          readCount: \"1000\",\r\n          location: \"北京\",\r\n          category: \"社交媒体\",\r\n          content: \"转发微博#中#大学生，人情世故。这是一条关于大学生人际关系的微博内容...\"\r\n        }\r\n      ]\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    // 获取URL参数中的搜索关键词\r\n    if (this.$route.query.q) {\r\n      this.searchQuery = this.$route.query.q;\r\n      this.handleSearch();\r\n    }\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch() {\r\n      this.$message.success(`搜索: ${this.searchQuery}`);\r\n      // 实际搜索逻辑\r\n    },\r\n\r\n    selectTime(value) {\r\n      this.selectedTime = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectPlatform(value) {\r\n      this.selectedPlatform = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    selectEmotion(value) {\r\n      this.selectedEmotion = value;\r\n      this.handleSearch();\r\n    },\r\n\r\n    handlePageChange(page) {\r\n      this.currentPage = page;\r\n      // 加载对应页面数据\r\n    },\r\n\r\n    goBack() {\r\n      // 返回上一页，如果没有历史记录则返回信息汇总页面\r\n      if (window.history.length > 1) {\r\n        this.$router.go(-1);\r\n      } else {\r\n        this.$router.push('/info-summary');\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.search-results-container {\r\n  padding: 20px;\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.search-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.search-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.back-button {\r\n  margin-right: 20px;\r\n  color: #409EFF;\r\n  font-size: 14px;\r\n}\r\n\r\n.back-button:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n.search-tabs {\r\n  display: flex;\r\n}\r\n\r\n.tab-item {\r\n  padding: 8px 20px;\r\n  cursor: pointer;\r\n  border-bottom: 2px solid transparent;\r\n  color: #666;\r\n}\r\n\r\n.tab-item.active {\r\n  color: #409EFF;\r\n  border-bottom-color: #409EFF;\r\n}\r\n\r\n.search-box {\r\n  max-width: 600px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n}\r\n\r\n.filter-section {\r\n  background: white;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  width: 80px;\r\n  color: #666;\r\n  font-size: 14px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 10px;\r\n}\r\n\r\n.count {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.result-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background: white;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.results-list {\r\n  background: white;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-item {\r\n  padding: 20px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.result-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  color: #333;\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  flex: 1;\r\n  margin-right: 20px;\r\n}\r\n\r\n.result-meta {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.meta-item {\r\n  white-space: nowrap;\r\n}\r\n\r\n.result-content {\r\n  color: #666;\r\n  line-height: 1.6;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  background: white;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n</style>\r\n"]}]}