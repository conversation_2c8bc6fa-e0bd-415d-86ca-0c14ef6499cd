{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\iFrame\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\iFrame\\index.vue", "mtime": 1749109381328}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgc3JjOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoZWlnaHQ6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyIsCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIHVybDogdGhpcy5zcmMKICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICB9LCAzMDApOwogICAgdmFyIHRoYXQgPSB0aGlzOwogICAgd2luZG93Lm9ucmVzaXplID0gZnVuY3Rpb24gdGVtcCgpIHsKICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiOwogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["props", "src", "type", "String", "required", "data", "height", "document", "documentElement", "clientHeight", "loading", "url", "mounted", "_this", "setTimeout", "that", "window", "onresize", "temp"], "sources": ["src/components/iFrame/index.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" :style=\"'height:' + height\">\r\n    <iframe\r\n      :src=\"src\"\r\n      frameborder=\"no\"\r\n      style=\"width: 100%; height: 100%\"\r\n      scrolling=\"auto\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      required: true\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\r\n      loading: true,\r\n      url: this.src\r\n    };\r\n  },\r\n  mounted: function () {\r\n    setTimeout(() => {\r\n      this.loading = false;\r\n    }, 300);\r\n    const that = this;\r\n    window.onresize = function temp() {\r\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\r\n    };\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;iCAWA;EACAA,KAAA;IACAC,GAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;MACAC,OAAA;MACAC,GAAA,OAAAV;IACA;EACA;EACAW,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,UAAA;MACAD,KAAA,CAAAH,OAAA;IACA;IACA,IAAAK,IAAA;IACAC,MAAA,CAAAC,QAAA,YAAAC,KAAA;MACAH,IAAA,CAAAT,MAAA,GAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;IACA;EACA;AACA", "ignoreList": []}]}