{"version": 3, "file": "circle.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/mask/circle.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAE9B,MAAM,UAAU,YAAY,CAAC,MAAM;IACjC,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAClC,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,IAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,GAAG,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;QACtC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACnC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACpC;IACD,OAAO;QACL,CAAC,GAAA;QACD,CAAC,GAAA;QACD,CAAC,GAAA;KACF,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH;IAAyB,8BAAQ;IAAjC;QAAA,qEAKC;QAJW,eAAS,GAAG,QAAQ,CAAC;;IAIjC,CAAC;IAHW,iCAAY,GAAtB;QACE,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IACH,iBAAC;AAAD,CAAC,AALD,CAAyB,QAAQ,GAKhC;AAED,eAAe,UAAU,CAAC", "sourcesContent": ["import { last } from '@antv/util';\nimport { distance } from '../util';\nimport MaskBase from './base';\n\nexport function getMaskAttrs(points) {\n  const currentPoint = last(points);\n  let r = 0;\n  let x = 0;\n  let y = 0;\n  if (points.length) {\n    const first = points[0];\n    r = distance(first, currentPoint) / 2;\n    x = (currentPoint.x + first.x) / 2;\n    y = (currentPoint.y + first.y) / 2;\n  }\n  return {\n    x,\n    y,\n    r,\n  };\n}\n\n/**\n * @ignore\n * 圆形辅助框 Action\n */\nclass CircleMask extends MaskBase {\n  protected shapeType = 'circle';\n  protected getMaskAttrs() {\n    return getMaskAttrs(this.points);\n  }\n}\n\nexport default CircleMask;\n"]}