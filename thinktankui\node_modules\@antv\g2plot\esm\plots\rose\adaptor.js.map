{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/rose/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACjH,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAGzF;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA2B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAgC,OAAO,KAAvC,EAAE,WAAW,GAAmB,OAAO,YAA1B,EAAE,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEpD,OAAO;IACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,IAAI,CAAC,QAAQ,CAAC,CACZ,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE;gBACR,KAAK,EAAE,WAAW;gBAClB,KAAK,OAAA;gBACL,KAAK,EAAE,KAAK;aACb;SACF;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA2B;IAChC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAClC,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEjD,0BAA0B;IAC1B,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;QAClB,IAAA,QAAQ,GAAqB,KAAK,SAA1B,EAAE,MAAM,GAAa,KAAK,OAAlB,EAAK,GAAG,UAAK,KAAK,EAApC,sBAA4B,CAAF,CAAW;QACnC,IAAA,MAAM,GAAK,GAAG,OAAR,CAAS;QACvB,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAExB,8CAA8C;QAC9C,gBAAgB;QAChB,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,IAAI,CAAC,EAAE;YACvC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7D,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,gBAAgB,EAA3B,CAA2B,CAAC,CAAC;YAChE,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,MAAM,CAAC;SACxC;QAED,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YAC1B,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;SAAM;QACL,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,KAAK,IAAI,EAAE,qCAAqC,CAAC,CAAC;QACvE,QAAQ,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;KACtC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA2B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAkB,OAAO,OAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAExC,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM,IAAI,WAAW,EAAE;QACtB,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;KACnC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA2B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAwC,OAAO,OAA/C,EAAE,WAAW,GAA2B,OAAO,YAAlC,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAE9D,KAAK,CAAC,UAAU,CAAC;QACf,IAAI,EAAE,OAAO;QACb,GAAG,EAAE;YACH,MAAM,QAAA;YACN,WAAW,aAAA;YACX,UAAU,YAAA;YACV,QAAQ,UAAA;SACT;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA2B;;IAC/B,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA2B;IAC/B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,iBAAiB;IACjB,IAAI,CAAC,KAAK,EAAE;QACV,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,CAAC,KAAK,EAAE;QACV,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA2B;IACjD,0BAA0B;IAC1B,IAAI,CACF,OAAO,CAAC,aAAa,CAAC,EACtB,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,SAAS,EACT,KAAK,EACL,UAAU,EAAE,EACZ,KAAK,CACN,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { filter, isArray, isObject } from '@antv/util';\nimport { animation, annotation, interaction, pattern, scale, state, theme, tooltip } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, LEVEL, log, transformLabel } from '../../utils';\nimport { RoseOptions } from './types';\n\n/**\n * geometry 配置处理\n * @param params\n */\nfunction geometry(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { chart, options } = params;\n  const { data, sectorStyle, shape, color } = options;\n\n  // 装载数据\n  chart.data(data);\n\n  flow(interval)(\n    deepAssign({}, params, {\n      options: {\n        marginRatio: 1,\n        interval: {\n          style: sectorStyle,\n          color,\n          shape: shape,\n        },\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * label 配置\n * @param params\n */\nfunction label(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { chart, options } = params;\n  const { label, xField } = options;\n  const geometry = findGeometry(chart, 'interval');\n\n  // label 为 false 不显示 label\n  if (label === false) {\n    geometry.label(false);\n  } else if (isObject(label)) {\n    const { callback, fields, ...cfg } = label;\n    const { offset } = cfg;\n    let layout = cfg.layout;\n\n    // 当 label 在 shape 外部显示时，设置 'limit-in-shape' 会\n    // 造成 label 不显示。\n    if (offset === undefined || offset >= 0) {\n      layout = layout ? (isArray(layout) ? layout : [layout]) : [];\n      cfg.layout = filter(layout, (v) => v.type !== 'limit-in-shape');\n      cfg.layout.length || delete cfg.layout;\n    }\n\n    geometry.label({\n      fields: fields || [xField],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  } else {\n    log(LEVEL.WARN, label === null, 'the label option must be an Object.');\n    geometry.label({ fields: [xField] });\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { chart, options } = params;\n  const { legend, seriesField } = options;\n\n  if (legend === false) {\n    chart.legend(false);\n  } else if (seriesField) {\n    chart.legend(seriesField, legend);\n  }\n\n  return params;\n}\n\n/**\n * coord 配置\n * @param params\n */\nfunction coordinate(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { chart, options } = params;\n  const { radius, innerRadius, startAngle, endAngle } = options;\n\n  chart.coordinate({\n    type: 'polar',\n    cfg: {\n      radius,\n      innerRadius,\n      startAngle,\n      endAngle,\n    },\n  });\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<RoseOptions>): Params<RoseOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  // 为 falsy 则是不显示轴\n  if (!xAxis) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (!yAxis) {\n    chart.axis(yField, false);\n  } else {\n    chart.axis(yField, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * 玫瑰图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<RoseOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  flow(\n    pattern('sectorStyle'),\n    geometry,\n    meta,\n    label,\n    coordinate,\n    axis,\n    legend,\n    tooltip,\n    interaction,\n    animation,\n    theme,\n    annotation(),\n    state\n  )(params);\n}\n"]}