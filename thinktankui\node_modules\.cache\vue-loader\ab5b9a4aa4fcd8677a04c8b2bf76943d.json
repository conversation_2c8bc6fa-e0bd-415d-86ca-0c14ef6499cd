{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\TagsView\\ScrollPane.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\TagsView\\ScrollPane.vue", "mtime": 1749109381332}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ScrollPane.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ScrollPane.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\r\n  <el-scrollbar ref=\"scrollContainer\" :vertical=\"false\" class=\"scroll-container\" @wheel.native.prevent=\"handleScroll\">\r\n    <slot />\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nconst tagAndTagSpacing = 4 // tagAndTagSpacing\r\n\r\nexport default {\r\n  name: 'ScrollPane',\r\n  data() {\r\n    return {\r\n      left: 0\r\n    }\r\n  },\r\n  computed: {\r\n    scrollWrapper() {\r\n      return this.$refs.scrollContainer.$refs.wrap\r\n    }\r\n  },\r\n  mounted() {\r\n    this.scrollWrapper.addEventListener('scroll', this.emitScroll, true)\r\n  },\r\n  beforeDestroy() {\r\n    this.scrollWrapper.removeEventListener('scroll', this.emitScroll)\r\n  },\r\n  methods: {\r\n    handleScroll(e) {\r\n      const eventDelta = e.wheelDelta || -e.deltaY * 40\r\n      const $scrollWrapper = this.scrollWrapper\r\n      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4\r\n    },\r\n    emitScroll() {\r\n      this.$emit('scroll')\r\n    },\r\n    moveToTarget(currentTag) {\r\n      const $container = this.$refs.scrollContainer.$el\r\n      const $containerWidth = $container.offsetWidth\r\n      const $scrollWrapper = this.scrollWrapper\r\n      const tagList = this.$parent.$refs.tag\r\n\r\n      let firstTag = null\r\n      let lastTag = null\r\n\r\n      // find first tag and last tag\r\n      if (tagList.length > 0) {\r\n        firstTag = tagList[0]\r\n        lastTag = tagList[tagList.length - 1]\r\n      }\r\n\r\n      if (firstTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = 0\r\n      } else if (lastTag === currentTag) {\r\n        $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth\r\n      } else {\r\n        // find preTag and nextTag\r\n        const currentIndex = tagList.findIndex(item => item === currentTag)\r\n        const prevTag = tagList[currentIndex - 1]\r\n        const nextTag = tagList[currentIndex + 1]\r\n\r\n        // the tag's offsetLeft after of nextTag\r\n        const afterNextTagOffsetLeft = nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing\r\n\r\n        // the tag's offsetLeft before of prevTag\r\n        const beforePrevTagOffsetLeft = prevTag.$el.offsetLeft - tagAndTagSpacing\r\n\r\n        if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {\r\n          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth\r\n        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {\r\n          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scroll-container {\r\n  white-space: nowrap;\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 100%;\r\n  ::v-deep {\r\n    .el-scrollbar__bar {\r\n      bottom: 0px;\r\n    }\r\n    .el-scrollbar__wrap {\r\n      height: 49px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}