{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/sankey/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC7D,OAAO,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACzF,OAAO,EAAE,oBAAoB,EAAE,MAAM,UAAU,CAAC;AAGhD;;;GAGG;AACH,SAAS,cAAc,CAAC,MAA6B;IAC3C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAmB,OAAO,UAAZ,EAAd,SAAS,mBAAG,EAAE,KAAA,CAAa;IAEnC,OAAO,UAAU,CACf,EAAE,EACF;QACE,OAAO,EAAE;YACP,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI,gBAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,GAAK,SAAS,QAAE;aAC5E;YACD,KAAK,EAAE;gBACL,MAAM,EAAE,IAAI,gBAAE,GAAG,EAAE,MAAM,GAAK,SAAS,QAAE;aAC1C;SACF;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAiF,OAAO,MAAxF,EAAE,SAAS,GAAsE,OAAO,UAA7E,EAAE,SAAS,GAA2D,OAAO,UAAlE,EAAE,KAAK,GAAoD,OAAO,MAA3D,EAAE,OAAO,GAA2C,OAAO,QAAlD,EAAE,SAAS,GAAgC,OAAO,UAAvC,EAAE,SAAS,GAAqB,OAAO,UAA5B,EAAE,KAAmB,OAAO,UAAZ,EAAd,SAAS,mBAAG,EAAE,KAAA,CAAa;IAEtG,4BAA4B;IAC5B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,uBAAuB;IACvB,KAAK,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEhC,qBAAqB;IACrB,aAAa;IACP,IAAA,KAAmB,oBAAoB,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,EAAzE,KAAK,WAAA,EAAE,KAAK,WAA6D,CAAC;IAElF,YAAY;IACZ,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IACzD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAErB,IAAI,CAAC;QACH,KAAK,EAAE,QAAQ;QACf,aAAa;QACb,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,WAAW,EAAE,WAAW;YACxB,SAAS,iBAAG,QAAQ,EAAE,QAAQ,GAAK,SAAS,OAAC;YAC7C,IAAI,EAAE;gBACJ,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,KAAK;aACb;YACD,OAAO,SAAA;YACP,KAAK,EAAE,SAAS;SACjB;KACF,CAAC,CAAC;IAEH,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IACzD,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAErB,OAAO,CAAC;QACN,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,WAAW,EAAE,WAAW;YACxB,OAAO,EAAE;gBACP,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;aACjB;YACD,KAAK,OAAA;YACL,OAAO,SAAA;YACP,KAAK,EAAE,SAAS;SACjB;KACF,CAAC,CAAC;IAEH,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;IAEpC,QAAQ;IACR,KAAK,CAAC,KAAK,CAAC;QACV,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;QACvE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;QACvE,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;KACrC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,MAA6B;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE9B,IAAM,UAAU,mCAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,SAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,OAAC,CAAC;IAEhF,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IAE/C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,MAA6B;IACjD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,aAAa,GAAK,OAAO,cAAZ,CAAa;IAElC,IAAM,gBAAgB,GAAG,uBAAuB,CAAC;IAEjD,IAAI,aAAa,EAAE;QACjB,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;KACrC;SAAM;QACL,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;KAC3C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,MAA6B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAsB,OAAO,aAAZ,EAAjB,YAAY,mBAAG,EAAE,KAAA,CAAa;IAEtC,IAAM,gBAAgB,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;IACjF,IAAM,gBAAgB,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC;IAEjF,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IACpD,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAEpD,gBAAgB,CAAC,OAAO,CAAC,UAAC,CAAC;QACzB,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,MAAM,MAAK,KAAK,EAAE;YACvB,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACL,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SAC3C;IACH,CAAC,CAAC,CAAC;IAEH,gBAAgB,CAAC,OAAO,CAAC,UAAC,CAAC;QACzB,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,MAAM,MAAK,KAAK,EAAE;YACvB,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACpC;aAAM;YACL,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SAC3C;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B;IACnD,0BAA0B;IAC1B,OAAO,IAAI,CACT,cAAc,EACd,QAAQ,EACR,WAAW,EACX,aAAa,EACb,SAAS,EACT,KAAK;IACL,uBAAuB;KACxB,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { uniq } from '@antv/util';\nimport { theme } from '../../adaptor/common';\nimport { edge, polygon } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findViewById, flow } from '../../utils';\nimport { addViewAnimation } from '../../utils/view';\nimport { COLOR_FIELD, EDGES_VIEW_ID, NODES_VIEW_ID, X_FIELD, Y_FIELD } from './constant';\nimport { transformToViewsData } from './helper';\nimport { SankeyOptions } from './types';\n\n/**\n * 默认配置项 处理\n * @param params\n */\nfunction defaultOptions(params: Params<SankeyOptions>): Params<SankeyOptions> {\n  const { options } = params;\n  const { rawFields = [] } = options;\n\n  return deepAssign(\n    {},\n    {\n      options: {\n        tooltip: {\n          fields: uniq(['name', 'source', 'target', 'value', 'isNode', ...rawFields]),\n        },\n        label: {\n          fields: uniq(['x', 'name', ...rawFields]),\n        },\n      },\n    },\n    params\n  );\n}\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<SankeyOptions>): Params<SankeyOptions> {\n  const { chart, options } = params;\n  const { color, nodeStyle, edgeStyle, label, tooltip, nodeState, edgeState, rawFields = [] } = options;\n\n  // 1. 组件，优先设置，因为子 view 会继承配置\n  chart.legend(false);\n  chart.tooltip(tooltip);\n  chart.axis(false);\n  // y 镜像一下，防止图形顺序和数据顺序反了\n  chart.coordinate().reflect('y');\n\n  // 2. node edge views\n  // @ts-ignore\n  const { nodes, edges } = transformToViewsData(options, chart.width, chart.height);\n\n  // edge view\n  const edgeView = chart.createView({ id: EDGES_VIEW_ID });\n  edgeView.data(edges);\n\n  edge({\n    chart: edgeView,\n    // @ts-ignore\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      seriesField: COLOR_FIELD,\n      rawFields: ['source', 'target', ...rawFields],\n      edge: {\n        color,\n        style: edgeStyle,\n        shape: 'arc',\n      },\n      tooltip,\n      state: edgeState,\n    },\n  });\n\n  const nodeView = chart.createView({ id: NODES_VIEW_ID });\n  nodeView.data(nodes);\n\n  polygon({\n    chart: nodeView,\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      seriesField: COLOR_FIELD,\n      polygon: {\n        color,\n        style: nodeStyle,\n      },\n      label,\n      tooltip,\n      state: nodeState,\n    },\n  });\n\n  chart.interaction('element-active');\n\n  // scale\n  chart.scale({\n    x: { sync: true, nice: true, min: 0, max: 1, minLimit: 0, maxLimit: 1 },\n    y: { sync: true, nice: true, min: 0, max: 1, minLimit: 0, maxLimit: 1 },\n    name: { sync: 'color', type: 'cat' },\n  });\n\n  return params;\n}\n\n/**\n * 动画\n * @param params\n */\nexport function animation(params: Params<SankeyOptions>): Params<SankeyOptions> {\n  const { chart, options } = params;\n  const { animation } = options;\n\n  const geometries = [...chart.views[0].geometries, ...chart.views[1].geometries];\n\n  addViewAnimation(chart, animation, geometries);\n\n  return params;\n}\n\n/**\n * 节点拖动\n * @param params\n */\nexport function nodeDraggable(params: Params<SankeyOptions>): Params<SankeyOptions> {\n  const { chart, options } = params;\n  const { nodeDraggable } = options;\n\n  const DRAG_INTERACTION = 'sankey-node-draggable';\n\n  if (nodeDraggable) {\n    chart.interaction(DRAG_INTERACTION);\n  } else {\n    chart.removeInteraction(DRAG_INTERACTION);\n  }\n\n  return params;\n}\n\n/**\n * Interaction 配置\n * @param params\n */\nfunction interaction(params: Params<SankeyOptions>): Params<SankeyOptions> {\n  const { chart, options } = params;\n  const { interactions = [] } = options;\n\n  const nodeInteractions = [].concat(interactions, options.nodeInteractions || []);\n  const edgeInteractions = [].concat(interactions, options.edgeInteractions || []);\n\n  const nodeView = findViewById(chart, NODES_VIEW_ID);\n  const edgeView = findViewById(chart, EDGES_VIEW_ID);\n\n  nodeInteractions.forEach((i) => {\n    if (i?.enable === false) {\n      nodeView.removeInteraction(i.type);\n    } else {\n      nodeView.interaction(i.type, i.cfg || {});\n    }\n  });\n\n  edgeInteractions.forEach((i) => {\n    if (i?.enable === false) {\n      edgeView.removeInteraction(i.type);\n    } else {\n      edgeView.interaction(i.type, i.cfg || {});\n    }\n  });\n\n  return params;\n}\n\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<SankeyOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    defaultOptions,\n    geometry,\n    interaction,\n    nodeDraggable,\n    animation,\n    theme\n    // ... 其他的 adaptor flow\n  )(params);\n}\n"]}