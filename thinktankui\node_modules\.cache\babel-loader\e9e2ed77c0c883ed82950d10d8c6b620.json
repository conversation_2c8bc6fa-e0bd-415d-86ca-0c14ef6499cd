{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\index.vue", "mtime": 1749109381333}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnZhciBfUmlnaHRQYW5lbCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL1JpZ2h0UGFuZWwiKSk7CnZhciBfY29tcG9uZW50cyA9IHJlcXVpcmUoIi4vY29tcG9uZW50cyIpOwp2YXIgX1Jlc2l6ZUhhbmRsZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vbWl4aW4vUmVzaXplSGFuZGxlciIpKTsKdmFyIF92dWV4ID0gcmVxdWlyZSgidnVleCIpOwp2YXIgX3ZhcmlhYmxlczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdMYXlvdXQnLAogIGNvbXBvbmVudHM6IHsKICAgIEFwcE1haW46IF9jb21wb25lbnRzLkFwcE1haW4sCiAgICBOYXZiYXI6IF9jb21wb25lbnRzLk5hdmJhciwKICAgIFJpZ2h0UGFuZWw6IF9SaWdodFBhbmVsLmRlZmF1bHQsCiAgICBTZXR0aW5nczogX2NvbXBvbmVudHMuU2V0dGluZ3MsCiAgICBTaWRlYmFyOiBfY29tcG9uZW50cy5TaWRlYmFyLAogICAgVGFnc1ZpZXc6IF9jb21wb25lbnRzLlRhZ3NWaWV3CiAgfSwKICBtaXhpbnM6IFtfUmVzaXplSGFuZGxlci5kZWZhdWx0XSwKICBjb21wdXRlZDogKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgKDAsIF92dWV4Lm1hcFN0YXRlKSh7CiAgICB0aGVtZTogZnVuY3Rpb24gdGhlbWUoc3RhdGUpIHsKICAgICAgcmV0dXJuIHN0YXRlLnNldHRpbmdzLnRoZW1lOwogICAgfSwKICAgIHNpZGVUaGVtZTogZnVuY3Rpb24gc2lkZVRoZW1lKHN0YXRlKSB7CiAgICAgIHJldHVybiBzdGF0ZS5zZXR0aW5ncy5zaWRlVGhlbWU7CiAgICB9LAogICAgc2lkZWJhcjogZnVuY3Rpb24gc2lkZWJhcihzdGF0ZSkgewogICAgICByZXR1cm4gc3RhdGUuYXBwLnNpZGViYXI7CiAgICB9LAogICAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2Uoc3RhdGUpIHsKICAgICAgcmV0dXJuIHN0YXRlLmFwcC5kZXZpY2U7CiAgICB9LAogICAgbmVlZFRhZ3NWaWV3OiBmdW5jdGlvbiBuZWVkVGFnc1ZpZXcoc3RhdGUpIHsKICAgICAgcmV0dXJuIHN0YXRlLnNldHRpbmdzLnRhZ3NWaWV3OwogICAgfSwKICAgIGZpeGVkSGVhZGVyOiBmdW5jdGlvbiBmaXhlZEhlYWRlcihzdGF0ZSkgewogICAgICByZXR1cm4gc3RhdGUuc2V0dGluZ3MuZml4ZWRIZWFkZXI7CiAgICB9CiAgfSkpLCB7fSwgewogICAgY2xhc3NPYmo6IGZ1bmN0aW9uIGNsYXNzT2JqKCkgewogICAgICByZXR1cm4gewogICAgICAgIGhpZGVTaWRlYmFyOiAhdGhpcy5zaWRlYmFyLm9wZW5lZCwKICAgICAgICBvcGVuU2lkZWJhcjogdGhpcy5zaWRlYmFyLm9wZW5lZCwKICAgICAgICB3aXRob3V0QW5pbWF0aW9uOiB0aGlzLnNpZGViYXIud2l0aG91dEFuaW1hdGlvbiwKICAgICAgICBtb2JpbGU6IHRoaXMuZGV2aWNlID09PSAnbW9iaWxlJwogICAgICB9OwogICAgfSwKICAgIHZhcmlhYmxlczogZnVuY3Rpb24gdmFyaWFibGVzKCkgewogICAgICByZXR1cm4gX3ZhcmlhYmxlczIuZGVmYXVsdDsKICAgIH0KICB9KSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVDbGlja091dHNpZGU6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrT3V0c2lkZSgpIHsKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9jbG9zZVNpZGVCYXInLCB7CiAgICAgICAgd2l0aG91dEFuaW1hdGlvbjogZmFsc2UKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_RightPanel", "_interopRequireDefault", "require", "_components", "_ResizeHandler", "_vuex", "_variables2", "name", "components", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "RightPanel", "Settings", "Sidebar", "TagsView", "mixins", "ResizeMixin", "computed", "_objectSpread2", "default", "mapState", "theme", "state", "settings", "sideTheme", "sidebar", "app", "device", "needTagsView", "tagsView", "fixedHeader", "classObj", "hideSidebar", "opened", "openSidebar", "withoutAnimation", "mobile", "variables", "methods", "handleClickOutside", "$store", "dispatch"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\r\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\r\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\r\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\r\n      <div :class=\"{'fixed-header':fixedHeader}\">\r\n        <navbar/>\r\n        <tags-view v-if=\"needTagsView\"/>\r\n      </div>\r\n      <app-main/>\r\n      <right-panel>\r\n        <settings/>\r\n      </right-panel>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RightPanel from '@/components/RightPanel'\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    RightPanel,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      theme: state => state.settings.theme,\r\n      sideTheme: state => state.settings.sideTheme,\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/assets/styles/mixin.scss\";\r\n  @import \"~@/assets/styles/variables.scss\";\r\n\r\n  .app-wrapper {\r\n    @include clearfix;\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    &.mobile.openSidebar {\r\n      position: fixed;\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .drawer-bg {\r\n    background: #000;\r\n    opacity: 0.3;\r\n    width: 100%;\r\n    top: 0;\r\n    height: 100%;\r\n    position: absolute;\r\n    z-index: 999;\r\n  }\r\n\r\n  .fixed-header {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 9;\r\n    width: calc(100% - #{$base-sidebar-width});\r\n    transition: width 0.28s;\r\n  }\r\n\r\n  .hideSidebar .fixed-header {\r\n    width: calc(100% - 54px);\r\n  }\r\n\r\n  .sidebarHide .fixed-header {\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile .fixed-header {\r\n    width: 100%;\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;AAkBA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA,oBAAA;IACAC,OAAA,EAAAA,mBAAA;IACAC,QAAA,EAAAA;EACA;EACAC,MAAA,GAAAC,sBAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA;IACAC,KAAA,WAAAA,MAAAC,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAF,KAAA;IAAA;IACAG,SAAA,WAAAA,UAAAF,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAC,SAAA;IAAA;IACAC,OAAA,WAAAA,QAAAH,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAD,OAAA;IAAA;IACAE,MAAA,WAAAA,OAAAL,KAAA;MAAA,OAAAA,KAAA,CAAAI,GAAA,CAAAC,MAAA;IAAA;IACAC,YAAA,WAAAA,aAAAN,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAM,QAAA;IAAA;IACAC,WAAA,WAAAA,YAAAR,KAAA;MAAA,OAAAA,KAAA,CAAAC,QAAA,CAAAO,WAAA;IAAA;EACA;IACAC,QAAA,WAAAA,SAAA;MACA;QACAC,WAAA,QAAAP,OAAA,CAAAQ,MAAA;QACAC,WAAA,OAAAT,OAAA,CAAAQ,MAAA;QACAE,gBAAA,OAAAV,OAAA,CAAAU,gBAAA;QACAC,MAAA,OAAAT,MAAA;MACA;IACA;IACAU,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;EAAA,EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,MAAA,CAAAC,QAAA;QAAAN,gBAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}