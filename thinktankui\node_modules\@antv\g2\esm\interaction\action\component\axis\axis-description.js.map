{"version": 3, "file": "axis-description.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/component/axis/axis-description.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,MAAM,MAAM,YAAY,CAAC;AAChC,OAAO,EAAE,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAGjD,IAAM,wBAAwB,GAAG,0BAA0B,CAAC;AAE5D;IAA8B,mCAAM;IAApC;;IAyEA,CAAC;IAtEQ,8BAAI,GAAX;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACrB,IAAA,IAAI,GAAK,mBAAmB,CAAC,OAAO,CAAC,KAAjC,CAAkC;QACxC,IAAA,KAAiD,IAAI,CAAC,GAAG,CAAC,KAAK,EAA7D,WAAW,iBAAA,EAAE,IAAI,UAAA,EAAE,uBAAuB,6BAAmB,CAAC;QAChE,IAAA,KAAW,OAAO,CAAC,KAAK,EAAtB,CAAC,OAAA,EAAE,CAAC,OAAkB,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAClB,KAAK,EAAE,IAAI,IAAI,EAAE;YACjB,aAAa,EAAE;gBACb,OAAO,mCACS,iBAAiB,CAAC,eAAe,uBAAY,uBAAuB,0CAClE,iBAAiB,CAAC,WAAW,8DAClC,WAAW,qDAGvB,CAAC;YACJ,CAAC;YACD,CAAC,GAAA;YACD,CAAC,GAAA;SACF,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAEM,iCAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC;QAChB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IAEM,8BAAI,GAAX;QACE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;IAEM,uCAAa,GAApB;;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAM,MAAM,GAAG;YACb,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACrB,GAAG,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;SACzD,CAAC;QACF,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC;YAC9B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU;YACnC,MAAM,QAAA;YACN,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,wBAAwB;YACrC,SAAS,eACJ,OAAO,CACR,EAAE;gBAEA,qCAAqC;gBACrC,GAAC,iBAAiB,CAAC,eAAe,IAAG;oBACnC,WAAW,EAAE,KAAK;oBAClB,OAAO,EAAE,MAAM;oBACf,aAAa,EAAE,MAAM;oBACrB,WAAW,EAAE,MAAM;oBACnB,KAAK,EAAE,oBAAoB;iBAC5B;gBACD,GAAC,iBAAiB,CAAC,WAAW,IAAG;oBAC/B,YAAY,EAAE,WAAW;oBACzB,eAAe,EAAE,KAAK;iBACvB;oBAEJ,CACF;SACF,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACH,sBAAC;AAAD,CAAC,AAzED,CAA8B,MAAM,GAyEnC;AACD,eAAe,eAAe,CAAC", "sourcesContent": ["import { TOOLTIP_CSS_CONST } from '@antv/component';\nimport { deepMix } from '@antv/util';\nimport { HtmlTooltip } from '../../../../dependents';\nimport Action from '../../base';\nimport { getDelegationObject } from '../../util';\nimport cx from 'classnames';\n\nconst AXIS_DESCRIPTION_TOOLTIP = 'aixs-description-tooltip';\n\nclass AxisDescription extends Action {\n  private tooltip;\n\n  public show() {\n    const context = this.context;\n    const { axis } = getDelegationObject(context);\n    const { description, text, descriptionTooltipStyle } = axis.cfg.title;\n    const { x, y } = context.event;\n    if (!this.tooltip) {\n      this.renderTooltip();\n    }\n    this.tooltip.update({\n      title: text || '',\n      customContent: () => {\n        return `\n          <div class=\"${TOOLTIP_CSS_CONST.CONTAINER_CLASS}\" style={${descriptionTooltipStyle}}>\n            <div class=\"${TOOLTIP_CSS_CONST.TITLE_CLASS}\">\n              字段说明：${description}\n            </div>\n          </div>\n        `;\n      },\n      x,\n      y,\n    });\n    this.tooltip.show();\n  }\n\n  public destroy() {\n    super.destroy();\n    this.tooltip && this.tooltip.destroy();\n  }\n\n  public hide() {\n    this.tooltip && this.tooltip.hide();\n  }\n\n  public renderTooltip() {\n    const view = this.context.view;\n    const canvas = view.canvas;\n    const region = {\n      start: { x: 0, y: 0 },\n      end: { x: canvas.get('width'), y: canvas.get('height') },\n    };\n    const tooltip = new HtmlTooltip({\n      parent: canvas.get('el').parentNode,\n      region,\n      visible: false,\n      containerId: AXIS_DESCRIPTION_TOOLTIP,\n      domStyles: {\n        ...deepMix(\n          {},\n          {\n            // 超长的时候，tooltip tip 最大宽度为 50%，然后可以换行\n            [TOOLTIP_CSS_CONST.CONTAINER_CLASS]: {\n              'max-width': '50%',\n              padding: '10px',\n              'line-height': '15px',\n              'font-size': '12px',\n              color: 'rgba(0, 0, 0, .65)',\n            },\n            [TOOLTIP_CSS_CONST.TITLE_CLASS]: {\n              'word-break': 'break-all',\n              'margin-bottom': '3px',\n            },\n          }\n        ),\n      },\n    });\n    tooltip.init();\n    tooltip.setCapture(false);\n    this.tooltip = tooltip;\n  }\n}\nexport default AxisDescription;\n"]}