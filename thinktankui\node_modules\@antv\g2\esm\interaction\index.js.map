{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/interaction/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAGlE,OAAO,kBAAwC,MAAM,uBAAuB,CAAC;AAG7E,IAAM,YAAY,GAAgB,EAAE,CAAC;AAErC;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,IAAY;IACzC,OAAO,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACvC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CAAC,IAAY,EAAE,WAAsD;IACtG,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC;AAC9C,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAAY,EAAE,IAAU,EAAE,GAAiB;IAC3E,IAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IACzC,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,IAAI,CAAC;KACb;IACD,IAAI,aAAa,CAAC,WAAW,CAAC,EAAE;QAC9B,2CAA2C;QAC3C,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,CAAqB,CAAC;QAC/D,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC5C;SAAM;QACL,IAAM,GAAG,GAAG,WAAqC,CAAC;QAClD,OAAO,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KAC3B;AACH,CAAC;AAED,OAAO,EAAE,OAAO,IAAI,WAAW,EAAE,MAAM,eAAe,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC", "sourcesContent": ["import { clone, isPlainObject, lowerCase, mix } from '@antv/util';\nimport { View } from '../chart';\nimport { LooseObject } from '../interface';\nimport GrammarInteraction, { InteractionSteps } from './grammar-interaction';\nimport { InteractionConstructor } from './interaction';\n\nconst Interactions: LooseObject = {};\n\n/**\n * 根据交互行为名字获取对应的交互类\n * @param name 交互名字\n * @returns 交互类\n */\nexport function getInteraction(name: string): InteractionSteps | InteractionConstructor {\n  return Interactions[lowerCase(name)];\n}\n\n/**\n * 注册交互行为\n * @param name 交互行为名字\n * @param interaction 交互类\n */\nexport function registerInteraction(name: string, interaction: InteractionSteps | InteractionConstructor) {\n  Interactions[lowerCase(name)] = interaction;\n}\n\n/**\n * 创建交互实例\n * @param name 交互名\n * @param view 交互应用的 View 实例\n * @param cfg 交互行为配置\n */\nexport function createInteraction(name: string, view: View, cfg?: LooseObject) {\n  const interaciton = getInteraction(name);\n  if (!interaciton) {\n    return null;\n  }\n  if (isPlainObject(interaciton)) {\n    // 如果不 clone 则会多个 interaction 实例共享 step 的定义\n    const steps = mix(clone(interaciton), cfg) as InteractionSteps;\n    return new GrammarInteraction(view, steps);\n  } else {\n    const cls = interaciton as InteractionConstructor;\n    return new cls(view, cfg);\n  }\n}\n\nexport { default as Interaction } from './interaction';\nexport { Action, registerAction, getActionClass } from './action';\n"]}