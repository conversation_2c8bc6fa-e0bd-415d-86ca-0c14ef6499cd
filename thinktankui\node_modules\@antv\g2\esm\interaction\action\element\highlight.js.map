{"version": 3, "file": "highlight.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/highlight.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAGlC,OAAO,EAAE,WAAW,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,WAAW,MAAM,SAAS,CAAC;AAElC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,MAAM,CAAC,IAAM,eAAe,GAAG,aAAa,CAAC,QAAQ,CAAC;AACtD,MAAM,CAAC,IAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AAGlD;;;GAGG;AACH;IAA+B,oCAAW;IAA1C;QAAA,qEAyEC;QAxEW,eAAS,GAAW,aAAa,CAAC;;IAwE9C,CAAC;IAtEC,sBAAsB;IACZ,iDAAsB,GAAhC,UAAiC,QAAmB,EAAE,KAAa,EAAE,IAAc,EAAE,MAAe;QAApG,iBAGC;QAFC,IAAM,QAAQ,GAAG,UAAC,EAAE,IAAK,OAAA,KAAI,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAhC,CAAgC,CAAC;QAC1D,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB;IACR,8CAAmB,GAA7B,UAA8B,EAAW,EAAE,QAAkB;QAC3D,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE;YAChB,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAChC,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SAClC;aAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YACtC,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;SACpC;IACH,CAAC;IAES,yCAAc,GAAxB,UAAyB,QAAmB,EAAE,QAAkB,EAAE,MAAe;QAAjF,iBAkCC;QAjCC,IAAI,MAAM,EAAE;YACV,6CAA6C;YAC7C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAE;gBAChB,KAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,sCAAsC;YACtC,IAAM,cAAc,GAAG,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;YAC5E,IAAI,WAAS,GAAG,IAAI,CAAC;YACrB,qCAAqC;YACrC,IAAI,CAAC,cAAc,EAAE,UAAC,EAAE;gBACtB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACjB,WAAS,GAAG,KAAK,CAAC;oBAClB,OAAO,KAAK,CAAC;iBACd;YACH,CAAC,CAAC,CAAC;YACH,IAAI,WAAS,EAAE;gBACb,iCAAiC;gBACjC,IAAI,CAAC,KAAK,EAAE,CAAC;aACd;iBAAM;gBACL,mDAAmD;gBACnD,kBAAkB;gBAClB,IAAI,CAAC,QAAQ,EAAE,UAAC,EAAE;oBAChB,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE;wBAChB,IAAI,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;4BAC9B,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;yBACnC;wBACD,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;qBACpC;gBACH,CAAC,CAAC,CAAC;aACJ;SACF;IACH,CAAC;IAED,sBAAsB;IACZ,0CAAe,GAAzB,UAA0B,OAAgB,EAAE,MAAe;QACzD,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAC,EAAE,IAAK,OAAA,OAAO,KAAK,EAAE,EAAd,CAAc,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAEM,oCAAS,GAAhB;QACE,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,6BAA6B;IACtB,gCAAK,GAAZ;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,cAAc,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IACH,uBAAC;AAAD,CAAC,AAzED,CAA+B,WAAW,GAyEzC;AAED,eAAe,gBAAgB,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { ListItem } from '../../../dependents';\nimport Element from '../../../geometry/element/';\nimport { getElements, getElementsByState } from '../util';\nimport { clearHighlight } from './highlight-util';\nimport StateAction from './state';\n\nimport { ELEMENT_STATE } from '../../../constant';\n\nexport const STATUS_UNACTIVE = ELEMENT_STATE.INACTIVE;\nexport const STATUS_ACTIVE = ELEMENT_STATE.ACTIVE;\nexport type Callback = (el) => boolean;\n\n/**\n * @ignore\n * highlight，指定图形高亮，其他图形变暗\n */\nclass ElementHighlight extends StateAction {\n  protected stateName: string = STATUS_ACTIVE;\n\n  // 多个元素设置、取消 highlight\n  protected setElementsStateByItem(elements: Element[], field: string, item: ListItem, enable: boolean) {\n    const callback = (el) => this.isMathItem(el, field, item);\n    this.setHighlightBy(elements, callback, enable);\n  }\n\n  // 设置元素的 highlight\n  protected setElementHighlight(el: Element, callback: Callback) {\n    if (callback(el)) {\n      if (el.hasState(STATUS_UNACTIVE)) {\n        el.setState(STATUS_UNACTIVE, false);\n      }\n      el.setState(STATUS_ACTIVE, true);\n    } else if (!el.hasState(STATUS_ACTIVE)) {\n      el.setState(STATUS_UNACTIVE, true);\n    }\n  }\n\n  protected setHighlightBy(elements: Element[], callback: Callback, enable: boolean) {\n    if (enable) {\n      // 如果是设置 highlight ，则将匹配的 element 设置成 active，\n      // 其他如果不是 active，则设置成 unactive\n      each(elements, (el) => {\n        this.setElementHighlight(el, callback);\n      });\n    } else {\n      // 如果取消 highlight，则要检测是否全部取消 highlight\n      const activeElements = getElementsByState(this.context.view, STATUS_ACTIVE);\n      let allCancel = true;\n      // 检测所有 activeElements 都要取消 highlight\n      each(activeElements, (el) => {\n        if (!callback(el)) {\n          allCancel = false;\n          return false;\n        }\n      });\n      if (allCancel) {\n        // 都要取消，则取消所有的 active，unactive 状态\n        this.clear();\n      } else {\n        // 如果不是都要取消 highlight, 则设置匹配的 element 的状态为 unactive\n        // 其他 element 状态不变\n        each(elements, (el) => {\n          if (callback(el)) {\n            if (el.hasState(STATUS_ACTIVE)) {\n              el.setState(STATUS_ACTIVE, false);\n            }\n            el.setState(STATUS_UNACTIVE, true);\n          }\n        });\n      }\n    }\n  }\n\n  // 单个元素设置和取消 highlight\n  protected setElementState(element: Element, enable: boolean) {\n    const view = this.context.view;\n    const elements = getElements(view);\n    this.setHighlightBy(elements, (el) => element === el, enable);\n  }\n\n  public highlight() {\n    this.setState();\n  }\n\n  // 清理掉所有的 active， unactive 状态\n  public clear() {\n    const view = this.context.view;\n    clearHighlight(view);\n  }\n}\n\nexport default ElementHighlight;\n"]}