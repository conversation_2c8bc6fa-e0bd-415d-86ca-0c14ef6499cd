{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RuoYi\\Doc\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RuoYi\\Doc\\index.vue", "mtime": 1749109381327}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSdW9ZaURvYycsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVybDogJ2h0dHA6Ly9kb2MucnVveWkudmlwL3J1b3lpLXZ1ZScNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnb3RvKCkgew0KICAgICAgd2luZG93Lm9wZW4odGhpcy51cmwpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Doc", "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"question\" @click=\"goto\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiDoc',\r\n  data() {\r\n    return {\r\n      url: 'http://doc.ruoyi.vip/ruoyi-vue'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>"]}]}