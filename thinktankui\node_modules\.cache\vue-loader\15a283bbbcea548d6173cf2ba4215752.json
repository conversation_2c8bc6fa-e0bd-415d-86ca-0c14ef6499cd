{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=template&id=6494804b&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1749109381331}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749109532675}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}