{"version": 3, "file": "highlight-by-color.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/highlight-by-color.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AACvD,OAAO,SAAS,MAAM,aAAa,CAAC;AAEpC;;;GAGG;AACH;IAA6B,kCAAS;IAAtC;;IAeA,CAAC;IAdW,0CAAiB,GAA3B,UAA4B,OAAgB,EAAE,MAAe;QAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;SACR;QACD,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,IAAM,KAAK,GAAG,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACpD,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,IAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,EAAE;YAC3C,OAAO,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QACpD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAC,EAAE,IAAK,OAAA,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAA9B,CAA8B,EAAE,MAAM,CAAC,CAAC;IAChF,CAAC;IACH,qBAAC;AAAD,CAAC,AAfD,CAA6B,SAAS,GAerC;AAED,eAAe,cAAc,CAAC", "sourcesContent": ["import Element from '../../../geometry/element/';\nimport { getElements, getElementValue } from '../util';\nimport Highlight from './highlight';\n\n/**\n * Highlight color\n * @ignore\n */\nclass HighlightColor extends Highlight {\n  protected setStateByElement(element: Element, enable: boolean) {\n    const view = this.context.view;\n    const colorAttr = element.geometry.getAttribute('color');\n    if (!colorAttr) {\n      return;\n    }\n    const scale = view.getScaleByField(colorAttr.getFields()[0]);\n    const value = getElementValue(element, scale.field);\n    const elements = getElements(view);\n    const highlightElements = elements.filter((el) => {\n      return getElementValue(el, scale.field) === value;\n    });\n    this.setHighlightBy(elements, (el) => highlightElements.includes(el), enable);\n  }\n}\n\nexport default HighlightColor;\n"]}