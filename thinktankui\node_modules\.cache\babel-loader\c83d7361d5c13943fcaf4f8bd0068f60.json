{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\clipboard.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\modules\\clipboard.js", "mtime": 1749109532426}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CmV4cG9ydHMubWF0Y2hBdHRyaWJ1dG9yID0gbWF0Y2hBdHRyaWJ1dG9yOwpleHBvcnRzLm1hdGNoQmxvdCA9IG1hdGNoQmxvdDsKZXhwb3J0cy5tYXRjaE5ld2xpbmUgPSBtYXRjaE5ld2xpbmU7CmV4cG9ydHMubWF0Y2hUZXh0ID0gbWF0Y2hUZXh0OwpleHBvcnRzLnRyYXZlcnNlID0gdHJhdmVyc2U7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIuanMiKSk7CnZhciBfc2xpY2VkVG9BcnJheTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9zbGljZWRUb0FycmF5LmpzIikpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NsYXNzQ2FsbENoZWNrLmpzIikpOwp2YXIgX2NyZWF0ZUNsYXNzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NhbGxTdXBlcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jYWxsU3VwZXIuanMiKSk7CnZhciBfaW5oZXJpdHMyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW5oZXJpdHMuanMiKSk7CnZhciBfZGVmaW5lUHJvcGVydHkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZGVmaW5lUHJvcGVydHkuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZnJvbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnJlZHVjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc2xpY2UuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5lbnRyaWVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuaXRlcmF0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcuc3BsaXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zdGFydHMtd2l0aC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnRyaW0uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLndlYWstbWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLnJlZHVjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyIpOwp2YXIgX3BhcmNobWVudCA9IHJlcXVpcmUoInBhcmNobWVudCIpOwp2YXIgX3F1aWxsRGVsdGEgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoInF1aWxsLWRlbHRhIikpOwp2YXIgX2Jsb2NrID0gcmVxdWlyZSgiLi4vYmxvdHMvYmxvY2suanMiKTsKdmFyIF9sb2dnZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2NvcmUvbG9nZ2VyLmpzIikpOwp2YXIgX21vZHVsZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29yZS9tb2R1bGUuanMiKSk7CnZhciBfcXVpbGwgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2NvcmUvcXVpbGwuanMiKSk7CnZhciBfYWxpZ24gPSByZXF1aXJlKCIuLi9mb3JtYXRzL2FsaWduLmpzIik7CnZhciBfYmFja2dyb3VuZCA9IHJlcXVpcmUoIi4uL2Zvcm1hdHMvYmFja2dyb3VuZC5qcyIpOwp2YXIgX2NvZGUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2Zvcm1hdHMvY29kZS5qcyIpKTsKdmFyIF9jb2xvciA9IHJlcXVpcmUoIi4uL2Zvcm1hdHMvY29sb3IuanMiKTsKdmFyIF9kaXJlY3Rpb24gPSByZXF1aXJlKCIuLi9mb3JtYXRzL2RpcmVjdGlvbi5qcyIpOwp2YXIgX2ZvbnQgPSByZXF1aXJlKCIuLi9mb3JtYXRzL2ZvbnQuanMiKTsKdmFyIF9zaXplID0gcmVxdWlyZSgiLi4vZm9ybWF0cy9zaXplLmpzIik7CnZhciBfa2V5Ym9hcmQgPSByZXF1aXJlKCIuL2tleWJvYXJkLmpzIik7CnZhciBfaW5kZXggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vbm9ybWFsaXplRXh0ZXJuYWxIVE1ML2luZGV4LmpzIikpOwp2YXIgZGVidWcgPSAoMCwgX2xvZ2dlci5kZWZhdWx0KSgncXVpbGw6Y2xpcGJvYXJkJyk7CnZhciBDTElQQk9BUkRfQ09ORklHID0gW1tOb2RlLlRFWFRfTk9ERSwgbWF0Y2hUZXh0XSwgW05vZGUuVEVYVF9OT0RFLCBtYXRjaE5ld2xpbmVdLCBbJ2JyJywgbWF0Y2hCcmVha10sIFtOb2RlLkVMRU1FTlRfTk9ERSwgbWF0Y2hOZXdsaW5lXSwgW05vZGUuRUxFTUVOVF9OT0RFLCBtYXRjaEJsb3RdLCBbTm9kZS5FTEVNRU5UX05PREUsIG1hdGNoQXR0cmlidXRvcl0sIFtOb2RlLkVMRU1FTlRfTk9ERSwgbWF0Y2hTdHlsZXNdLCBbJ2xpJywgbWF0Y2hJbmRlbnRdLCBbJ29sLCB1bCcsIG1hdGNoTGlzdF0sIFsncHJlJywgbWF0Y2hDb2RlQmxvY2tdLCBbJ3RyJywgbWF0Y2hUYWJsZV0sIFsnYicsIGNyZWF0ZU1hdGNoQWxpYXMoJ2JvbGQnKV0sIFsnaScsIGNyZWF0ZU1hdGNoQWxpYXMoJ2l0YWxpYycpXSwgWydzdHJpa2UnLCBjcmVhdGVNYXRjaEFsaWFzKCdzdHJpa2UnKV0sIFsnc3R5bGUnLCBtYXRjaElnbm9yZV1dOwp2YXIgQVRUUklCVVRFX0FUVFJJQlVUT1JTID0gW19hbGlnbi5BbGlnbkF0dHJpYnV0ZSwgX2RpcmVjdGlvbi5EaXJlY3Rpb25BdHRyaWJ1dGVdLnJlZHVjZShmdW5jdGlvbiAobWVtbywgYXR0cikgewogIG1lbW9bYXR0ci5rZXlOYW1lXSA9IGF0dHI7CiAgcmV0dXJuIG1lbW87Cn0sIHt9KTsKdmFyIFNUWUxFX0FUVFJJQlVUT1JTID0gW19hbGlnbi5BbGlnblN0eWxlLCBfYmFja2dyb3VuZC5CYWNrZ3JvdW5kU3R5bGUsIF9jb2xvci5Db2xvclN0eWxlLCBfZGlyZWN0aW9uLkRpcmVjdGlvblN0eWxlLCBfZm9udC5Gb250U3R5bGUsIF9zaXplLlNpemVTdHlsZV0ucmVkdWNlKGZ1bmN0aW9uIChtZW1vLCBhdHRyKSB7CiAgbWVtb1thdHRyLmtleU5hbWVdID0gYXR0cjsKICByZXR1cm4gbWVtbzsKfSwge30pOwp2YXIgQ2xpcGJvYXJkID0gZXhwb3J0cy5kZWZhdWx0ID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfTW9kdWxlKSB7CiAgZnVuY3Rpb24gQ2xpcGJvYXJkKHF1aWxsLCBvcHRpb25zKSB7CiAgICB2YXIgX3RoaXMkb3B0aW9ucyRtYXRjaGVyOwogICAgdmFyIF90aGlzOwogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgQ2xpcGJvYXJkKTsKICAgIF90aGlzID0gKDAsIF9jYWxsU3VwZXIyLmRlZmF1bHQpKHRoaXMsIENsaXBib2FyZCwgW3F1aWxsLCBvcHRpb25zXSk7CiAgICBfdGhpcy5xdWlsbC5yb290LmFkZEV2ZW50TGlzdGVuZXIoJ2NvcHknLCBmdW5jdGlvbiAoZSkgewogICAgICByZXR1cm4gX3RoaXMub25DYXB0dXJlQ29weShlLCBmYWxzZSk7CiAgICB9KTsKICAgIF90aGlzLnF1aWxsLnJvb3QuYWRkRXZlbnRMaXN0ZW5lcignY3V0JywgZnVuY3Rpb24gKGUpIHsKICAgICAgcmV0dXJuIF90aGlzLm9uQ2FwdHVyZUNvcHkoZSwgdHJ1ZSk7CiAgICB9KTsKICAgIF90aGlzLnF1aWxsLnJvb3QuYWRkRXZlbnRMaXN0ZW5lcigncGFzdGUnLCBfdGhpcy5vbkNhcHR1cmVQYXN0ZS5iaW5kKF90aGlzKSk7CiAgICBfdGhpcy5tYXRjaGVycyA9IFtdOwogICAgQ0xJUEJPQVJEX0NPTkZJRy5jb25jYXQoKF90aGlzJG9wdGlvbnMkbWF0Y2hlciA9IF90aGlzLm9wdGlvbnMubWF0Y2hlcnMpICE9PSBudWxsICYmIF90aGlzJG9wdGlvbnMkbWF0Y2hlciAhPT0gdm9pZCAwID8gX3RoaXMkb3B0aW9ucyRtYXRjaGVyIDogW10pLmZvckVhY2goZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgdmFyIF9yZWY2ID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfcmVmLCAyKSwKICAgICAgICBzZWxlY3RvciA9IF9yZWY2WzBdLAogICAgICAgIG1hdGNoZXIgPSBfcmVmNlsxXTsKICAgICAgX3RoaXMuYWRkTWF0Y2hlcihzZWxlY3RvciwgbWF0Y2hlcik7CiAgICB9KTsKICAgIHJldHVybiBfdGhpczsKICB9CiAgKDAsIF9pbmhlcml0czIuZGVmYXVsdCkoQ2xpcGJvYXJkLCBfTW9kdWxlKTsKICByZXR1cm4gKDAsIF9jcmVhdGVDbGFzczIuZGVmYXVsdCkoQ2xpcGJvYXJkLCBbewogICAga2V5OiAiYWRkTWF0Y2hlciIsCiAgICB2YWx1ZTogZnVuY3Rpb24gYWRkTWF0Y2hlcihzZWxlY3RvciwgbWF0Y2hlcikgewogICAgICB0aGlzLm1hdGNoZXJzLnB1c2goW3NlbGVjdG9yLCBtYXRjaGVyXSk7CiAgICB9CiAgfSwgewogICAga2V5OiAiY29udmVydCIsCiAgICB2YWx1ZTogZnVuY3Rpb24gY29udmVydChfcmVmMikgewogICAgICB2YXIgaHRtbCA9IF9yZWYyLmh0bWwsCiAgICAgICAgdGV4dCA9IF9yZWYyLnRleHQ7CiAgICAgIHZhciBmb3JtYXRzID0gYXJndW1lbnRzLmxlbmd0aCA+IDEgJiYgYXJndW1lbnRzWzFdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMV0gOiB7fTsKICAgICAgaWYgKGZvcm1hdHNbX2NvZGUuZGVmYXVsdC5ibG90TmFtZV0pIHsKICAgICAgICByZXR1cm4gbmV3IF9xdWlsbERlbHRhLmRlZmF1bHQoKS5pbnNlcnQodGV4dCB8fCAnJywgKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoe30sIF9jb2RlLmRlZmF1bHQuYmxvdE5hbWUsIGZvcm1hdHNbX2NvZGUuZGVmYXVsdC5ibG90TmFtZV0pKTsKICAgICAgfQogICAgICBpZiAoIWh0bWwpIHsKICAgICAgICByZXR1cm4gbmV3IF9xdWlsbERlbHRhLmRlZmF1bHQoKS5pbnNlcnQodGV4dCB8fCAnJywgZm9ybWF0cyk7CiAgICAgIH0KICAgICAgdmFyIGRlbHRhID0gdGhpcy5jb252ZXJ0SFRNTChodG1sKTsKICAgICAgLy8gUmVtb3ZlIHRyYWlsaW5nIG5ld2xpbmUKICAgICAgaWYgKGRlbHRhRW5kc1dpdGgoZGVsdGEsICdcbicpICYmIChkZWx0YS5vcHNbZGVsdGEub3BzLmxlbmd0aCAtIDFdLmF0dHJpYnV0ZXMgPT0gbnVsbCB8fCBmb3JtYXRzLnRhYmxlKSkgewogICAgICAgIHJldHVybiBkZWx0YS5jb21wb3NlKG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCkucmV0YWluKGRlbHRhLmxlbmd0aCgpIC0gMSkuZGVsZXRlKDEpKTsKICAgICAgfQogICAgICByZXR1cm4gZGVsdGE7CiAgICB9CiAgfSwgewogICAga2V5OiAibm9ybWFsaXplSFRNTCIsCiAgICB2YWx1ZTogZnVuY3Rpb24gbm9ybWFsaXplSFRNTChkb2MpIHsKICAgICAgKDAsIF9pbmRleC5kZWZhdWx0KShkb2MpOwogICAgfQogIH0sIHsKICAgIGtleTogImNvbnZlcnRIVE1MIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBjb252ZXJ0SFRNTChodG1sKSB7CiAgICAgIHZhciBkb2MgPSBuZXcgRE9NUGFyc2VyKCkucGFyc2VGcm9tU3RyaW5nKGh0bWwsICd0ZXh0L2h0bWwnKTsKICAgICAgdGhpcy5ub3JtYWxpemVIVE1MKGRvYyk7CiAgICAgIHZhciBjb250YWluZXIgPSBkb2MuYm9keTsKICAgICAgdmFyIG5vZGVNYXRjaGVzID0gbmV3IFdlYWtNYXAoKTsKICAgICAgdmFyIF90aGlzJHByZXBhcmVNYXRjaGluZyA9IHRoaXMucHJlcGFyZU1hdGNoaW5nKGNvbnRhaW5lciwgbm9kZU1hdGNoZXMpLAogICAgICAgIF90aGlzJHByZXBhcmVNYXRjaGluZzIgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF90aGlzJHByZXBhcmVNYXRjaGluZywgMiksCiAgICAgICAgZWxlbWVudE1hdGNoZXJzID0gX3RoaXMkcHJlcGFyZU1hdGNoaW5nMlswXSwKICAgICAgICB0ZXh0TWF0Y2hlcnMgPSBfdGhpcyRwcmVwYXJlTWF0Y2hpbmcyWzFdOwogICAgICByZXR1cm4gdHJhdmVyc2UodGhpcy5xdWlsbC5zY3JvbGwsIGNvbnRhaW5lciwgZWxlbWVudE1hdGNoZXJzLCB0ZXh0TWF0Y2hlcnMsIG5vZGVNYXRjaGVzKTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJkYW5nZXJvdXNseVBhc3RlSFRNTCIsCiAgICB2YWx1ZTogZnVuY3Rpb24gZGFuZ2Vyb3VzbHlQYXN0ZUhUTUwoaW5kZXgsIGh0bWwpIHsKICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAyICYmIGFyZ3VtZW50c1syXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzJdIDogX3F1aWxsLmRlZmF1bHQuc291cmNlcy5BUEk7CiAgICAgIGlmICh0eXBlb2YgaW5kZXggPT09ICdzdHJpbmcnKSB7CiAgICAgICAgdmFyIGRlbHRhID0gdGhpcy5jb252ZXJ0KHsKICAgICAgICAgIGh0bWw6IGluZGV4LAogICAgICAgICAgdGV4dDogJycKICAgICAgICB9KTsKICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yCiAgICAgICAgdGhpcy5xdWlsbC5zZXRDb250ZW50cyhkZWx0YSwgaHRtbCk7CiAgICAgICAgdGhpcy5xdWlsbC5zZXRTZWxlY3Rpb24oMCwgX3F1aWxsLmRlZmF1bHQuc291cmNlcy5TSUxFTlQpOwogICAgICB9IGVsc2UgewogICAgICAgIHZhciBwYXN0ZSA9IHRoaXMuY29udmVydCh7CiAgICAgICAgICBodG1sOiBodG1sLAogICAgICAgICAgdGV4dDogJycKICAgICAgICB9KTsKICAgICAgICB0aGlzLnF1aWxsLnVwZGF0ZUNvbnRlbnRzKG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCkucmV0YWluKGluZGV4KS5jb25jYXQocGFzdGUpLCBzb3VyY2UpOwogICAgICAgIHRoaXMucXVpbGwuc2V0U2VsZWN0aW9uKGluZGV4ICsgcGFzdGUubGVuZ3RoKCksIF9xdWlsbC5kZWZhdWx0LnNvdXJjZXMuU0lMRU5UKTsKICAgICAgfQogICAgfQogIH0sIHsKICAgIGtleTogIm9uQ2FwdHVyZUNvcHkiLAogICAgdmFsdWU6IGZ1bmN0aW9uIG9uQ2FwdHVyZUNvcHkoZSkgewogICAgICB2YXIgX2UkY2xpcGJvYXJkRGF0YSwgX2UkY2xpcGJvYXJkRGF0YTI7CiAgICAgIHZhciBpc0N1dCA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogZmFsc2U7CiAgICAgIGlmIChlLmRlZmF1bHRQcmV2ZW50ZWQpIHJldHVybjsKICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpOwogICAgICB2YXIgX3RoaXMkcXVpbGwkc2VsZWN0aW9uID0gdGhpcy5xdWlsbC5zZWxlY3Rpb24uZ2V0UmFuZ2UoKSwKICAgICAgICBfdGhpcyRxdWlsbCRzZWxlY3Rpb24yID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfdGhpcyRxdWlsbCRzZWxlY3Rpb24sIDEpLAogICAgICAgIHJhbmdlID0gX3RoaXMkcXVpbGwkc2VsZWN0aW9uMlswXTsKICAgICAgaWYgKHJhbmdlID09IG51bGwpIHJldHVybjsKICAgICAgdmFyIF90aGlzJG9uQ29weSA9IHRoaXMub25Db3B5KHJhbmdlLCBpc0N1dCksCiAgICAgICAgaHRtbCA9IF90aGlzJG9uQ29weS5odG1sLAogICAgICAgIHRleHQgPSBfdGhpcyRvbkNvcHkudGV4dDsKICAgICAgKF9lJGNsaXBib2FyZERhdGEgPSBlLmNsaXBib2FyZERhdGEpID09PSBudWxsIHx8IF9lJGNsaXBib2FyZERhdGEgPT09IHZvaWQgMCB8fCBfZSRjbGlwYm9hcmREYXRhLnNldERhdGEoJ3RleHQvcGxhaW4nLCB0ZXh0KTsKICAgICAgKF9lJGNsaXBib2FyZERhdGEyID0gZS5jbGlwYm9hcmREYXRhKSA9PT0gbnVsbCB8fCBfZSRjbGlwYm9hcmREYXRhMiA9PT0gdm9pZCAwIHx8IF9lJGNsaXBib2FyZERhdGEyLnNldERhdGEoJ3RleHQvaHRtbCcsIGh0bWwpOwogICAgICBpZiAoaXNDdXQpIHsKICAgICAgICAoMCwgX2tleWJvYXJkLmRlbGV0ZVJhbmdlKSh7CiAgICAgICAgICByYW5nZTogcmFuZ2UsCiAgICAgICAgICBxdWlsbDogdGhpcy5xdWlsbAogICAgICAgIH0pOwogICAgICB9CiAgICB9CgogICAgLyoKICAgICAqIGh0dHBzOi8vd3d3LmlhbmEub3JnL2Fzc2lnbm1lbnRzL21lZGlhLXR5cGVzL3RleHQvdXJpLWxpc3QKICAgICAqLwogIH0sIHsKICAgIGtleTogIm5vcm1hbGl6ZVVSSUxpc3QiLAogICAgdmFsdWU6IGZ1bmN0aW9uIG5vcm1hbGl6ZVVSSUxpc3QodXJsTGlzdCkgewogICAgICByZXR1cm4gdXJsTGlzdC5zcGxpdCgvXHI/XG4vKQogICAgICAvLyBJZ25vcmUgYWxsIGNvbW1lbnRzCiAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKHVybCkgewogICAgICAgIHJldHVybiB1cmxbMF0gIT09ICcjJzsKICAgICAgfSkuam9pbignXG4nKTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJvbkNhcHR1cmVQYXN0ZSIsCiAgICB2YWx1ZTogZnVuY3Rpb24gb25DYXB0dXJlUGFzdGUoZSkgewogICAgICB2YXIgX2UkY2xpcGJvYXJkRGF0YTMsIF9lJGNsaXBib2FyZERhdGE0LCBfZSRjbGlwYm9hcmREYXRhNjsKICAgICAgaWYgKGUuZGVmYXVsdFByZXZlbnRlZCB8fCAhdGhpcy5xdWlsbC5pc0VuYWJsZWQoKSkgcmV0dXJuOwogICAgICBlLnByZXZlbnREZWZhdWx0KCk7CiAgICAgIHZhciByYW5nZSA9IHRoaXMucXVpbGwuZ2V0U2VsZWN0aW9uKHRydWUpOwogICAgICBpZiAocmFuZ2UgPT0gbnVsbCkgcmV0dXJuOwogICAgICB2YXIgaHRtbCA9IChfZSRjbGlwYm9hcmREYXRhMyA9IGUuY2xpcGJvYXJkRGF0YSkgPT09IG51bGwgfHwgX2UkY2xpcGJvYXJkRGF0YTMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lJGNsaXBib2FyZERhdGEzLmdldERhdGEoJ3RleHQvaHRtbCcpOwogICAgICB2YXIgdGV4dCA9IChfZSRjbGlwYm9hcmREYXRhNCA9IGUuY2xpcGJvYXJkRGF0YSkgPT09IG51bGwgfHwgX2UkY2xpcGJvYXJkRGF0YTQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9lJGNsaXBib2FyZERhdGE0LmdldERhdGEoJ3RleHQvcGxhaW4nKTsKICAgICAgaWYgKCFodG1sICYmICF0ZXh0KSB7CiAgICAgICAgdmFyIF9lJGNsaXBib2FyZERhdGE1OwogICAgICAgIHZhciB1cmxMaXN0ID0gKF9lJGNsaXBib2FyZERhdGE1ID0gZS5jbGlwYm9hcmREYXRhKSA9PT0gbnVsbCB8fCBfZSRjbGlwYm9hcmREYXRhNSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2UkY2xpcGJvYXJkRGF0YTUuZ2V0RGF0YSgndGV4dC91cmktbGlzdCcpOwogICAgICAgIGlmICh1cmxMaXN0KSB7CiAgICAgICAgICB0ZXh0ID0gdGhpcy5ub3JtYWxpemVVUklMaXN0KHVybExpc3QpOwogICAgICAgIH0KICAgICAgfQogICAgICB2YXIgZmlsZXMgPSBBcnJheS5mcm9tKCgoX2UkY2xpcGJvYXJkRGF0YTYgPSBlLmNsaXBib2FyZERhdGEpID09PSBudWxsIHx8IF9lJGNsaXBib2FyZERhdGE2ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZSRjbGlwYm9hcmREYXRhNi5maWxlcykgfHwgW10pOwogICAgICBpZiAoIWh0bWwgJiYgZmlsZXMubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMucXVpbGwudXBsb2FkZXIudXBsb2FkKHJhbmdlLCBmaWxlcyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmIChodG1sICYmIGZpbGVzLmxlbmd0aCA+IDApIHsKICAgICAgICB2YXIgX2RvYyRib2R5JGZpcnN0RWxlbWVuOwogICAgICAgIHZhciBkb2MgPSBuZXcgRE9NUGFyc2VyKCkucGFyc2VGcm9tU3RyaW5nKGh0bWwsICd0ZXh0L2h0bWwnKTsKICAgICAgICBpZiAoZG9jLmJvZHkuY2hpbGRFbGVtZW50Q291bnQgPT09IDEgJiYgKChfZG9jJGJvZHkkZmlyc3RFbGVtZW4gPSBkb2MuYm9keS5maXJzdEVsZW1lbnRDaGlsZCkgPT09IG51bGwgfHwgX2RvYyRib2R5JGZpcnN0RWxlbWVuID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZG9jJGJvZHkkZmlyc3RFbGVtZW4udGFnTmFtZSkgPT09ICdJTUcnKSB7CiAgICAgICAgICB0aGlzLnF1aWxsLnVwbG9hZGVyLnVwbG9hZChyYW5nZSwgZmlsZXMpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLm9uUGFzdGUocmFuZ2UsIHsKICAgICAgICBodG1sOiBodG1sLAogICAgICAgIHRleHQ6IHRleHQKICAgICAgfSk7CiAgICB9CiAgfSwgewogICAga2V5OiAib25Db3B5IiwKICAgIHZhbHVlOiBmdW5jdGlvbiBvbkNvcHkocmFuZ2UpIHsKICAgICAgdmFyIHRleHQgPSB0aGlzLnF1aWxsLmdldFRleHQocmFuZ2UpOwogICAgICB2YXIgaHRtbCA9IHRoaXMucXVpbGwuZ2V0U2VtYW50aWNIVE1MKHJhbmdlKTsKICAgICAgcmV0dXJuIHsKICAgICAgICBodG1sOiBodG1sLAogICAgICAgIHRleHQ6IHRleHQKICAgICAgfTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJvblBhc3RlIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBvblBhc3RlKHJhbmdlLCBfcmVmMykgewogICAgICB2YXIgdGV4dCA9IF9yZWYzLnRleHQsCiAgICAgICAgaHRtbCA9IF9yZWYzLmh0bWw7CiAgICAgIHZhciBmb3JtYXRzID0gdGhpcy5xdWlsbC5nZXRGb3JtYXQocmFuZ2UuaW5kZXgpOwogICAgICB2YXIgcGFzdGVkRGVsdGEgPSB0aGlzLmNvbnZlcnQoewogICAgICAgIHRleHQ6IHRleHQsCiAgICAgICAgaHRtbDogaHRtbAogICAgICB9LCBmb3JtYXRzKTsKICAgICAgZGVidWcubG9nKCdvblBhc3RlJywgcGFzdGVkRGVsdGEsIHsKICAgICAgICB0ZXh0OiB0ZXh0LAogICAgICAgIGh0bWw6IGh0bWwKICAgICAgfSk7CiAgICAgIHZhciBkZWx0YSA9IG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCkucmV0YWluKHJhbmdlLmluZGV4KS5kZWxldGUocmFuZ2UubGVuZ3RoKS5jb25jYXQocGFzdGVkRGVsdGEpOwogICAgICB0aGlzLnF1aWxsLnVwZGF0ZUNvbnRlbnRzKGRlbHRhLCBfcXVpbGwuZGVmYXVsdC5zb3VyY2VzLlVTRVIpOwogICAgICAvLyByYW5nZS5sZW5ndGggY29udHJpYnV0ZXMgdG8gZGVsdGEubGVuZ3RoKCkKICAgICAgdGhpcy5xdWlsbC5zZXRTZWxlY3Rpb24oZGVsdGEubGVuZ3RoKCkgLSByYW5nZS5sZW5ndGgsIF9xdWlsbC5kZWZhdWx0LnNvdXJjZXMuU0lMRU5UKTsKICAgICAgdGhpcy5xdWlsbC5zY3JvbGxTZWxlY3Rpb25JbnRvVmlldygpOwogICAgfQogIH0sIHsKICAgIGtleTogInByZXBhcmVNYXRjaGluZyIsCiAgICB2YWx1ZTogZnVuY3Rpb24gcHJlcGFyZU1hdGNoaW5nKGNvbnRhaW5lciwgbm9kZU1hdGNoZXMpIHsKICAgICAgdmFyIGVsZW1lbnRNYXRjaGVycyA9IFtdOwogICAgICB2YXIgdGV4dE1hdGNoZXJzID0gW107CiAgICAgIHRoaXMubWF0Y2hlcnMuZm9yRWFjaChmdW5jdGlvbiAocGFpcikgewogICAgICAgIHZhciBfcGFpciA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkocGFpciwgMiksCiAgICAgICAgICBzZWxlY3RvciA9IF9wYWlyWzBdLAogICAgICAgICAgbWF0Y2hlciA9IF9wYWlyWzFdOwogICAgICAgIHN3aXRjaCAoc2VsZWN0b3IpIHsKICAgICAgICAgIGNhc2UgTm9kZS5URVhUX05PREU6CiAgICAgICAgICAgIHRleHRNYXRjaGVycy5wdXNoKG1hdGNoZXIpOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgTm9kZS5FTEVNRU5UX05PREU6CiAgICAgICAgICAgIGVsZW1lbnRNYXRjaGVycy5wdXNoKG1hdGNoZXIpOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgIEFycmF5LmZyb20oY29udGFpbmVyLnF1ZXJ5U2VsZWN0b3JBbGwoc2VsZWN0b3IpKS5mb3JFYWNoKGZ1bmN0aW9uIChub2RlKSB7CiAgICAgICAgICAgICAgaWYgKG5vZGVNYXRjaGVzLmhhcyhub2RlKSkgewogICAgICAgICAgICAgICAgdmFyIG1hdGNoZXMgPSBub2RlTWF0Y2hlcy5nZXQobm9kZSk7CiAgICAgICAgICAgICAgICBtYXRjaGVzID09PSBudWxsIHx8IG1hdGNoZXMgPT09IHZvaWQgMCB8fCBtYXRjaGVzLnB1c2gobWF0Y2hlcik7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIG5vZGVNYXRjaGVzLnNldChub2RlLCBbbWF0Y2hlcl0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBbZWxlbWVudE1hdGNoZXJzLCB0ZXh0TWF0Y2hlcnNdOwogICAgfQogIH1dKTsKfShfbW9kdWxlLmRlZmF1bHQpOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShDbGlwYm9hcmQsICJERUZBVUxUUyIsIHsKICBtYXRjaGVyczogW10KfSk7CmZ1bmN0aW9uIGFwcGx5Rm9ybWF0KGRlbHRhLCBmb3JtYXQsIHZhbHVlLCBzY3JvbGwpIHsKICBpZiAoIXNjcm9sbC5xdWVyeShmb3JtYXQpKSB7CiAgICByZXR1cm4gZGVsdGE7CiAgfQogIHJldHVybiBkZWx0YS5yZWR1Y2UoZnVuY3Rpb24gKG5ld0RlbHRhLCBvcCkgewogICAgaWYgKCFvcC5pbnNlcnQpIHJldHVybiBuZXdEZWx0YTsKICAgIGlmIChvcC5hdHRyaWJ1dGVzICYmIG9wLmF0dHJpYnV0ZXNbZm9ybWF0XSkgewogICAgICByZXR1cm4gbmV3RGVsdGEucHVzaChvcCk7CiAgICB9CiAgICB2YXIgZm9ybWF0cyA9IHZhbHVlID8gKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoe30sIGZvcm1hdCwgdmFsdWUpIDoge307CiAgICByZXR1cm4gbmV3RGVsdGEuaW5zZXJ0KG9wLmluc2VydCwgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgZm9ybWF0cyksIG9wLmF0dHJpYnV0ZXMpKTsKICB9LCBuZXcgX3F1aWxsRGVsdGEuZGVmYXVsdCgpKTsKfQpmdW5jdGlvbiBkZWx0YUVuZHNXaXRoKGRlbHRhLCB0ZXh0KSB7CiAgdmFyIGVuZFRleHQgPSAnJzsKICBmb3IgKHZhciBpID0gZGVsdGEub3BzLmxlbmd0aCAtIDE7IGkgPj0gMCAmJiBlbmRUZXh0Lmxlbmd0aCA8IHRleHQubGVuZ3RoOyAtLWkgLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1wbHVzcGx1cwogICkgewogICAgdmFyIG9wID0gZGVsdGEub3BzW2ldOwogICAgaWYgKHR5cGVvZiBvcC5pbnNlcnQgIT09ICdzdHJpbmcnKSBicmVhazsKICAgIGVuZFRleHQgPSBvcC5pbnNlcnQgKyBlbmRUZXh0OwogIH0KICByZXR1cm4gZW5kVGV4dC5zbGljZSgtMSAqIHRleHQubGVuZ3RoKSA9PT0gdGV4dDsKfQpmdW5jdGlvbiBpc0xpbmUobm9kZSwgc2Nyb2xsKSB7CiAgaWYgKCEobm9kZSBpbnN0YW5jZW9mIEVsZW1lbnQpKSByZXR1cm4gZmFsc2U7CiAgdmFyIG1hdGNoID0gc2Nyb2xsLnF1ZXJ5KG5vZGUpOwogIC8vIEB0cy1leHBlY3QtZXJyb3IKICBpZiAobWF0Y2ggJiYgbWF0Y2gucHJvdG90eXBlIGluc3RhbmNlb2YgX3BhcmNobWVudC5FbWJlZEJsb3QpIHJldHVybiBmYWxzZTsKICByZXR1cm4gWydhZGRyZXNzJywgJ2FydGljbGUnLCAnYmxvY2txdW90ZScsICdjYW52YXMnLCAnZGQnLCAnZGl2JywgJ2RsJywgJ2R0JywgJ2ZpZWxkc2V0JywgJ2ZpZ2NhcHRpb24nLCAnZmlndXJlJywgJ2Zvb3RlcicsICdmb3JtJywgJ2gxJywgJ2gyJywgJ2gzJywgJ2g0JywgJ2g1JywgJ2g2JywgJ2hlYWRlcicsICdpZnJhbWUnLCAnbGknLCAnbWFpbicsICduYXYnLCAnb2wnLCAnb3V0cHV0JywgJ3AnLCAncHJlJywgJ3NlY3Rpb24nLCAndGFibGUnLCAndGQnLCAndHInLCAndWwnLCAndmlkZW8nXS5pbmNsdWRlcyhub2RlLnRhZ05hbWUudG9Mb3dlckNhc2UoKSk7Cn0KZnVuY3Rpb24gaXNCZXR3ZWVuSW5saW5lRWxlbWVudHMobm9kZSwgc2Nyb2xsKSB7CiAgcmV0dXJuIG5vZGUucHJldmlvdXNFbGVtZW50U2libGluZyAmJiBub2RlLm5leHRFbGVtZW50U2libGluZyAmJiAhaXNMaW5lKG5vZGUucHJldmlvdXNFbGVtZW50U2libGluZywgc2Nyb2xsKSAmJiAhaXNMaW5lKG5vZGUubmV4dEVsZW1lbnRTaWJsaW5nLCBzY3JvbGwpOwp9CnZhciBwcmVOb2RlcyA9IG5ldyBXZWFrTWFwKCk7CmZ1bmN0aW9uIGlzUHJlKG5vZGUpIHsKICBpZiAobm9kZSA9PSBudWxsKSByZXR1cm4gZmFsc2U7CiAgaWYgKCFwcmVOb2Rlcy5oYXMobm9kZSkpIHsKICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgIGlmIChub2RlLnRhZ05hbWUgPT09ICdQUkUnKSB7CiAgICAgIHByZU5vZGVzLnNldChub2RlLCB0cnVlKTsKICAgIH0gZWxzZSB7CiAgICAgIHByZU5vZGVzLnNldChub2RlLCBpc1ByZShub2RlLnBhcmVudE5vZGUpKTsKICAgIH0KICB9CiAgcmV0dXJuIHByZU5vZGVzLmdldChub2RlKTsKfQpmdW5jdGlvbiB0cmF2ZXJzZShzY3JvbGwsIG5vZGUsIGVsZW1lbnRNYXRjaGVycywgdGV4dE1hdGNoZXJzLCBub2RlTWF0Y2hlcykgewogIC8vIFBvc3Qtb3JkZXIKICBpZiAobm9kZS5ub2RlVHlwZSA9PT0gbm9kZS5URVhUX05PREUpIHsKICAgIHJldHVybiB0ZXh0TWF0Y2hlcnMucmVkdWNlKGZ1bmN0aW9uIChkZWx0YSwgbWF0Y2hlcikgewogICAgICByZXR1cm4gbWF0Y2hlcihub2RlLCBkZWx0YSwgc2Nyb2xsKTsKICAgIH0sIG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCkpOwogIH0KICBpZiAobm9kZS5ub2RlVHlwZSA9PT0gbm9kZS5FTEVNRU5UX05PREUpIHsKICAgIHJldHVybiBBcnJheS5mcm9tKG5vZGUuY2hpbGROb2RlcyB8fCBbXSkucmVkdWNlKGZ1bmN0aW9uIChkZWx0YSwgY2hpbGROb2RlKSB7CiAgICAgIHZhciBjaGlsZHJlbkRlbHRhID0gdHJhdmVyc2Uoc2Nyb2xsLCBjaGlsZE5vZGUsIGVsZW1lbnRNYXRjaGVycywgdGV4dE1hdGNoZXJzLCBub2RlTWF0Y2hlcyk7CiAgICAgIGlmIChjaGlsZE5vZGUubm9kZVR5cGUgPT09IG5vZGUuRUxFTUVOVF9OT0RFKSB7CiAgICAgICAgY2hpbGRyZW5EZWx0YSA9IGVsZW1lbnRNYXRjaGVycy5yZWR1Y2UoZnVuY3Rpb24gKHJlZHVjZWREZWx0YSwgbWF0Y2hlcikgewogICAgICAgICAgcmV0dXJuIG1hdGNoZXIoY2hpbGROb2RlLCByZWR1Y2VkRGVsdGEsIHNjcm9sbCk7CiAgICAgICAgfSwgY2hpbGRyZW5EZWx0YSk7CiAgICAgICAgY2hpbGRyZW5EZWx0YSA9IChub2RlTWF0Y2hlcy5nZXQoY2hpbGROb2RlKSB8fCBbXSkucmVkdWNlKGZ1bmN0aW9uIChyZWR1Y2VkRGVsdGEsIG1hdGNoZXIpIHsKICAgICAgICAgIHJldHVybiBtYXRjaGVyKGNoaWxkTm9kZSwgcmVkdWNlZERlbHRhLCBzY3JvbGwpOwogICAgICAgIH0sIGNoaWxkcmVuRGVsdGEpOwogICAgICB9CiAgICAgIHJldHVybiBkZWx0YS5jb25jYXQoY2hpbGRyZW5EZWx0YSk7CiAgICB9LCBuZXcgX3F1aWxsRGVsdGEuZGVmYXVsdCgpKTsKICB9CiAgcmV0dXJuIG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCk7Cn0KZnVuY3Rpb24gY3JlYXRlTWF0Y2hBbGlhcyhmb3JtYXQpIHsKICByZXR1cm4gZnVuY3Rpb24gKF9ub2RlLCBkZWx0YSwgc2Nyb2xsKSB7CiAgICByZXR1cm4gYXBwbHlGb3JtYXQoZGVsdGEsIGZvcm1hdCwgdHJ1ZSwgc2Nyb2xsKTsKICB9Owp9CmZ1bmN0aW9uIG1hdGNoQXR0cmlidXRvcihub2RlLCBkZWx0YSwgc2Nyb2xsKSB7CiAgdmFyIGF0dHJpYnV0ZXMgPSBfcGFyY2htZW50LkF0dHJpYnV0b3Iua2V5cyhub2RlKTsKICB2YXIgY2xhc3NlcyA9IF9wYXJjaG1lbnQuQ2xhc3NBdHRyaWJ1dG9yLmtleXMobm9kZSk7CiAgdmFyIHN0eWxlcyA9IF9wYXJjaG1lbnQuU3R5bGVBdHRyaWJ1dG9yLmtleXMobm9kZSk7CiAgdmFyIGZvcm1hdHMgPSB7fTsKICBhdHRyaWJ1dGVzLmNvbmNhdChjbGFzc2VzKS5jb25jYXQoc3R5bGVzKS5mb3JFYWNoKGZ1bmN0aW9uIChuYW1lKSB7CiAgICB2YXIgYXR0ciA9IHNjcm9sbC5xdWVyeShuYW1lLCBfcGFyY2htZW50LlNjb3BlLkFUVFJJQlVURSk7CiAgICBpZiAoYXR0ciAhPSBudWxsKSB7CiAgICAgIGZvcm1hdHNbYXR0ci5hdHRyTmFtZV0gPSBhdHRyLnZhbHVlKG5vZGUpOwogICAgICBpZiAoZm9ybWF0c1thdHRyLmF0dHJOYW1lXSkgcmV0dXJuOwogICAgfQogICAgYXR0ciA9IEFUVFJJQlVURV9BVFRSSUJVVE9SU1tuYW1lXTsKICAgIGlmIChhdHRyICE9IG51bGwgJiYgKGF0dHIuYXR0ck5hbWUgPT09IG5hbWUgfHwgYXR0ci5rZXlOYW1lID09PSBuYW1lKSkgewogICAgICBmb3JtYXRzW2F0dHIuYXR0ck5hbWVdID0gYXR0ci52YWx1ZShub2RlKSB8fCB1bmRlZmluZWQ7CiAgICB9CiAgICBhdHRyID0gU1RZTEVfQVRUUklCVVRPUlNbbmFtZV07CiAgICBpZiAoYXR0ciAhPSBudWxsICYmIChhdHRyLmF0dHJOYW1lID09PSBuYW1lIHx8IGF0dHIua2V5TmFtZSA9PT0gbmFtZSkpIHsKICAgICAgYXR0ciA9IFNUWUxFX0FUVFJJQlVUT1JTW25hbWVdOwogICAgICBmb3JtYXRzW2F0dHIuYXR0ck5hbWVdID0gYXR0ci52YWx1ZShub2RlKSB8fCB1bmRlZmluZWQ7CiAgICB9CiAgfSk7CiAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKGZvcm1hdHMpLnJlZHVjZShmdW5jdGlvbiAobmV3RGVsdGEsIF9yZWY0KSB7CiAgICB2YXIgX3JlZjggPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWY0LCAyKSwKICAgICAgbmFtZSA9IF9yZWY4WzBdLAogICAgICB2YWx1ZSA9IF9yZWY4WzFdOwogICAgcmV0dXJuIGFwcGx5Rm9ybWF0KG5ld0RlbHRhLCBuYW1lLCB2YWx1ZSwgc2Nyb2xsKTsKICB9LCBkZWx0YSk7Cn0KZnVuY3Rpb24gbWF0Y2hCbG90KG5vZGUsIGRlbHRhLCBzY3JvbGwpIHsKICB2YXIgbWF0Y2ggPSBzY3JvbGwucXVlcnkobm9kZSk7CiAgaWYgKG1hdGNoID09IG51bGwpIHJldHVybiBkZWx0YTsKICAvLyBAdHMtZXhwZWN0LWVycm9yCiAgaWYgKG1hdGNoLnByb3RvdHlwZSBpbnN0YW5jZW9mIF9wYXJjaG1lbnQuRW1iZWRCbG90KSB7CiAgICB2YXIgZW1iZWQgPSB7fTsKICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgIHZhciB2YWx1ZSA9IG1hdGNoLnZhbHVlKG5vZGUpOwogICAgaWYgKHZhbHVlICE9IG51bGwpIHsKICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcgogICAgICBlbWJlZFttYXRjaC5ibG90TmFtZV0gPSB2YWx1ZTsKICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcgogICAgICByZXR1cm4gbmV3IF9xdWlsbERlbHRhLmRlZmF1bHQoKS5pbnNlcnQoZW1iZWQsIG1hdGNoLmZvcm1hdHMobm9kZSwgc2Nyb2xsKSk7CiAgICB9CiAgfSBlbHNlIHsKICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgIGlmIChtYXRjaC5wcm90b3R5cGUgaW5zdGFuY2VvZiBfcGFyY2htZW50LkJsb2NrQmxvdCAmJiAhZGVsdGFFbmRzV2l0aChkZWx0YSwgJ1xuJykpIHsKICAgICAgZGVsdGEuaW5zZXJ0KCdcbicpOwogICAgfQogICAgaWYgKCdibG90TmFtZScgaW4gbWF0Y2ggJiYgJ2Zvcm1hdHMnIGluIG1hdGNoICYmIHR5cGVvZiBtYXRjaC5mb3JtYXRzID09PSAnZnVuY3Rpb24nKSB7CiAgICAgIHJldHVybiBhcHBseUZvcm1hdChkZWx0YSwgbWF0Y2guYmxvdE5hbWUsIG1hdGNoLmZvcm1hdHMobm9kZSwgc2Nyb2xsKSwgc2Nyb2xsKTsKICAgIH0KICB9CiAgcmV0dXJuIGRlbHRhOwp9CmZ1bmN0aW9uIG1hdGNoQnJlYWsobm9kZSwgZGVsdGEpIHsKICBpZiAoIWRlbHRhRW5kc1dpdGgoZGVsdGEsICdcbicpKSB7CiAgICBkZWx0YS5pbnNlcnQoJ1xuJyk7CiAgfQogIHJldHVybiBkZWx0YTsKfQpmdW5jdGlvbiBtYXRjaENvZGVCbG9jayhub2RlLCBkZWx0YSwgc2Nyb2xsKSB7CiAgdmFyIG1hdGNoID0gc2Nyb2xsLnF1ZXJ5KCdjb2RlLWJsb2NrJyk7CiAgdmFyIGxhbmd1YWdlID0gbWF0Y2ggJiYgJ2Zvcm1hdHMnIGluIG1hdGNoICYmIHR5cGVvZiBtYXRjaC5mb3JtYXRzID09PSAnZnVuY3Rpb24nID8gbWF0Y2guZm9ybWF0cyhub2RlLCBzY3JvbGwpIDogdHJ1ZTsKICByZXR1cm4gYXBwbHlGb3JtYXQoZGVsdGEsICdjb2RlLWJsb2NrJywgbGFuZ3VhZ2UsIHNjcm9sbCk7Cn0KZnVuY3Rpb24gbWF0Y2hJZ25vcmUoKSB7CiAgcmV0dXJuIG5ldyBfcXVpbGxEZWx0YS5kZWZhdWx0KCk7Cn0KZnVuY3Rpb24gbWF0Y2hJbmRlbnQobm9kZSwgZGVsdGEsIHNjcm9sbCkgewogIHZhciBtYXRjaCA9IHNjcm9sbC5xdWVyeShub2RlKTsKICBpZiAobWF0Y2ggPT0gbnVsbCB8fAogIC8vIEB0cy1leHBlY3QtZXJyb3IKICBtYXRjaC5ibG90TmFtZSAhPT0gJ2xpc3QnIHx8ICFkZWx0YUVuZHNXaXRoKGRlbHRhLCAnXG4nKSkgewogICAgcmV0dXJuIGRlbHRhOwogIH0KICB2YXIgaW5kZW50ID0gLTE7CiAgdmFyIHBhcmVudCA9IG5vZGUucGFyZW50Tm9kZTsKICB3aGlsZSAocGFyZW50ICE9IG51bGwpIHsKICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgIGlmIChbJ09MJywgJ1VMJ10uaW5jbHVkZXMocGFyZW50LnRhZ05hbWUpKSB7CiAgICAgIGluZGVudCArPSAxOwogICAgfQogICAgcGFyZW50ID0gcGFyZW50LnBhcmVudE5vZGU7CiAgfQogIGlmIChpbmRlbnQgPD0gMCkgcmV0dXJuIGRlbHRhOwogIHJldHVybiBkZWx0YS5yZWR1Y2UoZnVuY3Rpb24gKGNvbXBvc2VkLCBvcCkgewogICAgaWYgKCFvcC5pbnNlcnQpIHJldHVybiBjb21wb3NlZDsKICAgIGlmIChvcC5hdHRyaWJ1dGVzICYmIHR5cGVvZiBvcC5hdHRyaWJ1dGVzLmluZGVudCA9PT0gJ251bWJlcicpIHsKICAgICAgcmV0dXJuIGNvbXBvc2VkLnB1c2gob3ApOwogICAgfQogICAgcmV0dXJuIGNvbXBvc2VkLmluc2VydChvcC5pbnNlcnQsICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7CiAgICAgIGluZGVudDogaW5kZW50CiAgICB9LCBvcC5hdHRyaWJ1dGVzIHx8IHt9KSk7CiAgfSwgbmV3IF9xdWlsbERlbHRhLmRlZmF1bHQoKSk7Cn0KZnVuY3Rpb24gbWF0Y2hMaXN0KG5vZGUsIGRlbHRhLCBzY3JvbGwpIHsKICB2YXIgZWxlbWVudCA9IG5vZGU7CiAgdmFyIGxpc3QgPSBlbGVtZW50LnRhZ05hbWUgPT09ICdPTCcgPyAnb3JkZXJlZCcgOiAnYnVsbGV0JzsKICB2YXIgY2hlY2tlZEF0dHIgPSBlbGVtZW50LmdldEF0dHJpYnV0ZSgnZGF0YS1jaGVja2VkJyk7CiAgaWYgKGNoZWNrZWRBdHRyKSB7CiAgICBsaXN0ID0gY2hlY2tlZEF0dHIgPT09ICd0cnVlJyA/ICdjaGVja2VkJyA6ICd1bmNoZWNrZWQnOwogIH0KICByZXR1cm4gYXBwbHlGb3JtYXQoZGVsdGEsICdsaXN0JywgbGlzdCwgc2Nyb2xsKTsKfQpmdW5jdGlvbiBtYXRjaE5ld2xpbmUobm9kZSwgZGVsdGEsIHNjcm9sbCkgewogIGlmICghZGVsdGFFbmRzV2l0aChkZWx0YSwgJ1xuJykpIHsKICAgIGlmIChpc0xpbmUobm9kZSwgc2Nyb2xsKSAmJiAobm9kZS5jaGlsZE5vZGVzLmxlbmd0aCA+IDAgfHwgbm9kZSBpbnN0YW5jZW9mIEhUTUxQYXJhZ3JhcGhFbGVtZW50KSkgewogICAgICByZXR1cm4gZGVsdGEuaW5zZXJ0KCdcbicpOwogICAgfQogICAgaWYgKGRlbHRhLmxlbmd0aCgpID4gMCAmJiBub2RlLm5leHRTaWJsaW5nKSB7CiAgICAgIHZhciBuZXh0U2libGluZyA9IG5vZGUubmV4dFNpYmxpbmc7CiAgICAgIHdoaWxlIChuZXh0U2libGluZyAhPSBudWxsKSB7CiAgICAgICAgaWYgKGlzTGluZShuZXh0U2libGluZywgc2Nyb2xsKSkgewogICAgICAgICAgcmV0dXJuIGRlbHRhLmluc2VydCgnXG4nKTsKICAgICAgICB9CiAgICAgICAgdmFyIG1hdGNoID0gc2Nyb2xsLnF1ZXJ5KG5leHRTaWJsaW5nKTsKICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yCiAgICAgICAgaWYgKG1hdGNoICYmIG1hdGNoLnByb3RvdHlwZSBpbnN0YW5jZW9mIF9ibG9jay5CbG9ja0VtYmVkKSB7CiAgICAgICAgICByZXR1cm4gZGVsdGEuaW5zZXJ0KCdcbicpOwogICAgICAgIH0KICAgICAgICBuZXh0U2libGluZyA9IG5leHRTaWJsaW5nLmZpcnN0Q2hpbGQ7CiAgICAgIH0KICAgIH0KICB9CiAgcmV0dXJuIGRlbHRhOwp9CmZ1bmN0aW9uIG1hdGNoU3R5bGVzKG5vZGUsIGRlbHRhLCBzY3JvbGwpIHsKICB2YXIgX3N0eWxlJGZvbnRXZWlnaHQ7CiAgdmFyIGZvcm1hdHMgPSB7fTsKICB2YXIgc3R5bGUgPSBub2RlLnN0eWxlIHx8IHt9OwogIGlmIChzdHlsZS5mb250U3R5bGUgPT09ICdpdGFsaWMnKSB7CiAgICBmb3JtYXRzLml0YWxpYyA9IHRydWU7CiAgfQogIGlmIChzdHlsZS50ZXh0RGVjb3JhdGlvbiA9PT0gJ3VuZGVybGluZScpIHsKICAgIGZvcm1hdHMudW5kZXJsaW5lID0gdHJ1ZTsKICB9CiAgaWYgKHN0eWxlLnRleHREZWNvcmF0aW9uID09PSAnbGluZS10aHJvdWdoJykgewogICAgZm9ybWF0cy5zdHJpa2UgPSB0cnVlOwogIH0KICBpZiAoKF9zdHlsZSRmb250V2VpZ2h0ID0gc3R5bGUuZm9udFdlaWdodCkgIT09IG51bGwgJiYgX3N0eWxlJGZvbnRXZWlnaHQgIT09IHZvaWQgMCAmJiBfc3R5bGUkZm9udFdlaWdodC5zdGFydHNXaXRoKCdib2xkJykgfHwKICAvLyBAdHMtZXhwZWN0LWVycm9yIEZpeCBtZSBsYXRlcgogIHBhcnNlSW50KHN0eWxlLmZvbnRXZWlnaHQsIDEwKSA+PSA3MDApIHsKICAgIGZvcm1hdHMuYm9sZCA9IHRydWU7CiAgfQogIGRlbHRhID0gT2JqZWN0LmVudHJpZXMoZm9ybWF0cykucmVkdWNlKGZ1bmN0aW9uIChuZXdEZWx0YSwgX3JlZjUpIHsKICAgIHZhciBfcmVmOSA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkoX3JlZjUsIDIpLAogICAgICBuYW1lID0gX3JlZjlbMF0sCiAgICAgIHZhbHVlID0gX3JlZjlbMV07CiAgICByZXR1cm4gYXBwbHlGb3JtYXQobmV3RGVsdGEsIG5hbWUsIHZhbHVlLCBzY3JvbGwpOwogIH0sIGRlbHRhKTsKICAvLyBAdHMtZXhwZWN0LWVycm9yCiAgaWYgKHBhcnNlRmxvYXQoc3R5bGUudGV4dEluZGVudCB8fCAwKSA+IDApIHsKICAgIC8vIENvdWxkIGJlIDAuNWluCiAgICByZXR1cm4gbmV3IF9xdWlsbERlbHRhLmRlZmF1bHQoKS5pbnNlcnQoJ1x0JykuY29uY2F0KGRlbHRhKTsKICB9CiAgcmV0dXJuIGRlbHRhOwp9CmZ1bmN0aW9uIG1hdGNoVGFibGUobm9kZSwgZGVsdGEsIHNjcm9sbCkgewogIHZhciBfbm9kZSRwYXJlbnRFbGVtZW50LCBfbm9kZSRwYXJlbnRFbGVtZW50MjsKICB2YXIgdGFibGUgPSAoKF9ub2RlJHBhcmVudEVsZW1lbnQgPSBub2RlLnBhcmVudEVsZW1lbnQpID09PSBudWxsIHx8IF9ub2RlJHBhcmVudEVsZW1lbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9ub2RlJHBhcmVudEVsZW1lbnQudGFnTmFtZSkgPT09ICdUQUJMRScgPyBub2RlLnBhcmVudEVsZW1lbnQgOiAoX25vZGUkcGFyZW50RWxlbWVudDIgPSBub2RlLnBhcmVudEVsZW1lbnQpID09PSBudWxsIHx8IF9ub2RlJHBhcmVudEVsZW1lbnQyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfbm9kZSRwYXJlbnRFbGVtZW50Mi5wYXJlbnRFbGVtZW50OwogIGlmICh0YWJsZSAhPSBudWxsKSB7CiAgICB2YXIgcm93cyA9IEFycmF5LmZyb20odGFibGUucXVlcnlTZWxlY3RvckFsbCgndHInKSk7CiAgICB2YXIgcm93ID0gcm93cy5pbmRleE9mKG5vZGUpICsgMTsKICAgIHJldHVybiBhcHBseUZvcm1hdChkZWx0YSwgJ3RhYmxlJywgcm93LCBzY3JvbGwpOwogIH0KICByZXR1cm4gZGVsdGE7Cn0KZnVuY3Rpb24gbWF0Y2hUZXh0KG5vZGUsIGRlbHRhLCBzY3JvbGwpIHsKICB2YXIgX25vZGUkcGFyZW50RWxlbWVudDM7CiAgLy8gQHRzLWV4cGVjdC1lcnJvcgogIHZhciB0ZXh0ID0gbm9kZS5kYXRhOwogIC8vIFdvcmQgcmVwcmVzZW50cyBlbXB0eSBsaW5lIHdpdGggPG86cD4mbmJzcDs8L286cD4KICBpZiAoKChfbm9kZSRwYXJlbnRFbGVtZW50MyA9IG5vZGUucGFyZW50RWxlbWVudCkgPT09IG51bGwgfHwgX25vZGUkcGFyZW50RWxlbWVudDMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9ub2RlJHBhcmVudEVsZW1lbnQzLnRhZ05hbWUpID09PSAnTzpQJykgewogICAgcmV0dXJuIGRlbHRhLmluc2VydCh0ZXh0LnRyaW0oKSk7CiAgfQogIGlmICghaXNQcmUobm9kZSkpIHsKICAgIGlmICh0ZXh0LnRyaW0oKS5sZW5ndGggPT09IDAgJiYgdGV4dC5pbmNsdWRlcygnXG4nKSAmJiAhaXNCZXR3ZWVuSW5saW5lRWxlbWVudHMobm9kZSwgc2Nyb2xsKSkgewogICAgICByZXR1cm4gZGVsdGE7CiAgICB9CiAgICB2YXIgcmVwbGFjZXIgPSBmdW5jdGlvbiByZXBsYWNlcihjb2xsYXBzZSwgbWF0Y2gpIHsKICAgICAgdmFyIHJlcGxhY2VkID0gbWF0Y2gucmVwbGFjZSgvW15cdTAwYTBdL2csICcnKTsgLy8gXHUwMGEwIGlzIG5ic3A7CiAgICAgIHJldHVybiByZXBsYWNlZC5sZW5ndGggPCAxICYmIGNvbGxhcHNlID8gJyAnIDogcmVwbGFjZWQ7CiAgICB9OwogICAgdGV4dCA9IHRleHQucmVwbGFjZSgvXHJcbi9nLCAnICcpLnJlcGxhY2UoL1xuL2csICcgJyk7CiAgICB0ZXh0ID0gdGV4dC5yZXBsYWNlKC9cc1xzKy9nLCByZXBsYWNlci5iaW5kKHJlcGxhY2VyLCB0cnVlKSk7IC8vIGNvbGxhcHNlIHdoaXRlc3BhY2UKICAgIGlmIChub2RlLnByZXZpb3VzU2libGluZyA9PSBudWxsICYmIG5vZGUucGFyZW50RWxlbWVudCAhPSBudWxsICYmIGlzTGluZShub2RlLnBhcmVudEVsZW1lbnQsIHNjcm9sbCkgfHwgbm9kZS5wcmV2aW91c1NpYmxpbmcgaW5zdGFuY2VvZiBFbGVtZW50ICYmIGlzTGluZShub2RlLnByZXZpb3VzU2libGluZywgc2Nyb2xsKSkgewogICAgICB0ZXh0ID0gdGV4dC5yZXBsYWNlKC9eXHMrLywgcmVwbGFjZXIuYmluZChyZXBsYWNlciwgZmFsc2UpKTsKICAgIH0KICAgIGlmIChub2RlLm5leHRTaWJsaW5nID09IG51bGwgJiYgbm9kZS5wYXJlbnRFbGVtZW50ICE9IG51bGwgJiYgaXNMaW5lKG5vZGUucGFyZW50RWxlbWVudCwgc2Nyb2xsKSB8fCBub2RlLm5leHRTaWJsaW5nIGluc3RhbmNlb2YgRWxlbWVudCAmJiBpc0xpbmUobm9kZS5uZXh0U2libGluZywgc2Nyb2xsKSkgewogICAgICB0ZXh0ID0gdGV4dC5yZXBsYWNlKC9ccyskLywgcmVwbGFjZXIuYmluZChyZXBsYWNlciwgZmFsc2UpKTsKICAgIH0KICB9CiAgcmV0dXJuIGRlbHRhLmluc2VydCh0ZXh0KTsKfQ=="}, {"version": 3, "names": ["_parchment", "require", "_quill<PERSON><PERSON><PERSON>", "_interopRequireDefault", "_block", "_logger", "_module", "_quill", "_align", "_background", "_code", "_color", "_direction", "_font", "_size", "_keyboard", "_index", "debug", "logger", "CLIPBOARD_CONFIG", "Node", "TEXT_NODE", "matchText", "matchNewline", "matchBreak", "ELEMENT_NODE", "matchBlot", "matchAttributor", "matchStyles", "matchIndent", "matchList", "matchCodeBlock", "matchTable", "createMatchAlias", "matchIgnore", "ATTRIBUTE_ATTRIBUTORS", "AlignAttribute", "DirectionAttribute", "reduce", "memo", "attr", "keyName", "STYLE_ATTRIBUTORS", "AlignStyle", "BackgroundStyle", "ColorStyle", "DirectionStyle", "FontStyle", "SizeStyle", "Clipboard", "exports", "default", "_Module", "quill", "options", "_this$options$matcher", "_this", "_classCallCheck2", "_callSuper2", "root", "addEventListener", "e", "onCaptureCopy", "onCapturePaste", "bind", "matchers", "concat", "for<PERSON>ach", "_ref", "_ref6", "_slicedToArray2", "selector", "matcher", "addMatcher", "_inherits2", "_createClass2", "key", "value", "push", "convert", "_ref2", "html", "text", "formats", "arguments", "length", "undefined", "CodeBlock", "blotName", "Delta", "insert", "_defineProperty2", "delta", "convertHTML", "deltaEndsWith", "ops", "attributes", "table", "compose", "retain", "delete", "normalizeHTML", "doc", "normalizeExternalHTML", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "container", "body", "nodeMatches", "WeakMap", "_this$prepareMatching", "prepareMatching", "_this$prepareMatching2", "elementMatchers", "textMatchers", "traverse", "scroll", "dangerouslyPasteHTML", "index", "source", "<PERSON><PERSON><PERSON>", "sources", "API", "setContents", "setSelection", "SILENT", "paste", "updateContents", "_e$clipboardData", "_e$clipboardData2", "isCut", "defaultPrevented", "preventDefault", "_this$quill$selection", "selection", "getRange", "_this$quill$selection2", "range", "_this$onCopy", "onCopy", "clipboardData", "setData", "deleteRange", "normalizeURIList", "urlList", "split", "filter", "url", "join", "_e$clipboardData3", "_e$clipboardData4", "_e$clipboardData6", "isEnabled", "getSelection", "getData", "_e$clipboardData5", "files", "Array", "from", "uploader", "upload", "_doc$body$firstElemen", "childElementCount", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "onPaste", "getText", "getSemanticHTML", "_ref3", "getFormat", "pastedDelta", "log", "USER", "scrollSelectionIntoView", "pair", "_pair", "querySelectorAll", "node", "has", "matches", "get", "set", "<PERSON><PERSON><PERSON>", "applyFormat", "format", "query", "newDelta", "op", "_objectSpread2", "endText", "i", "slice", "isLine", "Element", "match", "prototype", "EmbedBlot", "includes", "toLowerCase", "isBetweenInlineElements", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "preNodes", "isPre", "parentNode", "nodeType", "childNodes", "childNode", "<PERSON><PERSON><PERSON><PERSON>", "reducedDelta", "_node", "Attributor", "keys", "classes", "ClassAttributor", "styles", "StyleAttributor", "name", "<PERSON><PERSON>", "ATTRIBUTE", "attrName", "Object", "entries", "_ref4", "_ref8", "embed", "BlockBlot", "language", "indent", "parent", "composed", "element", "list", "checkedAttr", "getAttribute", "HTMLParagraphElement", "nextS<PERSON>ling", "BlockEmbed", "<PERSON><PERSON><PERSON><PERSON>", "_style$fontWeight", "style", "fontStyle", "italic", "textDecoration", "underline", "strike", "fontWeight", "startsWith", "parseInt", "bold", "_ref5", "_ref9", "parseFloat", "textIndent", "_node$parentElement", "_node$parentElement2", "parentElement", "rows", "row", "indexOf", "_node$parentElement3", "data", "trim", "replacer", "collapse", "replaced", "replace", "previousSibling"], "sources": ["../../src/modules/clipboard.ts"], "sourcesContent": ["import type { ScrollBlot } from 'parchment';\nimport {\n  Attributor,\n  BlockBlot,\n  ClassAttributor,\n  EmbedB<PERSON>,\n  Scope,\n  StyleAttributor,\n} from 'parchment';\nimport Delta from 'quill-delta';\nimport { BlockEmbed } from '../blots/block.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { AlignAttribute, AlignStyle } from '../formats/align.js';\nimport { BackgroundStyle } from '../formats/background.js';\nimport CodeBlock from '../formats/code.js';\nimport { ColorStyle } from '../formats/color.js';\nimport { DirectionAttribute, DirectionStyle } from '../formats/direction.js';\nimport { FontStyle } from '../formats/font.js';\nimport { SizeStyle } from '../formats/size.js';\nimport { deleteRange } from './keyboard.js';\nimport normalizeExternalHTML from './normalizeExternalHTML/index.js';\n\nconst debug = logger('quill:clipboard');\n\ntype Selector = string | Node['TEXT_NODE'] | Node['ELEMENT_NODE'];\ntype Matcher = (node: Node, delta: Delta, scroll: ScrollBlot) => Delta;\n\nconst CLIPBOARD_CONFIG: [Selector, Matcher][] = [\n  [Node.TEXT_NODE, matchText],\n  [Node.TEXT_NODE, matchNewline],\n  ['br', matchBreak],\n  [Node.ELEMENT_NODE, matchNewline],\n  [Node.ELEMENT_NODE, matchBlot],\n  [Node.ELEMENT_NODE, matchAttributor],\n  [Node.ELEMENT_NODE, matchStyles],\n  ['li', matchIndent],\n  ['ol, ul', matchList],\n  ['pre', matchCodeBlock],\n  ['tr', matchTable],\n  ['b', createMatchAlias('bold')],\n  ['i', createMatchAlias('italic')],\n  ['strike', createMatchAlias('strike')],\n  ['style', matchIgnore],\n];\n\nconst ATTRIBUTE_ATTRIBUTORS = [AlignAttribute, DirectionAttribute].reduce(\n  (memo: Record<string, Attributor>, attr) => {\n    memo[attr.keyName] = attr;\n    return memo;\n  },\n  {},\n);\n\nconst STYLE_ATTRIBUTORS = [\n  AlignStyle,\n  BackgroundStyle,\n  ColorStyle,\n  DirectionStyle,\n  FontStyle,\n  SizeStyle,\n].reduce((memo: Record<string, Attributor>, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\n\ninterface ClipboardOptions {\n  matchers: [Selector, Matcher][];\n}\n\nclass Clipboard extends Module<ClipboardOptions> {\n  static DEFAULTS: ClipboardOptions = {\n    matchers: [],\n  };\n\n  matchers: [Selector, Matcher][];\n\n  constructor(quill: Quill, options: Partial<ClipboardOptions>) {\n    super(quill, options);\n    this.quill.root.addEventListener('copy', (e) =>\n      this.onCaptureCopy(e, false),\n    );\n    this.quill.root.addEventListener('cut', (e) => this.onCaptureCopy(e, true));\n    this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));\n    this.matchers = [];\n    CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach(\n      ([selector, matcher]) => {\n        this.addMatcher(selector, matcher);\n      },\n    );\n  }\n\n  addMatcher(selector: Selector, matcher: Matcher) {\n    this.matchers.push([selector, matcher]);\n  }\n\n  convert(\n    { html, text }: { html?: string; text?: string },\n    formats: Record<string, unknown> = {},\n  ) {\n    if (formats[CodeBlock.blotName]) {\n      return new Delta().insert(text || '', {\n        [CodeBlock.blotName]: formats[CodeBlock.blotName],\n      });\n    }\n    if (!html) {\n      return new Delta().insert(text || '', formats);\n    }\n    const delta = this.convertHTML(html);\n    // Remove trailing newline\n    if (\n      deltaEndsWith(delta, '\\n') &&\n      (delta.ops[delta.ops.length - 1].attributes == null || formats.table)\n    ) {\n      return delta.compose(new Delta().retain(delta.length() - 1).delete(1));\n    }\n    return delta;\n  }\n\n  protected normalizeHTML(doc: Document) {\n    normalizeExternalHTML(doc);\n  }\n\n  protected convertHTML(html: string) {\n    const doc = new DOMParser().parseFromString(html, 'text/html');\n    this.normalizeHTML(doc);\n    const container = doc.body;\n    const nodeMatches = new WeakMap();\n    const [elementMatchers, textMatchers] = this.prepareMatching(\n      container,\n      nodeMatches,\n    );\n    return traverse(\n      this.quill.scroll,\n      container,\n      elementMatchers,\n      textMatchers,\n      nodeMatches,\n    );\n  }\n\n  dangerouslyPasteHTML(html: string, source?: EmitterSource): void;\n  dangerouslyPasteHTML(\n    index: number,\n    html: string,\n    source?: EmitterSource,\n  ): void;\n  dangerouslyPasteHTML(\n    index: number | string,\n    html?: string,\n    source: EmitterSource = Quill.sources.API,\n  ) {\n    if (typeof index === 'string') {\n      const delta = this.convert({ html: index, text: '' });\n      // @ts-expect-error\n      this.quill.setContents(delta, html);\n      this.quill.setSelection(0, Quill.sources.SILENT);\n    } else {\n      const paste = this.convert({ html, text: '' });\n      this.quill.updateContents(\n        new Delta().retain(index).concat(paste),\n        source,\n      );\n      this.quill.setSelection(index + paste.length(), Quill.sources.SILENT);\n    }\n  }\n\n  onCaptureCopy(e: ClipboardEvent, isCut = false) {\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const [range] = this.quill.selection.getRange();\n    if (range == null) return;\n    const { html, text } = this.onCopy(range, isCut);\n    e.clipboardData?.setData('text/plain', text);\n    e.clipboardData?.setData('text/html', html);\n    if (isCut) {\n      deleteRange({ range, quill: this.quill });\n    }\n  }\n\n  /*\n   * https://www.iana.org/assignments/media-types/text/uri-list\n   */\n  private normalizeURIList(urlList: string) {\n    return (\n      urlList\n        .split(/\\r?\\n/)\n        // Ignore all comments\n        .filter((url) => url[0] !== '#')\n        .join('\\n')\n    );\n  }\n\n  onCapturePaste(e: ClipboardEvent) {\n    if (e.defaultPrevented || !this.quill.isEnabled()) return;\n    e.preventDefault();\n    const range = this.quill.getSelection(true);\n    if (range == null) return;\n    const html = e.clipboardData?.getData('text/html');\n    let text = e.clipboardData?.getData('text/plain');\n    if (!html && !text) {\n      const urlList = e.clipboardData?.getData('text/uri-list');\n      if (urlList) {\n        text = this.normalizeURIList(urlList);\n      }\n    }\n    const files = Array.from(e.clipboardData?.files || []);\n    if (!html && files.length > 0) {\n      this.quill.uploader.upload(range, files);\n      return;\n    }\n    if (html && files.length > 0) {\n      const doc = new DOMParser().parseFromString(html, 'text/html');\n      if (\n        doc.body.childElementCount === 1 &&\n        doc.body.firstElementChild?.tagName === 'IMG'\n      ) {\n        this.quill.uploader.upload(range, files);\n        return;\n      }\n    }\n    this.onPaste(range, { html, text });\n  }\n\n  onCopy(range: Range, isCut: boolean): { html: string; text: string };\n  onCopy(range: Range) {\n    const text = this.quill.getText(range);\n    const html = this.quill.getSemanticHTML(range);\n    return { html, text };\n  }\n\n  onPaste(range: Range, { text, html }: { text?: string; html?: string }) {\n    const formats = this.quill.getFormat(range.index);\n    const pastedDelta = this.convert({ text, html }, formats);\n    debug.log('onPaste', pastedDelta, { text, html });\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .concat(pastedDelta);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    // range.length contributes to delta.length()\n    this.quill.setSelection(\n      delta.length() - range.length,\n      Quill.sources.SILENT,\n    );\n    this.quill.scrollSelectionIntoView();\n  }\n\n  prepareMatching(container: Element, nodeMatches: WeakMap<Node, Matcher[]>) {\n    const elementMatchers: Matcher[] = [];\n    const textMatchers: Matcher[] = [];\n    this.matchers.forEach((pair) => {\n      const [selector, matcher] = pair;\n      switch (selector) {\n        case Node.TEXT_NODE:\n          textMatchers.push(matcher);\n          break;\n        case Node.ELEMENT_NODE:\n          elementMatchers.push(matcher);\n          break;\n        default:\n          Array.from(container.querySelectorAll(selector)).forEach((node) => {\n            if (nodeMatches.has(node)) {\n              const matches = nodeMatches.get(node);\n              matches?.push(matcher);\n            } else {\n              nodeMatches.set(node, [matcher]);\n            }\n          });\n          break;\n      }\n    });\n    return [elementMatchers, textMatchers];\n  }\n}\n\nfunction applyFormat(\n  delta: Delta,\n  format: string,\n  value: unknown,\n  scroll: ScrollBlot,\n): Delta {\n  if (!scroll.query(format)) {\n    return delta;\n  }\n\n  return delta.reduce((newDelta, op) => {\n    if (!op.insert) return newDelta;\n    if (op.attributes && op.attributes[format]) {\n      return newDelta.push(op);\n    }\n    const formats = value ? { [format]: value } : {};\n    return newDelta.insert(op.insert, { ...formats, ...op.attributes });\n  }, new Delta());\n}\n\nfunction deltaEndsWith(delta: Delta, text: string) {\n  let endText = '';\n  for (\n    let i = delta.ops.length - 1;\n    i >= 0 && endText.length < text.length;\n    --i // eslint-disable-line no-plusplus\n  ) {\n    const op = delta.ops[i];\n    if (typeof op.insert !== 'string') break;\n    endText = op.insert + endText;\n  }\n  return endText.slice(-1 * text.length) === text;\n}\n\nfunction isLine(node: Node, scroll: ScrollBlot) {\n  if (!(node instanceof Element)) return false;\n  const match = scroll.query(node);\n  // @ts-expect-error\n  if (match && match.prototype instanceof EmbedBlot) return false;\n\n  return [\n    'address',\n    'article',\n    'blockquote',\n    'canvas',\n    'dd',\n    'div',\n    'dl',\n    'dt',\n    'fieldset',\n    'figcaption',\n    'figure',\n    'footer',\n    'form',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'header',\n    'iframe',\n    'li',\n    'main',\n    'nav',\n    'ol',\n    'output',\n    'p',\n    'pre',\n    'section',\n    'table',\n    'td',\n    'tr',\n    'ul',\n    'video',\n  ].includes(node.tagName.toLowerCase());\n}\n\nfunction isBetweenInlineElements(node: HTMLElement, scroll: ScrollBlot) {\n  return (\n    node.previousElementSibling &&\n    node.nextElementSibling &&\n    !isLine(node.previousElementSibling, scroll) &&\n    !isLine(node.nextElementSibling, scroll)\n  );\n}\n\nconst preNodes = new WeakMap();\nfunction isPre(node: Node | null) {\n  if (node == null) return false;\n  if (!preNodes.has(node)) {\n    // @ts-expect-error\n    if (node.tagName === 'PRE') {\n      preNodes.set(node, true);\n    } else {\n      preNodes.set(node, isPre(node.parentNode));\n    }\n  }\n  return preNodes.get(node);\n}\n\nfunction traverse(\n  scroll: ScrollBlot,\n  node: ChildNode,\n  elementMatchers: Matcher[],\n  textMatchers: Matcher[],\n  nodeMatches: WeakMap<Node, Matcher[]>,\n): Delta {\n  // Post-order\n  if (node.nodeType === node.TEXT_NODE) {\n    return textMatchers.reduce((delta: Delta, matcher) => {\n      return matcher(node, delta, scroll);\n    }, new Delta());\n  }\n  if (node.nodeType === node.ELEMENT_NODE) {\n    return Array.from(node.childNodes || []).reduce((delta, childNode) => {\n      let childrenDelta = traverse(\n        scroll,\n        childNode,\n        elementMatchers,\n        textMatchers,\n        nodeMatches,\n      );\n      if (childNode.nodeType === node.ELEMENT_NODE) {\n        childrenDelta = elementMatchers.reduce((reducedDelta, matcher) => {\n          return matcher(childNode as HTMLElement, reducedDelta, scroll);\n        }, childrenDelta);\n        childrenDelta = (nodeMatches.get(childNode) || []).reduce(\n          (reducedDelta, matcher) => {\n            return matcher(childNode, reducedDelta, scroll);\n          },\n          childrenDelta,\n        );\n      }\n      return delta.concat(childrenDelta);\n    }, new Delta());\n  }\n  return new Delta();\n}\n\nfunction createMatchAlias(format: string) {\n  return (_node: Element, delta: Delta, scroll: ScrollBlot) => {\n    return applyFormat(delta, format, true, scroll);\n  };\n}\n\nfunction matchAttributor(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const attributes = Attributor.keys(node);\n  const classes = ClassAttributor.keys(node);\n  const styles = StyleAttributor.keys(node);\n  const formats: Record<string, string | undefined> = {};\n  attributes\n    .concat(classes)\n    .concat(styles)\n    .forEach((name) => {\n      let attr = scroll.query(name, Scope.ATTRIBUTE) as Attributor;\n      if (attr != null) {\n        formats[attr.attrName] = attr.value(node);\n        if (formats[attr.attrName]) return;\n      }\n      attr = ATTRIBUTE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n      attr = STYLE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        attr = STYLE_ATTRIBUTORS[name];\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n    });\n\n  return Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n}\n\nfunction matchBlot(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (match == null) return delta;\n  // @ts-expect-error\n  if (match.prototype instanceof EmbedBlot) {\n    const embed = {};\n    // @ts-expect-error\n    const value = match.value(node);\n    if (value != null) {\n      // @ts-expect-error\n      embed[match.blotName] = value;\n      // @ts-expect-error\n      return new Delta().insert(embed, match.formats(node, scroll));\n    }\n  } else {\n    // @ts-expect-error\n    if (match.prototype instanceof BlockBlot && !deltaEndsWith(delta, '\\n')) {\n      delta.insert('\\n');\n    }\n    if (\n      'blotName' in match &&\n      'formats' in match &&\n      typeof match.formats === 'function'\n    ) {\n      return applyFormat(\n        delta,\n        match.blotName,\n        match.formats(node, scroll),\n        scroll,\n      );\n    }\n  }\n  return delta;\n}\n\nfunction matchBreak(node: Node, delta: Delta) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    delta.insert('\\n');\n  }\n  return delta;\n}\n\nfunction matchCodeBlock(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query('code-block');\n  const language =\n    match && 'formats' in match && typeof match.formats === 'function'\n      ? match.formats(node, scroll)\n      : true;\n  return applyFormat(delta, 'code-block', language, scroll);\n}\n\nfunction matchIgnore() {\n  return new Delta();\n}\n\nfunction matchIndent(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (\n    match == null ||\n    // @ts-expect-error\n    match.blotName !== 'list' ||\n    !deltaEndsWith(delta, '\\n')\n  ) {\n    return delta;\n  }\n  let indent = -1;\n  let parent = node.parentNode;\n  while (parent != null) {\n    // @ts-expect-error\n    if (['OL', 'UL'].includes(parent.tagName)) {\n      indent += 1;\n    }\n    parent = parent.parentNode;\n  }\n  if (indent <= 0) return delta;\n  return delta.reduce((composed, op) => {\n    if (!op.insert) return composed;\n    if (op.attributes && typeof op.attributes.indent === 'number') {\n      return composed.push(op);\n    }\n    return composed.insert(op.insert, { indent, ...(op.attributes || {}) });\n  }, new Delta());\n}\n\nfunction matchList(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const element = node as Element;\n  let list = element.tagName === 'OL' ? 'ordered' : 'bullet';\n\n  const checkedAttr = element.getAttribute('data-checked');\n  if (checkedAttr) {\n    list = checkedAttr === 'true' ? 'checked' : 'unchecked';\n  }\n\n  return applyFormat(delta, 'list', list, scroll);\n}\n\nfunction matchNewline(node: Node, delta: Delta, scroll: ScrollBlot) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    if (\n      isLine(node, scroll) &&\n      (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)\n    ) {\n      return delta.insert('\\n');\n    }\n    if (delta.length() > 0 && node.nextSibling) {\n      let nextSibling: Node | null = node.nextSibling;\n      while (nextSibling != null) {\n        if (isLine(nextSibling, scroll)) {\n          return delta.insert('\\n');\n        }\n        const match = scroll.query(nextSibling);\n        // @ts-expect-error\n        if (match && match.prototype instanceof BlockEmbed) {\n          return delta.insert('\\n');\n        }\n        nextSibling = nextSibling.firstChild;\n      }\n    }\n  }\n  return delta;\n}\n\nfunction matchStyles(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const formats: Record<string, unknown> = {};\n  const style: Partial<CSSStyleDeclaration> = node.style || {};\n  if (style.fontStyle === 'italic') {\n    formats.italic = true;\n  }\n  if (style.textDecoration === 'underline') {\n    formats.underline = true;\n  }\n  if (style.textDecoration === 'line-through') {\n    formats.strike = true;\n  }\n  if (\n    style.fontWeight?.startsWith('bold') ||\n    // @ts-expect-error Fix me later\n    parseInt(style.fontWeight, 10) >= 700\n  ) {\n    formats.bold = true;\n  }\n  delta = Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n  // @ts-expect-error\n  if (parseFloat(style.textIndent || 0) > 0) {\n    // Could be 0.5in\n    return new Delta().insert('\\t').concat(delta);\n  }\n  return delta;\n}\n\nfunction matchTable(\n  node: HTMLTableRowElement,\n  delta: Delta,\n  scroll: ScrollBlot,\n) {\n  const table =\n    node.parentElement?.tagName === 'TABLE'\n      ? node.parentElement\n      : node.parentElement?.parentElement;\n  if (table != null) {\n    const rows = Array.from(table.querySelectorAll('tr'));\n    const row = rows.indexOf(node) + 1;\n    return applyFormat(delta, 'table', row, scroll);\n  }\n  return delta;\n}\n\nfunction matchText(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  // @ts-expect-error\n  let text = node.data;\n  // Word represents empty line with <o:p>&nbsp;</o:p>\n  if (node.parentElement?.tagName === 'O:P') {\n    return delta.insert(text.trim());\n  }\n  if (!isPre(node)) {\n    if (\n      text.trim().length === 0 &&\n      text.includes('\\n') &&\n      !isBetweenInlineElements(node, scroll)\n    ) {\n      return delta;\n    }\n    const replacer = (collapse: unknown, match: string) => {\n      const replaced = match.replace(/[^\\u00a0]/g, ''); // \\u00a0 is nbsp;\n      return replaced.length < 1 && collapse ? ' ' : replaced;\n    };\n    text = text.replace(/\\r\\n/g, ' ').replace(/\\n/g, ' ');\n    text = text.replace(/\\s\\s+/g, replacer.bind(replacer, true)); // collapse whitespace\n    if (\n      (node.previousSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.previousSibling instanceof Element &&\n        isLine(node.previousSibling, scroll))\n    ) {\n      text = text.replace(/^\\s+/, replacer.bind(replacer, false));\n    }\n    if (\n      (node.nextSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.nextSibling instanceof Element && isLine(node.nextSibling, scroll))\n    ) {\n      text = text.replace(/\\s+$/, replacer.bind(replacer, false));\n    }\n  }\n  return delta.insert(text);\n}\n\nexport {\n  Clipboard as default,\n  matchAttributor,\n  matchBlot,\n  matchNewline,\n  matchText,\n  traverse,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AAEA,IAAAI,OAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,OAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AAEA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,UAAA,GAAAX,OAAA;AACA,IAAAY,KAAA,GAAAZ,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;AACA,IAAAc,SAAA,GAAAd,OAAA;AACA,IAAAe,MAAA,GAAAb,sBAAA,CAAAF,OAAA;AAEA,IAAMgB,KAAK,GAAG,IAAAC,eAAM,EAAC,iBAAiB,CAAC;AAKvC,IAAMC,gBAAuC,GAAG,CAC9C,CAACC,IAAI,CAACC,SAAS,EAAEC,SAAS,CAAC,EAC3B,CAACF,IAAI,CAACC,SAAS,EAAEE,YAAY,CAAC,EAC9B,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAACJ,IAAI,CAACK,YAAY,EAAEF,YAAY,CAAC,EACjC,CAACH,IAAI,CAACK,YAAY,EAAEC,SAAS,CAAC,EAC9B,CAACN,IAAI,CAACK,YAAY,EAAEE,eAAe,CAAC,EACpC,CAACP,IAAI,CAACK,YAAY,EAAEG,WAAW,CAAC,EAChC,CAAC,IAAI,EAAEC,WAAW,CAAC,EACnB,CAAC,QAAQ,EAAEC,SAAS,CAAC,EACrB,CAAC,KAAK,EAAEC,cAAc,CAAC,EACvB,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAAC,GAAG,EAAEC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACjC,CAAC,QAAQ,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACtC,CAAC,OAAO,EAAEC,WAAW,CAAC,CACvB;AAED,IAAMC,qBAAqB,GAAG,CAACC,qBAAc,EAAEC,6BAAkB,CAAC,CAACC,MAAM,CACvE,UAACC,IAAgC,EAAEC,IAAI,EAAK;EAC1CD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EACD,CAAC,CACH,CAAC;AAED,IAAMG,iBAAiB,GAAG,CACxBC,iBAAU,EACVC,2BAAe,EACfC,iBAAU,EACVC,yBAAc,EACdC,eAAS,EACTC,eAAS,CACV,CAACV,MAAM,CAAC,UAACC,IAAgC,EAAEC,IAAI,EAAK;EACnDD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,IAMAU,SAAS,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAOb,SAAAH,UAAYI,KAAY,EAAEC,OAAkC,EAAE;IAAA,IAAAC,qBAAA;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAN,OAAA,QAAAF,SAAA;IAC5DO,KAAA,OAAAE,WAAA,CAAAP,OAAA,QAAAF,SAAA,GAAMI,KAAK,EAAEC,OAAO;IACpBE,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAG,UAAAC,CAAC;MAAA,OACzCL,KAAA,CAAKM,aAAa,CAACD,CAAC,EAAE,KAAK,CAC7B;IAAA,EAAC;IACDL,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAG,UAAAC,CAAC;MAAA,OAAKL,KAAA,CAAKM,aAAa,CAACD,CAAC,EAAE,IAAI,CAAC;IAAA,EAAC;IAC3EL,KAAA,CAAKH,KAAK,CAACM,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAEJ,KAAA,CAAKO,cAAc,CAACC,IAAI,CAAAR,KAAK,CAAC,CAAC;IACzEA,KAAA,CAAKS,QAAQ,GAAG,EAAE;IAClB9C,gBAAgB,CAAC+C,MAAM,EAAAX,qBAAA,GAACC,KAAA,CAAKF,OAAO,CAACW,QAAQ,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,CAACY,OAAO,CAC1D,UAAAC,IAAA,EAAyB;MAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAnB,OAAA,EAALiB,IAAA;QAAlBG,QAAQ,GAAAF,KAAA;QAAEG,OAAO,GAAAH,KAAA;MACjBb,KAAA,CAAKiB,UAAU,CAACF,QAAQ,EAAEC,OAAO,CAAC;IACpC,CACF,CAAC;IAAA,OAAAhB,KAAA;EACH;EAAA,IAAAkB,UAAA,CAAAvB,OAAA,EAAAF,SAAA,EAAAG,OAAA;EAAA,WAAAuB,aAAA,CAAAxB,OAAA,EAAAF,SAAA;IAAA2B,GAAA;IAAAC,KAAA,EAEA,SAAAJ,UAAUA,CAACF,QAAkB,EAAEC,OAAgB,EAAE;MAC/C,IAAI,CAACP,QAAQ,CAACa,IAAI,CAAC,CAACP,QAAQ,EAAEC,OAAO,CAAC,CAAC;IACzC;EAAA;IAAAI,GAAA;IAAAC,KAAA,EAEA,SAAAE,OAAOA,CAAAC,KAAA,EAGL;MAAA,IAFEC,IAAI,GAA0CD,KAAA,CAA9CC,IAAI;QAAEC,IAAA,GAAwCF,KAAA,CAAxCE,IAAA;MAAwC,IAChDC,OAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAErC,IAAID,OAAO,CAACI,aAAS,CAACC,QAAQ,CAAC,EAAE;QAC/B,OAAO,IAAIC,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,IAAI,EAAE,MAAAS,gBAAA,CAAAxC,OAAA,MACjCoC,aAAS,CAACC,QAAQ,EAAGL,OAAO,CAACI,aAAS,CAACC,QAAQ,EACjD,CAAC;MACJ;MACA,IAAI,CAACP,IAAI,EAAE;QACT,OAAO,IAAIQ,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC;MAChD;MACA,IAAMS,KAAK,GAAG,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;MACpC;MACA,IACEa,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,KACzBA,KAAK,CAACG,GAAG,CAACH,KAAK,CAACG,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC,CAACW,UAAU,IAAI,IAAI,IAAIb,OAAO,CAACc,KAAK,CAAC,EACrE;QACA,OAAOL,KAAK,CAACM,OAAO,CAAC,IAAIT,mBAAK,CAAC,CAAC,CAACU,MAAM,CAACP,KAAK,CAACP,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;MACxE;MACA,OAAOR,KAAK;IACd;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEU,SAAAwB,aAAaA,CAACC,GAAa,EAAE;MACrC,IAAAC,cAAqB,EAACD,GAAG,CAAC;IAC5B;EAAA;IAAA1B,GAAA;IAAAC,KAAA,EAEU,SAAAgB,WAAWA,CAACZ,IAAY,EAAE;MAClC,IAAMqB,GAAG,GAAG,IAAIE,SAAS,CAAC,CAAC,CAACC,eAAe,CAACxB,IAAI,EAAE,WAAW,CAAC;MAC9D,IAAI,CAACoB,aAAa,CAACC,GAAG,CAAC;MACvB,IAAMI,SAAS,GAAGJ,GAAG,CAACK,IAAI;MAC1B,IAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;MACjC,IAAAC,qBAAA,GAAwC,IAAI,CAACC,eAAe,CAC1DL,SAAS,EACTE,WACF,CAAC;QAAAI,sBAAA,OAAA1C,eAAA,CAAAnB,OAAA,EAAA2D,qBAAA;QAHMG,eAAe,GAAAD,sBAAA;QAAEE,YAAY,GAAAF,sBAAA;MAIpC,OAAOG,QAAQ,CACb,IAAI,CAAC9D,KAAK,CAAC+D,MAAM,EACjBV,SAAS,EACTO,eAAe,EACfC,YAAY,EACZN,WACF,CAAC;IACH;EAAA;IAAAhC,GAAA;IAAAC,KAAA,EAQA,SAAAwC,oBAAoBA,CAClBC,KAAsB,EACtBrC,IAAa,EAEb;MAAA,IADAsC,MAAqB,GAAAnC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGoC,cAAK,CAACC,OAAO,CAACC,GAAG;MAEzC,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAM1B,KAAK,GAAG,IAAI,CAACb,OAAO,CAAC;UAAEE,IAAI,EAAEqC,KAAK;UAAEpC,IAAI,EAAE;QAAG,CAAC,CAAC;QACrD;QACA,IAAI,CAAC7B,KAAK,CAACsE,WAAW,CAAC/B,KAAK,EAAEX,IAAI,CAAC;QACnC,IAAI,CAAC5B,KAAK,CAACuE,YAAY,CAAC,CAAC,EAAEJ,cAAK,CAACC,OAAO,CAACI,MAAM,CAAC;MAClD,CAAC,MAAM;QACL,IAAMC,KAAK,GAAG,IAAI,CAAC/C,OAAO,CAAC;UAAEE,IAAI,EAAJA,IAAI;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC;QAC9C,IAAI,CAAC7B,KAAK,CAAC0E,cAAc,CACvB,IAAItC,mBAAK,CAAC,CAAC,CAACU,MAAM,CAACmB,KAAK,CAAC,CAACpD,MAAM,CAAC4D,KAAK,CAAC,EACvCP,MACF,CAAC;QACD,IAAI,CAAClE,KAAK,CAACuE,YAAY,CAACN,KAAK,GAAGQ,KAAK,CAACzC,MAAM,CAAC,CAAC,EAAEmC,cAAK,CAACC,OAAO,CAACI,MAAM,CAAC;MACvE;IACF;EAAA;IAAAjD,GAAA;IAAAC,KAAA,EAEA,SAAAf,aAAaA,CAACD,CAAiB,EAAiB;MAAA,IAAAmE,gBAAA,EAAAC,iBAAA;MAAA,IAAfC,KAAK,GAAA9C,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAC5C,IAAIvB,CAAC,CAACsE,gBAAgB,EAAE;MACxBtE,CAAC,CAACuE,cAAc,CAAC,CAAC;MAClB,IAAAC,qBAAA,GAAgB,IAAI,CAAChF,KAAK,CAACiF,SAAS,CAACC,QAAQ,CAAC,CAAC;QAAAC,sBAAA,OAAAlE,eAAA,CAAAnB,OAAA,EAAAkF,qBAAA;QAAxCI,KAAK,GAAAD,sBAAA;MACZ,IAAIC,KAAK,IAAI,IAAI,EAAE;MACnB,IAAAC,YAAA,GAAuB,IAAI,CAACC,MAAM,CAACF,KAAK,EAAEP,KAAK,CAAC;QAAxCjD,IAAI,GAAAyD,YAAA,CAAJzD,IAAI;QAAEC,IAAA,GAAAwD,YAAA,CAAAxD,IAAA;MACd,CAAA8C,gBAAA,GAAAnE,CAAC,CAAC+E,aAAa,cAAAZ,gBAAA,eAAfA,gBAAA,CAAiBa,OAAO,CAAC,YAAY,EAAE3D,IAAI,CAAC;MAC5C,CAAA+C,iBAAA,GAAApE,CAAC,CAAC+E,aAAa,cAAAX,iBAAA,eAAfA,iBAAA,CAAiBY,OAAO,CAAC,WAAW,EAAE5D,IAAI,CAAC;MAC3C,IAAIiD,KAAK,EAAE;QACT,IAAAY,qBAAW,EAAC;UAAEL,KAAK,EAALA,KAAK;UAAEpF,KAAK,EAAE,IAAI,CAACA;QAAM,CAAC,CAAC;MAC3C;IACF;;IAEA;AACF;AACA;EAFE;IAAAuB,GAAA;IAAAC,KAAA,EAGQ,SAAAkE,gBAAgBA,CAACC,OAAe,EAAE;MACxC,OACEA,OAAO,CACJC,KAAK,CAAC,OAAO;MACd;MAAA,CACCC,MAAM,CAAE,UAAAC,GAAG;QAAA,OAAKA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;MAAA,EAAC,CAC/BC,IAAI,CAAC,IAAI,CAAC;IAEjB;EAAA;IAAAxE,GAAA;IAAAC,KAAA,EAEA,SAAAd,cAAcA,CAACF,CAAiB,EAAE;MAAA,IAAAwF,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MAChC,IAAI1F,CAAC,CAACsE,gBAAgB,IAAI,CAAC,IAAI,CAAC9E,KAAK,CAACmG,SAAS,CAAC,CAAC,EAAE;MACnD3F,CAAC,CAACuE,cAAc,CAAC,CAAC;MAClB,IAAMK,KAAK,GAAG,IAAI,CAACpF,KAAK,CAACoG,YAAY,CAAC,IAAI,CAAC;MAC3C,IAAIhB,KAAK,IAAI,IAAI,EAAE;MACnB,IAAMxD,IAAI,IAAAoE,iBAAA,GAAGxF,CAAC,CAAC+E,aAAa,cAAAS,iBAAA,uBAAfA,iBAAA,CAAiBK,OAAO,CAAC,WAAW,CAAC;MAClD,IAAIxE,IAAI,IAAAoE,iBAAA,GAAGzF,CAAC,CAAC+E,aAAa,cAAAU,iBAAA,uBAAfA,iBAAA,CAAiBI,OAAO,CAAC,YAAY,CAAC;MACjD,IAAI,CAACzE,IAAI,IAAI,CAACC,IAAI,EAAE;QAAA,IAAAyE,iBAAA;QAClB,IAAMX,OAAO,IAAAW,iBAAA,GAAG9F,CAAC,CAAC+E,aAAa,cAAAe,iBAAA,uBAAfA,iBAAA,CAAiBD,OAAO,CAAC,eAAe,CAAC;QACzD,IAAIV,OAAO,EAAE;UACX9D,IAAI,GAAG,IAAI,CAAC6D,gBAAgB,CAACC,OAAO,CAAC;QACvC;MACF;MACA,IAAMY,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,EAAAP,iBAAA,GAAA1F,CAAC,CAAC+E,aAAa,cAAAW,iBAAA,uBAAfA,iBAAA,CAAiBK,KAAK,KAAI,EAAE,CAAC;MACtD,IAAI,CAAC3E,IAAI,IAAI2E,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;QAC7B,IAAI,CAAChC,KAAK,CAAC0G,QAAQ,CAACC,MAAM,CAACvB,KAAK,EAAEmB,KAAK,CAAC;QACxC;MACF;MACA,IAAI3E,IAAI,IAAI2E,KAAK,CAACvE,MAAM,GAAG,CAAC,EAAE;QAAA,IAAA4E,qBAAA;QAC5B,IAAM3D,GAAG,GAAG,IAAIE,SAAS,CAAC,CAAC,CAACC,eAAe,CAACxB,IAAI,EAAE,WAAW,CAAC;QAC9D,IACEqB,GAAG,CAACK,IAAI,CAACuD,iBAAiB,KAAK,CAAC,IAChC,EAAAD,qBAAA,GAAA3D,GAAG,CAACK,IAAI,CAACwD,iBAAiB,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BG,OAAO,MAAK,KAAK,EAC7C;UACA,IAAI,CAAC/G,KAAK,CAAC0G,QAAQ,CAACC,MAAM,CAACvB,KAAK,EAAEmB,KAAK,CAAC;UACxC;QACF;MACF;MACA,IAAI,CAACS,OAAO,CAAC5B,KAAK,EAAE;QAAExD,IAAI,EAAJA,IAAI;QAAEC,IAAA,EAAAA;MAAK,CAAC,CAAC;IACrC;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAGA,SAAA8D,MAAMA,CAACF,KAAY,EAAE;MACnB,IAAMvD,IAAI,GAAG,IAAI,CAAC7B,KAAK,CAACiH,OAAO,CAAC7B,KAAK,CAAC;MACtC,IAAMxD,IAAI,GAAG,IAAI,CAAC5B,KAAK,CAACkH,eAAe,CAAC9B,KAAK,CAAC;MAC9C,OAAO;QAAExD,IAAI,EAAJA,IAAI;QAAEC,IAAA,EAAAA;MAAK,CAAC;IACvB;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAEA,SAAAwF,OAAOA,CAAC5B,KAAY,EAAA+B,KAAA,EAAoD;MAAA,IAAhDtF,IAAI,GAA0CsF,KAAA,CAA9CtF,IAAI;QAAED,IAAA,GAAwCuF,KAAA,CAAxCvF,IAAA;MAC5B,IAAME,OAAO,GAAG,IAAI,CAAC9B,KAAK,CAACoH,SAAS,CAAChC,KAAK,CAACnB,KAAK,CAAC;MACjD,IAAMoD,WAAW,GAAG,IAAI,CAAC3F,OAAO,CAAC;QAAEG,IAAI,EAAJA,IAAI;QAAED,IAAA,EAAAA;MAAK,CAAC,EAAEE,OAAO,CAAC;MACzDlE,KAAK,CAAC0J,GAAG,CAAC,SAAS,EAAED,WAAW,EAAE;QAAExF,IAAI,EAAJA,IAAI;QAAED,IAAA,EAAAA;MAAK,CAAC,CAAC;MACjD,IAAMW,KAAK,GAAG,IAAIH,mBAAK,CAAC,CAAC,CACtBU,MAAM,CAACsC,KAAK,CAACnB,KAAK,CAAC,CACnBlB,MAAM,CAACqC,KAAK,CAACpD,MAAM,CAAC,CACpBnB,MAAM,CAACwG,WAAW,CAAC;MACtB,IAAI,CAACrH,KAAK,CAAC0E,cAAc,CAACnC,KAAK,EAAE4B,cAAK,CAACC,OAAO,CAACmD,IAAI,CAAC;MACpD;MACA,IAAI,CAACvH,KAAK,CAACuE,YAAY,CACrBhC,KAAK,CAACP,MAAM,CAAC,CAAC,GAAGoD,KAAK,CAACpD,MAAM,EAC7BmC,cAAK,CAACC,OAAO,CAACI,MAChB,CAAC;MACD,IAAI,CAACxE,KAAK,CAACwH,uBAAuB,CAAC,CAAC;IACtC;EAAA;IAAAjG,GAAA;IAAAC,KAAA,EAEA,SAAAkC,eAAeA,CAACL,SAAkB,EAAEE,WAAqC,EAAE;MACzE,IAAMK,eAA0B,GAAG,EAAE;MACrC,IAAMC,YAAuB,GAAG,EAAE;MAClC,IAAI,CAACjD,QAAQ,CAACE,OAAO,CAAE,UAAA2G,IAAI,EAAK;QAC9B,IAAAC,KAAA,OAAAzG,eAAA,CAAAnB,OAAA,EAA4B2H,IAAI;UAAzBvG,QAAQ,GAAAwG,KAAA;UAAEvG,OAAO,GAAAuG,KAAA;QACxB,QAAQxG,QAAQ;UACd,KAAKnD,IAAI,CAACC,SAAS;YACjB6F,YAAY,CAACpC,IAAI,CAACN,OAAO,CAAC;YAC1B;UACF,KAAKpD,IAAI,CAACK,YAAY;YACpBwF,eAAe,CAACnC,IAAI,CAACN,OAAO,CAAC;YAC7B;UACF;YACEqF,KAAK,CAACC,IAAI,CAACpD,SAAS,CAACsE,gBAAgB,CAACzG,QAAQ,CAAC,CAAC,CAACJ,OAAO,CAAE,UAAA8G,IAAI,EAAK;cACjE,IAAIrE,WAAW,CAACsE,GAAG,CAACD,IAAI,CAAC,EAAE;gBACzB,IAAME,OAAO,GAAGvE,WAAW,CAACwE,GAAG,CAACH,IAAI,CAAC;gBACrCE,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAErG,IAAI,CAACN,OAAO,CAAC;cACxB,CAAC,MAAM;gBACLoC,WAAW,CAACyE,GAAG,CAACJ,IAAI,EAAE,CAACzG,OAAO,CAAC,CAAC;cAClC;YACF,CAAC,CAAC;YACF;QACJ;MACF,CAAC,CAAC;MACF,OAAO,CAACyC,eAAe,EAAEC,YAAY,CAAC;IACxC;EAAA;AAAA,EA3MsBoE,eAAM;AAAA,IAAA3F,gBAAA,CAAAxC,OAAA,EAAxBF,SAAS,cACuB;EAClCgB,QAAQ,EAAE;AACZ,CAAC;AA2MH,SAASsH,WAAWA,CAClB3F,KAAY,EACZ4F,MAAc,EACd3G,KAAc,EACduC,MAAkB,EACX;EACP,IAAI,CAACA,MAAM,CAACqE,KAAK,CAACD,MAAM,CAAC,EAAE;IACzB,OAAO5F,KAAK;EACd;EAEA,OAAOA,KAAK,CAACtD,MAAM,CAAC,UAACoJ,QAAQ,EAAEC,EAAE,EAAK;IACpC,IAAI,CAACA,EAAE,CAACjG,MAAM,EAAE,OAAOgG,QAAQ;IAC/B,IAAIC,EAAE,CAAC3F,UAAU,IAAI2F,EAAE,CAAC3F,UAAU,CAACwF,MAAM,CAAC,EAAE;MAC1C,OAAOE,QAAQ,CAAC5G,IAAI,CAAC6G,EAAE,CAAC;IAC1B;IACA,IAAMxG,OAAO,GAAGN,KAAK,OAAAc,gBAAA,CAAAxC,OAAA,MAAMqI,MAAM,EAAG3G,KAAA,IAAU,CAAC,CAAC;IAChD,OAAO6G,QAAQ,CAAChG,MAAM,CAACiG,EAAE,CAACjG,MAAM,MAAAkG,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAOgC,OAAO,GAAKwG,EAAE,CAAC3F,UAAA,CAAY,CAAC;EACrE,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAASK,aAAaA,CAACF,KAAY,EAAEV,IAAY,EAAE;EACjD,IAAI2G,OAAO,GAAG,EAAE;EAChB,KACE,IAAIC,CAAC,GAAGlG,KAAK,CAACG,GAAG,CAACV,MAAM,GAAG,CAAC,EAC5ByG,CAAC,IAAI,CAAC,IAAID,OAAO,CAACxG,MAAM,GAAGH,IAAI,CAACG,MAAM,EACtC,EAAEyG,CAAC,CAAC;EAAA,EACJ;IACA,IAAMH,EAAE,GAAG/F,KAAK,CAACG,GAAG,CAAC+F,CAAC,CAAC;IACvB,IAAI,OAAOH,EAAE,CAACjG,MAAM,KAAK,QAAQ,EAAE;IACnCmG,OAAO,GAAGF,EAAE,CAACjG,MAAM,GAAGmG,OAAO;EAC/B;EACA,OAAOA,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,GAAG7G,IAAI,CAACG,MAAM,CAAC,KAAKH,IAAI;AACjD;AAEA,SAAS8G,MAAMA,CAACf,IAAU,EAAE7D,MAAkB,EAAE;EAC9C,IAAI,EAAE6D,IAAI,YAAYgB,OAAO,CAAC,EAAE,OAAO,KAAK;EAC5C,IAAMC,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC;EACA,IAAIiB,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAYC,oBAAS,EAAE,OAAO,KAAK;EAE/D,OAAO,CACL,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,KAAK,EACL,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAACC,QAAQ,CAACpB,IAAI,CAACb,OAAO,CAACkC,WAAW,CAAC,CAAC,CAAC;AACxC;AAEA,SAASC,uBAAuBA,CAACtB,IAAiB,EAAE7D,MAAkB,EAAE;EACtE,OACE6D,IAAI,CAACuB,sBAAsB,IAC3BvB,IAAI,CAACwB,kBAAkB,IACvB,CAACT,MAAM,CAACf,IAAI,CAACuB,sBAAsB,EAAEpF,MAAM,CAAC,IAC5C,CAAC4E,MAAM,CAACf,IAAI,CAACwB,kBAAkB,EAAErF,MAAM,CAAC;AAE5C;AAEA,IAAMsF,QAAQ,GAAG,IAAI7F,OAAO,CAAC,CAAC;AAC9B,SAAS8F,KAAKA,CAAC1B,IAAiB,EAAE;EAChC,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,KAAK;EAC9B,IAAI,CAACyB,QAAQ,CAACxB,GAAG,CAACD,IAAI,CAAC,EAAE;IACvB;IACA,IAAIA,IAAI,CAACb,OAAO,KAAK,KAAK,EAAE;MAC1BsC,QAAQ,CAACrB,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC;IAC1B,CAAC,MAAM;MACLyB,QAAQ,CAACrB,GAAG,CAACJ,IAAI,EAAE0B,KAAK,CAAC1B,IAAI,CAAC2B,UAAU,CAAC,CAAC;IAC5C;EACF;EACA,OAAOF,QAAQ,CAACtB,GAAG,CAACH,IAAI,CAAC;AAC3B;AAEA,SAAS9D,QAAQA,CACfC,MAAkB,EAClB6D,IAAe,EACfhE,eAA0B,EAC1BC,YAAuB,EACvBN,WAAqC,EAC9B;EACP;EACA,IAAIqE,IAAI,CAAC4B,QAAQ,KAAK5B,IAAI,CAAC5J,SAAS,EAAE;IACpC,OAAO6F,YAAY,CAAC5E,MAAM,CAAC,UAACsD,KAAY,EAAEpB,OAAO,EAAK;MACpD,OAAOA,OAAO,CAACyG,IAAI,EAAErF,KAAK,EAAEwB,MAAM,CAAC;IACrC,CAAC,EAAE,IAAI3B,mBAAK,CAAC,CAAC,CAAC;EACjB;EACA,IAAIwF,IAAI,CAAC4B,QAAQ,KAAK5B,IAAI,CAACxJ,YAAY,EAAE;IACvC,OAAOoI,KAAK,CAACC,IAAI,CAACmB,IAAI,CAAC6B,UAAU,IAAI,EAAE,CAAC,CAACxK,MAAM,CAAC,UAACsD,KAAK,EAAEmH,SAAS,EAAK;MACpE,IAAIC,aAAa,GAAG7F,QAAQ,CAC1BC,MAAM,EACN2F,SAAS,EACT9F,eAAe,EACfC,YAAY,EACZN,WACF,CAAC;MACD,IAAImG,SAAS,CAACF,QAAQ,KAAK5B,IAAI,CAACxJ,YAAY,EAAE;QAC5CuL,aAAa,GAAG/F,eAAe,CAAC3E,MAAM,CAAC,UAAC2K,YAAY,EAAEzI,OAAO,EAAK;UAChE,OAAOA,OAAO,CAACuI,SAAS,EAAiBE,YAAY,EAAE7F,MAAM,CAAC;QAChE,CAAC,EAAE4F,aAAa,CAAC;QACjBA,aAAa,GAAG,CAACpG,WAAW,CAACwE,GAAG,CAAC2B,SAAS,CAAC,IAAI,EAAE,EAAEzK,MAAM,CACvD,UAAC2K,YAAY,EAAEzI,OAAO,EAAK;UACzB,OAAOA,OAAO,CAACuI,SAAS,EAAEE,YAAY,EAAE7F,MAAM,CAAC;QACjD,CAAC,EACD4F,aACF,CAAC;MACH;MACA,OAAOpH,KAAK,CAAC1B,MAAM,CAAC8I,aAAa,CAAC;IACpC,CAAC,EAAE,IAAIvH,mBAAK,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,IAAIA,mBAAK,CAAC,CAAC;AACpB;AAEA,SAASxD,gBAAgBA,CAACuJ,MAAc,EAAE;EACxC,OAAO,UAAC0B,KAAc,EAAEtH,KAAY,EAAEwB,MAAkB,EAAK;IAC3D,OAAOmE,WAAW,CAAC3F,KAAK,EAAE4F,MAAM,EAAE,IAAI,EAAEpE,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,SAASzF,eAAeA,CAACsJ,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC5E,IAAMpB,UAAU,GAAGmH,qBAAU,CAACC,IAAI,CAACnC,IAAI,CAAC;EACxC,IAAMoC,OAAO,GAAGC,0BAAe,CAACF,IAAI,CAACnC,IAAI,CAAC;EAC1C,IAAMsC,MAAM,GAAGC,0BAAe,CAACJ,IAAI,CAACnC,IAAI,CAAC;EACzC,IAAM9F,OAA2C,GAAG,CAAC,CAAC;EACtDa,UAAU,CACP9B,MAAM,CAACmJ,OAAO,CAAC,CACfnJ,MAAM,CAACqJ,MAAM,CAAC,CACdpJ,OAAO,CAAE,UAAAsJ,IAAI,EAAK;IACjB,IAAIjL,IAAI,GAAG4E,MAAM,CAACqE,KAAK,CAACgC,IAAI,EAAEC,gBAAK,CAACC,SAAS,CAAe;IAC5D,IAAInL,IAAI,IAAI,IAAI,EAAE;MAChB2C,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC;MACzC,IAAI9F,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,EAAE;IAC9B;IACApL,IAAI,GAAGL,qBAAqB,CAACsL,IAAI,CAAC;IAClC,IAAIjL,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACoL,QAAQ,KAAKH,IAAI,IAAIjL,IAAI,CAACC,OAAO,KAAKgL,IAAI,CAAC,EAAE;MACrEtI,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC,IAAI3F,SAAS;IACxD;IACA9C,IAAI,GAAGE,iBAAiB,CAAC+K,IAAI,CAAC;IAC9B,IAAIjL,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACoL,QAAQ,KAAKH,IAAI,IAAIjL,IAAI,CAACC,OAAO,KAAKgL,IAAI,CAAC,EAAE;MACrEjL,IAAI,GAAGE,iBAAiB,CAAC+K,IAAI,CAAC;MAC9BtI,OAAO,CAAC3C,IAAI,CAACoL,QAAQ,CAAC,GAAGpL,IAAI,CAACqC,KAAK,CAACoG,IAAI,CAAC,IAAI3F,SAAS;IACxD;EACF,CAAC,CAAC;EAEJ,OAAOuI,MAAM,CAACC,OAAO,CAAC3I,OAAO,CAAC,CAAC7C,MAAM,CACnC,UAACoJ,QAAQ,EAAAqC,KAAA;IAAA,IAAAC,KAAA,OAAA1J,eAAA,CAAAnB,OAAA,EAAe4K,KAAA;MAAZN,IAAI,GAAAO,KAAA;MAAEnJ,KAAK,GAAAmJ,KAAA;IAAC,OAAKzC,WAAW,CAACG,QAAQ,EAAE+B,IAAI,EAAE5I,KAAK,EAAEuC,MAAM,CAAC;EAAA,GACvExB,KACF,CAAC;AACH;AAEA,SAASlE,SAASA,CAACuJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC/D,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC,IAAIiB,KAAK,IAAI,IAAI,EAAE,OAAOtG,KAAK;EAC/B;EACA,IAAIsG,KAAK,CAACC,SAAS,YAAYC,oBAAS,EAAE;IACxC,IAAM6B,KAAK,GAAG,CAAC,CAAC;IAChB;IACA,IAAMpJ,KAAK,GAAGqH,KAAK,CAACrH,KAAK,CAACoG,IAAI,CAAC;IAC/B,IAAIpG,KAAK,IAAI,IAAI,EAAE;MACjB;MACAoJ,KAAK,CAAC/B,KAAK,CAAC1G,QAAQ,CAAC,GAAGX,KAAK;MAC7B;MACA,OAAO,IAAIY,mBAAK,CAAC,CAAC,CAACC,MAAM,CAACuI,KAAK,EAAE/B,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,CAAC;IAC/D;EACF,CAAC,MAAM;IACL;IACA,IAAI8E,KAAK,CAACC,SAAS,YAAY+B,oBAAS,IAAI,CAACpI,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;MACvEA,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;IACpB;IACA,IACE,UAAU,IAAIwG,KAAK,IACnB,SAAS,IAAIA,KAAK,IAClB,OAAOA,KAAK,CAAC/G,OAAO,KAAK,UAAU,EACnC;MACA,OAAOoG,WAAW,CAChB3F,KAAK,EACLsG,KAAK,CAAC1G,QAAQ,EACd0G,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,EAC3BA,MACF,CAAC;IACH;EACF;EACA,OAAOxB,KAAK;AACd;AAEA,SAASpE,UAAUA,CAACyJ,IAAU,EAAErF,KAAY,EAAE;EAC5C,IAAI,CAACE,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/BA,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;EACpB;EACA,OAAOE,KAAK;AACd;AAEA,SAAS7D,cAAcA,CAACkJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EACpE,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAAC,YAAY,CAAC;EACxC,IAAM0C,QAAQ,GACZjC,KAAK,IAAI,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAAC/G,OAAO,KAAK,UAAU,GAC9D+G,KAAK,CAAC/G,OAAO,CAAC8F,IAAI,EAAE7D,MAAM,CAAC,GAC3B,IAAI;EACV,OAAOmE,WAAW,CAAC3F,KAAK,EAAE,YAAY,EAAEuI,QAAQ,EAAE/G,MAAM,CAAC;AAC3D;AAEA,SAASlF,WAAWA,CAAA,EAAG;EACrB,OAAO,IAAIuD,mBAAK,CAAC,CAAC;AACpB;AAEA,SAAS5D,WAAWA,CAACoJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EACjE,IAAM8E,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACR,IAAI,CAAC;EAChC,IACEiB,KAAK,IAAI,IAAI;EACb;EACAA,KAAK,CAAC1G,QAAQ,KAAK,MAAM,IACzB,CAACM,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAC3B;IACA,OAAOA,KAAK;EACd;EACA,IAAIwI,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,MAAM,GAAGpD,IAAI,CAAC2B,UAAU;EAC5B,OAAOyB,MAAM,IAAI,IAAI,EAAE;IACrB;IACA,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAChC,QAAQ,CAACgC,MAAM,CAACjE,OAAO,CAAC,EAAE;MACzCgE,MAAM,IAAI,CAAC;IACb;IACAC,MAAM,GAAGA,MAAM,CAACzB,UAAU;EAC5B;EACA,IAAIwB,MAAM,IAAI,CAAC,EAAE,OAAOxI,KAAK;EAC7B,OAAOA,KAAK,CAACtD,MAAM,CAAC,UAACgM,QAAQ,EAAE3C,EAAE,EAAK;IACpC,IAAI,CAACA,EAAE,CAACjG,MAAM,EAAE,OAAO4I,QAAQ;IAC/B,IAAI3C,EAAE,CAAC3F,UAAU,IAAI,OAAO2F,EAAE,CAAC3F,UAAU,CAACoI,MAAM,KAAK,QAAQ,EAAE;MAC7D,OAAOE,QAAQ,CAACxJ,IAAI,CAAC6G,EAAE,CAAC;IAC1B;IACA,OAAO2C,QAAQ,CAAC5I,MAAM,CAACiG,EAAE,CAACjG,MAAM,MAAAkG,cAAA,CAAAzI,OAAA;MAAIiL,MAAM,EAANA;IAAM,GAAMzC,EAAE,CAAC3F,UAAU,IAAI,CAAC,CAAC,CAAG,CAAC;EACzE,CAAC,EAAE,IAAIP,mBAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAAS3D,SAASA,CAACmJ,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAC/D,IAAMmH,OAAO,GAAGtD,IAAe;EAC/B,IAAIuD,IAAI,GAAGD,OAAO,CAACnE,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,QAAQ;EAE1D,IAAMqE,WAAW,GAAGF,OAAO,CAACG,YAAY,CAAC,cAAc,CAAC;EACxD,IAAID,WAAW,EAAE;IACfD,IAAI,GAAGC,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,WAAW;EACzD;EAEA,OAAOlD,WAAW,CAAC3F,KAAK,EAAE,MAAM,EAAE4I,IAAI,EAAEpH,MAAM,CAAC;AACjD;AAEA,SAAS7F,YAAYA,CAAC0J,IAAU,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAClE,IAAI,CAACtB,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/B,IACEoG,MAAM,CAACf,IAAI,EAAE7D,MAAM,CAAC,KACnB6D,IAAI,CAAC6B,UAAU,CAACzH,MAAM,GAAG,CAAC,IAAI4F,IAAI,YAAY0D,oBAAoB,CAAC,EACpE;MACA,OAAO/I,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;IAC3B;IACA,IAAIE,KAAK,CAACP,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI4F,IAAI,CAAC2D,WAAW,EAAE;MAC1C,IAAIA,WAAwB,GAAG3D,IAAI,CAAC2D,WAAW;MAC/C,OAAOA,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAI5C,MAAM,CAAC4C,WAAW,EAAExH,MAAM,CAAC,EAAE;UAC/B,OAAOxB,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;QAC3B;QACA,IAAMwG,KAAK,GAAG9E,MAAM,CAACqE,KAAK,CAACmD,WAAW,CAAC;QACvC;QACA,IAAI1C,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAY0C,iBAAU,EAAE;UAClD,OAAOjJ,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC;QAC3B;QACAkJ,WAAW,GAAGA,WAAW,CAACE,UAAU;MACtC;IACF;EACF;EACA,OAAOlJ,KAAK;AACd;AAEA,SAAShE,WAAWA,CAACqJ,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAAA,IAAA2H,iBAAA;EACxE,IAAM5J,OAAgC,GAAG,CAAC,CAAC;EAC3C,IAAM6J,KAAmC,GAAG/D,IAAI,CAAC+D,KAAK,IAAI,CAAC,CAAC;EAC5D,IAAIA,KAAK,CAACC,SAAS,KAAK,QAAQ,EAAE;IAChC9J,OAAO,CAAC+J,MAAM,GAAG,IAAI;EACvB;EACA,IAAIF,KAAK,CAACG,cAAc,KAAK,WAAW,EAAE;IACxChK,OAAO,CAACiK,SAAS,GAAG,IAAI;EAC1B;EACA,IAAIJ,KAAK,CAACG,cAAc,KAAK,cAAc,EAAE;IAC3ChK,OAAO,CAACkK,MAAM,GAAG,IAAI;EACvB;EACA,IACE,CAAAN,iBAAA,GAAAC,KAAK,CAACM,UAAU,cAAAP,iBAAA,eAAhBA,iBAAA,CAAkBQ,UAAU,CAAC,MAAM,CAAC;EACpC;EACAC,QAAQ,CAACR,KAAK,CAACM,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,EACrC;IACAnK,OAAO,CAACsK,IAAI,GAAG,IAAI;EACrB;EACA7J,KAAK,GAAGiI,MAAM,CAACC,OAAO,CAAC3I,OAAO,CAAC,CAAC7C,MAAM,CACpC,UAACoJ,QAAQ,EAAAgE,KAAA;IAAA,IAAAC,KAAA,OAAArL,eAAA,CAAAnB,OAAA,EAAeuM,KAAA;MAAZjC,IAAI,GAAAkC,KAAA;MAAE9K,KAAK,GAAA8K,KAAA;IAAC,OAAKpE,WAAW,CAACG,QAAQ,EAAE+B,IAAI,EAAE5I,KAAK,EAAEuC,MAAM,CAAC;EAAA,GACvExB,KACF,CAAC;EACD;EACA,IAAIgK,UAAU,CAACZ,KAAK,CAACa,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzC;IACA,OAAO,IAAIpK,mBAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC,CAACxB,MAAM,CAAC0B,KAAK,CAAC;EAC/C;EACA,OAAOA,KAAK;AACd;AAEA,SAAS5D,UAAUA,CACjBiJ,IAAyB,EACzBrF,KAAY,EACZwB,MAAkB,EAClB;EAAA,IAAA0I,mBAAA,EAAAC,oBAAA;EACA,IAAM9J,KAAK,GACT,EAAA6J,mBAAA,GAAA7E,IAAI,CAAC+E,aAAa,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoB1F,OAAO,MAAK,OAAO,GACnCa,IAAI,CAAC+E,aAAa,IAAAD,oBAAA,GAClB9E,IAAI,CAAC+E,aAAa,cAAAD,oBAAA,uBAAlBA,oBAAA,CAAoBC,aAAa;EACvC,IAAI/J,KAAK,IAAI,IAAI,EAAE;IACjB,IAAMgK,IAAI,GAAGpG,KAAK,CAACC,IAAI,CAAC7D,KAAK,CAAC+E,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD,IAAMkF,GAAG,GAAGD,IAAI,CAACE,OAAO,CAAClF,IAAI,CAAC,GAAG,CAAC;IAClC,OAAOM,WAAW,CAAC3F,KAAK,EAAE,OAAO,EAAEsK,GAAG,EAAE9I,MAAM,CAAC;EACjD;EACA,OAAOxB,KAAK;AACd;AAEA,SAAStE,SAASA,CAAC2J,IAAiB,EAAErF,KAAY,EAAEwB,MAAkB,EAAE;EAAA,IAAAgJ,oBAAA;EACtE;EACA,IAAIlL,IAAI,GAAG+F,IAAI,CAACoF,IAAI;EACpB;EACA,IAAI,EAAAD,oBAAA,GAAAnF,IAAI,CAAC+E,aAAa,cAAAI,oBAAA,uBAAlBA,oBAAA,CAAoBhG,OAAO,MAAK,KAAK,EAAE;IACzC,OAAOxE,KAAK,CAACF,MAAM,CAACR,IAAI,CAACoL,IAAI,CAAC,CAAC,CAAC;EAClC;EACA,IAAI,CAAC3D,KAAK,CAAC1B,IAAI,CAAC,EAAE;IAChB,IACE/F,IAAI,CAACoL,IAAI,CAAC,CAAC,CAACjL,MAAM,KAAK,CAAC,IACxBH,IAAI,CAACmH,QAAQ,CAAC,IAAI,CAAC,IACnB,CAACE,uBAAuB,CAACtB,IAAI,EAAE7D,MAAM,CAAC,EACtC;MACA,OAAOxB,KAAK;IACd;IACA,IAAM2K,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAiB,EAAEtE,KAAa,EAAK;MACrD,IAAMuE,QAAQ,GAAGvE,KAAK,CAACwE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;MAClD,OAAOD,QAAQ,CAACpL,MAAM,GAAG,CAAC,IAAImL,QAAQ,GAAG,GAAG,GAAGC,QAAQ;IACzD,CAAC;IACDvL,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACrDxL,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,QAAQ,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9D,IACGtF,IAAI,CAAC0F,eAAe,IAAI,IAAI,IAC3B1F,IAAI,CAAC+E,aAAa,IAAI,IAAI,IAC1BhE,MAAM,CAACf,IAAI,CAAC+E,aAAa,EAAE5I,MAAM,CAAC,IACnC6D,IAAI,CAAC0F,eAAe,YAAY1E,OAAO,IACtCD,MAAM,CAACf,IAAI,CAAC0F,eAAe,EAAEvJ,MAAM,CAAE,EACvC;MACAlC,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,MAAM,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7D;IACA,IACGtF,IAAI,CAAC2D,WAAW,IAAI,IAAI,IACvB3D,IAAI,CAAC+E,aAAa,IAAI,IAAI,IAC1BhE,MAAM,CAACf,IAAI,CAAC+E,aAAa,EAAE5I,MAAM,CAAC,IACnC6D,IAAI,CAAC2D,WAAW,YAAY3C,OAAO,IAAID,MAAM,CAACf,IAAI,CAAC2D,WAAW,EAAExH,MAAM,CAAE,EACzE;MACAlC,IAAI,GAAGA,IAAI,CAACwL,OAAO,CAAC,MAAM,EAAEH,QAAQ,CAACvM,IAAI,CAACuM,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC7D;EACF;EACA,OAAO3K,KAAK,CAACF,MAAM,CAACR,IAAI,CAAC;AAC3B", "ignoreList": []}]}