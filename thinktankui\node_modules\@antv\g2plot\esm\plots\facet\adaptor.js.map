{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/facet/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAGvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE1C,SAAS,YAAY,CAAC,MAA4B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAM,SAAS,GAA6B,OAAO,KAApC,EAAE,IAAI,GAAuB,OAAO,KAA9B,EAAE,MAAM,GAAe,OAAO,OAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAE5D,IAAM,YAAY,GAAG,IAAI,CAAC,OAAc,EAAE;QACxC,MAAM;QACN,MAAM;QACN,QAAQ;QACR,UAAU;QACV,MAAM;QACN,MAAM;QACN,SAAS;QACT,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,cAAc;QACd,aAAa;KACd,CAAC,CAAC;IAEH,UAAU;IACV,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,WAAW;IACX,KAAK,CAAC,KAAK,CAAC,SAAS,wBAChB,YAAY,KACf,MAAM,QAAA,EACN,QAAQ,EAAE,UAAC,QAAQ,EAAE,KAAK;YACxB,IAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC9C,IAAK,WAAqB,CAAC,UAAU,EAAE;gBACrC,eAAe,CAAC,QAAQ,EAAE,WAAoB,CAAC,CAAC;aACjD;iBAAM;gBACL,IAAM,IAAI,GAAG,WAAoB,CAAC;gBAClC,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;gBACjC,0BAA0B;gBAC1B,IAAI,WAAW,CAAC,OAAO,EAAE;oBACvB,gBAAgB;oBAChB,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;iBACjC;gBACD,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;aACnD;QACH,CAAC,IACD,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,MAA4B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA0E,OAAO,KAAjF,EAAE,IAAI,GAAoE,OAAO,KAA3E,EAAE,OAAO,GAA2D,OAAO,QAAlE,EAAE,UAAU,GAA+C,OAAO,WAAtD,EAAE,KAAK,GAAwC,OAAO,MAA/C,EAAE,MAAM,GAAgC,OAAO,OAAvC,EAAE,YAAY,GAAkB,OAAO,aAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAE9F,aAAa;IACb,IAAI,MAAM,GAAwB,EAAE,CAAC;IACrC,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;YACnC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IACtC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB,mBAAmB;IACnB,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAE7B,sBAAsB;IACtB,IAAI,CAAC,IAAI,EAAE;QACT,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnB;SAAM;QACL,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;YACnC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;KACJ;IAED,gBAAgB;IAChB,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC7B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KACxB;SAAM,IAAI,OAAO,KAAK,KAAK,EAAE;QAC5B,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;KACpC;IAED,qBAAqB;IACrB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAErB,WAAW;IACX,IAAI,KAAK,EAAE;QACT,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACpB;IAED,kBAAkB;IAClB,IAAI,CAAC,YAAY,EAAE,UAAC,WAAwB;QAC1C,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK,EAAE;YAChC,KAAK,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SAC3C;aAAM;YACL,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;SACtD;IACH,CAAC,CAAC,CAAC;IAEH,iBAAiB;IACjB,IAAI,CAAC,WAAW,EAAE,UAAC,UAAU;QAC3B,KAAK,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,cAC9B,UAAU,EACb,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAClD,0BAA0B;IAC1B,OAAO,IAAI,CAAC,KAAK,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AACtD,CAAC", "sourcesContent": ["import { each, omit } from '@antv/util';\nimport { theme } from '../../adaptor/common';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { Axis, Interaction } from '../../types';\nimport { deepAssign, flow, pick } from '../../utils';\nimport { execPlotAdaptor } from '../mix/utils';\nimport { FacetOptions, IPlot, IView } from './types';\nimport { execViewAdaptor } from './utils';\n\nfunction facetAdaptor(params: Params<FacetOptions>): Params<FacetOptions> {\n  const { chart, options } = params;\n  const { type: facetType, data, fields, eachView } = options;\n\n  const restFacetCfg = omit(options as any, [\n    'type',\n    'data',\n    'fields',\n    'eachView',\n    'axes',\n    'meta',\n    'tooltip',\n    'coordinate',\n    'theme',\n    'legend',\n    'interactions',\n    'annotations',\n  ]);\n\n  // 1. data\n  chart.data(data);\n\n  // 2. facet\n  chart.facet(facetType, {\n    ...restFacetCfg,\n    fields,\n    eachView: (viewOfG2, facet) => {\n      const viewOptions = eachView(viewOfG2, facet);\n      if ((viewOptions as IView).geometries) {\n        execViewAdaptor(viewOfG2, viewOptions as IView);\n      } else {\n        const plot = viewOptions as IPlot;\n        const plotOptions = plot.options;\n        // @ts-ignore 仪表盘没 tooltip\n        if (plotOptions.tooltip) {\n          // 配置 tooltip 交互\n          viewOfG2.interaction('tooltip');\n        }\n        execPlotAdaptor(plot.type, viewOfG2, plotOptions);\n      }\n    },\n  });\n\n  return params;\n}\n\nfunction component(params: Params<FacetOptions>): Params<FacetOptions> {\n  const { chart, options } = params;\n  const { axes, meta, tooltip, coordinate, theme, legend, interactions, annotations } = options;\n\n  // 3. meta 配置\n  let scales: Record<string, any> = {};\n  if (axes) {\n    each(axes, (axis: Axis, field: string) => {\n      scales[field] = pick(axis, AXIS_META_CONFIG_KEYS);\n    });\n  }\n\n  scales = deepAssign({}, meta, scales);\n  chart.scale(scales);\n\n  // 4. coordinate 配置\n  chart.coordinate(coordinate);\n\n  // 5. axis 轴配置 (默认不展示)\n  if (!axes) {\n    chart.axis(false);\n  } else {\n    each(axes, (axis: Axis, field: string) => {\n      chart.axis(field, axis);\n    });\n  }\n\n  // 6. tooltip 配置\n  if (tooltip) {\n    chart.interaction('tooltip');\n    chart.tooltip(tooltip);\n  } else if (tooltip === false) {\n    chart.removeInteraction('tooltip');\n  }\n\n  // 7. legend 配置（默认展示）\n  chart.legend(legend);\n\n  // theme 配置\n  if (theme) {\n    chart.theme(theme);\n  }\n\n  // 8. interactions\n  each(interactions, (interaction: Interaction) => {\n    if (interaction.enable === false) {\n      chart.removeInteraction(interaction.type);\n    } else {\n      chart.interaction(interaction.type, interaction.cfg);\n    }\n  });\n\n  // 9. annotations\n  each(annotations, (annotation) => {\n    chart.annotation()[annotation.type]({\n      ...annotation,\n    });\n  });\n\n  return params;\n}\n\n/**\n * 分面图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<FacetOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(theme, facetAdaptor, component)(params);\n}\n"]}