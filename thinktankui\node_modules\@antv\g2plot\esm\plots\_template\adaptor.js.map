{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/_template/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAE5E,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAGnC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA+B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAqB,OAAO,KAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,UAAG,MAAM,cAAI,MAAM,CAAE,CAAC,CAAC;IAEjD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;;IAC1C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AACD;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,SAAS;IACT,uBAAuB;KACxB,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { animation, interaction, scale, theme } from '../../adaptor/common';\nimport { Params } from '../../core/adaptor';\nimport { flow } from '../../utils';\nimport { TemplateOptions } from './types';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<TemplateOptions>): Params<TemplateOptions> {\n  const { chart, options } = params;\n  const { data, xField, yField } = options;\n\n  chart.data(data);\n\n  chart.interval().position(`${xField}*${yField}`);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<TemplateOptions>): Params<TemplateOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(params);\n}\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<TemplateOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    geometry,\n    meta,\n    interaction,\n    animation\n    // ... 其他的 adaptor flow\n  )(params);\n}\n"]}