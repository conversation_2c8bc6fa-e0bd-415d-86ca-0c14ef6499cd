{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\Radar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\Radar.vue", "mtime": 1749109381342}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVmlzZXIgZnJvbSAndmlzZXItdnVlJzsNCmltcG9ydCBWdWUgZnJvbSAndnVlJzsNCg0KVnVlLnVzZShWaXNlcik7DQoNCmNvbnN0IGF4aXMxT3B0cyA9IHsNCiAgZGF0YUtleTogJ2l0ZW0nLA0KICBsaW5lOiBudWxsLA0KICB0aWNrTGluZTogbnVsbCwNCiAgZ3JpZDogew0KICAgIGxpbmVTdHlsZTogew0KICAgICAgbGluZURhc2g6IG51bGwNCiAgICB9LA0KICAgIGhpZGVGaXJzdExpbmU6IGZhbHNlDQogIH0NCn0NCmNvbnN0IGF4aXMyT3B0cyA9IHsNCiAgZGF0YUtleTogJ3Njb3JlJywNCiAgbGluZTogbnVsbCwNCiAgdGlja0xpbmU6IG51bGwsDQogIGdyaWQ6IHsNCiAgICB0eXBlOiAncG9seWdvbicsDQogICAgbGluZVN0eWxlOiB7DQogICAgICBsaW5lRGFzaDogbnVsbA0KICAgIH0NCiAgfQ0KfQ0KDQpjb25zdCBzY2FsZSA9IFsNCiAgew0KICAgIGRhdGFLZXk6ICdzY29yZScsDQogICAgbWluOiAwLA0KICAgIG1heDogODANCiAgfSwgew0KICAgIGRhdGFLZXk6ICd1c2VyJywNCiAgICBhbGlhczogJ+exu+WeiycNCiAgfQ0KXQ0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSYWRhcicsDQogIHByb3BzOiB7DQogICAgZGF0YTogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiBudWxsDQogICAgfQ0KICB9LA0KICBkYXRhICgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYXhpczFPcHRzLA0KICAgICAgYXhpczJPcHRzLA0KICAgICAgc2NhbGUNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Radar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Radar.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <v-chart :forceFit=\"true\" height=\"400\" :data=\"data\" :padding=\"[20, 20, 95, 20]\" :scale=\"scale\">\r\n    <v-tooltip></v-tooltip>\r\n    <v-axis :dataKey=\"axis1Opts.dataKey\" :line=\"axis1Opts.line\" :tickLine=\"axis1Opts.tickLine\" :grid=\"axis1Opts.grid\" />\r\n    <v-axis :dataKey=\"axis2Opts.dataKey\" :line=\"axis2Opts.line\" :tickLine=\"axis2Opts.tickLine\" :grid=\"axis2Opts.grid\" />\r\n    <v-legend dataKey=\"user\" marker=\"circle\" :offset=\"30\" />\r\n    <v-coord type=\"polar\" radius=\"0.8\" />\r\n    <v-line position=\"item*score\" color=\"user\" :size=\"2\" />\r\n    <v-point position=\"item*score\" color=\"user\" :size=\"4\" shape=\"circle\" />\r\n  </v-chart>\r\n</template>\r\n\r\n<script>\r\nimport Viser from 'viser-vue';\r\nimport Vue from 'vue';\r\n\r\nVue.use(Viser);\r\n\r\nconst axis1Opts = {\r\n  dataKey: 'item',\r\n  line: null,\r\n  tickLine: null,\r\n  grid: {\r\n    lineStyle: {\r\n      lineDash: null\r\n    },\r\n    hideFirstLine: false\r\n  }\r\n}\r\nconst axis2Opts = {\r\n  dataKey: 'score',\r\n  line: null,\r\n  tickLine: null,\r\n  grid: {\r\n    type: 'polygon',\r\n    lineStyle: {\r\n      lineDash: null\r\n    }\r\n  }\r\n}\r\n\r\nconst scale = [\r\n  {\r\n    dataKey: 'score',\r\n    min: 0,\r\n    max: 80\r\n  }, {\r\n    dataKey: 'user',\r\n    alias: '类型'\r\n  }\r\n]\r\n\r\nexport default {\r\n  name: 'Radar',\r\n  props: {\r\n    data: {\r\n      type: Array,\r\n      default: null\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      axis1Opts,\r\n      axis2Opts,\r\n      scale\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}