{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\account\\user-management.vue", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJVc2VyTWFuYWdlbWVudCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOeUqOaIt+WIl+ihqA0KICAgICAgdXNlckxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIHVzZXJJZDogMSwNCiAgICAgICAgICB1c2VyTmFtZTogImFkbWluIiwNCiAgICAgICAgICBwaG9uZU51bWJlcjogIjEzODAwMTM4MDAwIiwNCiAgICAgICAgICBlbWFpbDogImFkbWluQGV4YW1wbGUuY29tIiwNCiAgICAgICAgICBwYXNzd29yZDogIioqKioqKiIsDQogICAgICAgICAgbWF4VmlzaXRzOiAxMDAwLA0KICAgICAgICAgIHJvbGU6ICLnrqHnkIblkZgiLA0KICAgICAgICAgIGRlcGFydG1lbnQ6ICLmioDmnK/pg6giLA0KICAgICAgICAgIHN0YXR1czogMQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdXNlcklkOiAyLA0KICAgICAgICAgIHVzZXJOYW1lOiAidXNlcjEiLA0KICAgICAgICAgIHBob25lTnVtYmVyOiAiMTM4MDAxMzgwMDEiLA0KICAgICAgICAgIGVtYWlsOiAidXNlcjFAZXhhbXBsZS5jb20iLA0KICAgICAgICAgIHBhc3N3b3JkOiAiKioqKioqIiwNCiAgICAgICAgICBtYXhWaXNpdHM6IDUwMCwNCiAgICAgICAgICByb2xlOiAi5pmu6YCa55So5oi3IiwNCiAgICAgICAgICBkZXBhcnRtZW50OiAi5biC5Zy66YOoIiwNCiAgICAgICAgICBzdGF0dXM6IDENCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHVzZXJJZDogMywNCiAgICAgICAgICB1c2VyTmFtZTogInVzZXIyIiwNCiAgICAgICAgICBwaG9uZU51bWJlcjogIjEzODAwMTM4MDAyIiwNCiAgICAgICAgICBlbWFpbDogInVzZXIyQGV4YW1wbGUuY29tIiwNCiAgICAgICAgICBwYXNzd29yZDogIioqKioqKiIsDQogICAgICAgICAgbWF4VmlzaXRzOiAzMDAsDQogICAgICAgICAgcm9sZTogIuaZrumAmueUqOaItyIsDQogICAgICAgICAgZGVwYXJ0bWVudDogIumUgOWUrumDqCIsDQogICAgICAgICAgc3RhdHVzOiAwDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDliIbpobXnm7jlhbMNCiAgICAgIHRvdGFsOiAzLA0KICAgICAgY3VycmVudFBhZ2U6IDEsDQogICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAvLyDlr7nor53moYbnm7jlhbMNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nVGl0bGU6ICLmt7vliqDnlKjmiLciLA0KICAgICAgdXNlckZvcm06IHsNCiAgICAgICAgdXNlcklkOiBudWxsLA0KICAgICAgICB1c2VyTmFtZTogIiIsDQogICAgICAgIHBob25lTnVtYmVyOiAiIiwNCiAgICAgICAgZW1haWw6ICIiLA0KICAgICAgICBwYXNzd29yZDogIiIsDQogICAgICAgIG1heFZpc2l0czogMCwNCiAgICAgICAgcm9sZTogIiIsDQogICAgICAgIGRlcGFydG1lbnQ6ICIiLA0KICAgICAgICBzdGF0dXM6IDENCiAgICAgIH0sDQogICAgICB1c2VyRm9ybVJ1bGVzOiB7DQogICAgICAgIHVzZXJOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeeUqOaIt+WQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgbWluOiAzLCBtYXg6IDIwLCBtZXNzYWdlOiAi6ZW/5bqm5ZyoIDMg5YiwIDIwIOS4quWtl+espiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIHBob25lTnVtYmVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeaJi+acuuWPt+eggSIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICAgIHsgcGF0dGVybjogL14xWzMtOV1cZHs5fSQvLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgZW1haWw6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl6YKu566xIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB0eXBlOiAiZW1haWwiLCBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGFzc3dvcmQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5a+G56CBIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyBtaW46IDgsIG1heDogMTYsIG1lc3NhZ2U6ICLplb/luqblnKggOCDliLAgMTYg5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgcGF0dGVybjogL14oPz0uKlthLXpdKSg/PS4qW0EtWl0pKD89LipcZCkoPz0uKlteXGRhLXpBLVpdKS57OCwxNn0kLywNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlr4bnoIHlv4XpobvlkIzml7bljIXlkKvmlbDlrZfjgIHlpKflsI/lhpnlrZfmr43lkoznrKblj7ciLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWkhOeQhumhteeggeWPmOWMlg0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gdmFsOw0KICAgICAgLy8g5a6e6ZmF5bqU55So5Lit6L+Z6YeM6ZyA6KaB6LCD55So5o6l5Y+j6I635Y+W5a+55bqU6aG155qE5pWw5o2uDQogICAgfSwNCiAgICAvLyDmt7vliqDnlKjmiLcNCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gIua3u+WKoOeUqOaItyI7DQogICAgICB0aGlzLnVzZXJGb3JtID0gew0KICAgICAgICB1c2VySWQ6IG51bGwsDQogICAgICAgIHVzZXJOYW1lOiAiIiwNCiAgICAgICAgcGhvbmVOdW1iZXI6ICIiLA0KICAgICAgICBlbWFpbDogIiIsDQogICAgICAgIHBhc3N3b3JkOiAiIiwNCiAgICAgICAgbWF4VmlzaXRzOiAwLA0KICAgICAgICByb2xlOiAiIiwNCiAgICAgICAgZGVwYXJ0bWVudDogIiIsDQogICAgICAgIHN0YXR1czogMQ0KICAgICAgfTsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDnvJbovpHnlKjmiLcNCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy5kaWFsb2dUaXRsZSA9ICLnvJbovpHnlKjmiLciOw0KICAgICAgdGhpcy51c2VyRm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocm93KSk7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy8g5Yig6Zmk55So5oi3DQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgdGhpcy4kY29uZmlybShg56Gu5a6a6KaB5Yig6Zmk55So5oi3IiR7cm93LnVzZXJOYW1lfSLlkJfvvJ9gLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDlrp7pmYXlupTnlKjkuK3ov5nph4zpnIDopoHosIPnlKjmjqXlj6PliKDpmaTnlKjmiLcNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IHRoaXMudXNlckxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS51c2VySWQgIT09IHJvdy51c2VySWQpOw0KICAgICAgICB0aGlzLnRvdGFsID0gdGhpcy51c2VyTGlzdC5sZW5ndGg7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi5bey5Y+W5raI5Yig6ZmkIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOS/ruaUueeUqOaIt+eKtuaAgQ0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIC8vIOWunumZheW6lOeUqOS4rei/memHjOmcgOimgeiwg+eUqOaOpeWPo+S/ruaUueeUqOaIt+eKtuaAgQ0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDnlKjmiLciJHtyb3cudXNlck5hbWV9IueKtuaAgeW3siR7cm93LnN0YXR1cyA9PT0gMSA/ICflkK/nlKgnIDogJ+emgeeUqCd9YCk7DQogICAgfSwNCiAgICAvLyDmj5DkuqTooajljZUNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy51c2VyRm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLnVzZXJGb3JtLnVzZXJJZCkgew0KICAgICAgICAgICAgLy8g57yW6L6R55So5oi3DQogICAgICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMudXNlckxpc3QuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS51c2VySWQgPT09IHRoaXMudXNlckZvcm0udXNlcklkKTsNCiAgICAgICAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgICAgICAgdGhpcy51c2VyTGlzdC5zcGxpY2UoaW5kZXgsIDEsIHRoaXMudXNlckZvcm0pOw0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmt7vliqDnlKjmiLcNCiAgICAgICAgICAgIHRoaXMudXNlckZvcm0udXNlcklkID0gdGhpcy51c2VyTGlzdC5sZW5ndGggKyAxOw0KICAgICAgICAgICAgdGhpcy51c2VyTGlzdC5wdXNoKHRoaXMudXNlckZvcm0pOw0KICAgICAgICAgICAgdGhpcy50b3RhbCA9IHRoaXMudXNlckxpc3QubGVuZ3RoOw0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmt7vliqDmiJDlip8iKTsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["user-management.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "user-management.vue", "sourceRoot": "src/views/account", "sourcesContent": ["<template>\r\n  <div class=\"user-management-container\">\r\n    <div class=\"user-management-header\">\r\n      <div class=\"title\">用户管理</div>\r\n      <div class=\"actions\">\r\n        <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"handleAdd\">添加用户</el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"user-management-content\">\r\n      <el-table\r\n        :data=\"userList\"\r\n        style=\"width: 100%\"\r\n        border\r\n        stripe\r\n        :header-cell-style=\"{background:'#f5f7fa', color:'#606266'}\"\r\n      >\r\n        <el-table-column\r\n          prop=\"userId\"\r\n          label=\"用户ID\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"userName\"\r\n          label=\"用户名称\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"phoneNumber\"\r\n          label=\"手机号\"\r\n          width=\"150\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"email\"\r\n          label=\"邮箱\"\r\n          width=\"180\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"maxVisits\"\r\n          label=\"访问权限\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        />\r\n        <el-table-column\r\n          prop=\"status\"\r\n          label=\"状态\"\r\n          width=\"100\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.status\"\r\n              active-color=\"#13ce66\"\r\n              inactive-color=\"#ff4949\"\r\n              :active-value=\"1\"\r\n              :inactive-value=\"0\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleEdit(scope.row)\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              class=\"delete-btn\"\r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          :current-page.sync=\"currentPage\"\r\n          :page-size=\"pageSize\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用户对话框 -->\r\n    <el-dialog title=\"添加用户\" :visible.sync=\"dialogVisible\" width=\"500px\" center class=\"user-dialog\">\r\n      <el-form ref=\"userForm\" :model=\"userForm\" :rules=\"userFormRules\" label-width=\"100px\">\r\n        <el-form-item label=\"用户名称\" prop=\"userName\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.userName\" placeholder=\"请输入用户名/手机号/邮箱等登录名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号码\" prop=\"phoneNumber\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.phoneNumber\" placeholder=\"输入手机号码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.email\" placeholder=\"输入邮箱\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\">\r\n          <div class=\"required-mark\">*</div>\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"8-16 密码必须同时包含数字、大小写字母和符号\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大访问量\" prop=\"maxVisits\">\r\n          <el-input v-model=\"userForm.maxVisits\" placeholder=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-switch\r\n            v-model=\"userForm.status\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\" class=\"confirm-btn\">确定</el-button>\r\n        <el-button @click=\"dialogVisible = false\" class=\"cancel-btn\">取消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"UserManagement\",\r\n  data() {\r\n    return {\r\n      // 用户列表\r\n      userList: [\r\n        {\r\n          userId: 1,\r\n          userName: \"admin\",\r\n          phoneNumber: \"13800138000\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 1000,\r\n          role: \"管理员\",\r\n          department: \"技术部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 2,\r\n          userName: \"user1\",\r\n          phoneNumber: \"13800138001\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 500,\r\n          role: \"普通用户\",\r\n          department: \"市场部\",\r\n          status: 1\r\n        },\r\n        {\r\n          userId: 3,\r\n          userName: \"user2\",\r\n          phoneNumber: \"13800138002\",\r\n          email: \"<EMAIL>\",\r\n          password: \"******\",\r\n          maxVisits: 300,\r\n          role: \"普通用户\",\r\n          department: \"销售部\",\r\n          status: 0\r\n        }\r\n      ],\r\n      // 分页相关\r\n      total: 3,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      // 对话框相关\r\n      dialogVisible: false,\r\n      dialogTitle: \"添加用户\",\r\n      userForm: {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      },\r\n      userFormRules: {\r\n        userName: [\r\n          { required: true, message: \"请输入用户名称\", trigger: \"blur\" },\r\n          { min: 3, max: 20, message: \"长度在 3 到 20 个字符\", trigger: \"blur\" }\r\n        ],\r\n        phoneNumber: [\r\n          { required: true, message: \"请输入手机号码\", trigger: \"blur\" },\r\n          { pattern: /^1[3-9]\\d{9}$/, message: \"请输入正确的手机号码\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"请输入邮箱\", trigger: \"blur\" },\r\n          { type: \"email\", message: \"请输入正确的邮箱地址\", trigger: \"blur\" }\r\n        ],\r\n        password: [\r\n          { required: true, message: \"请输入密码\", trigger: \"blur\" },\r\n          { min: 8, max: 16, message: \"长度在 8 到 16 个字符\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[^\\da-zA-Z]).{8,16}$/,\r\n            message: \"密码必须同时包含数字、大小写字母和符号\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    // 处理页码变化\r\n    handleCurrentChange(val) {\r\n      this.currentPage = val;\r\n      // 实际应用中这里需要调用接口获取对应页的数据\r\n    },\r\n    // 添加用户\r\n    handleAdd() {\r\n      this.dialogTitle = \"添加用户\";\r\n      this.userForm = {\r\n        userId: null,\r\n        userName: \"\",\r\n        phoneNumber: \"\",\r\n        email: \"\",\r\n        password: \"\",\r\n        maxVisits: 0,\r\n        role: \"\",\r\n        department: \"\",\r\n        status: 1\r\n      };\r\n      this.dialogVisible = true;\r\n    },\r\n    // 编辑用户\r\n    handleEdit(row) {\r\n      this.dialogTitle = \"编辑用户\";\r\n      this.userForm = JSON.parse(JSON.stringify(row));\r\n      this.dialogVisible = true;\r\n    },\r\n    // 删除用户\r\n    handleDelete(row) {\r\n      this.$confirm(`确定要删除用户\"${row.userName}\"吗？`, \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 实际应用中这里需要调用接口删除用户\r\n        this.userList = this.userList.filter(item => item.userId !== row.userId);\r\n        this.total = this.userList.length;\r\n        this.$message.success(\"删除成功\");\r\n      }).catch(() => {\r\n        this.$message.info(\"已取消删除\");\r\n      });\r\n    },\r\n    // 修改用户状态\r\n    handleStatusChange(row) {\r\n      // 实际应用中这里需要调用接口修改用户状态\r\n      this.$message.success(`用户\"${row.userName}\"状态已${row.status === 1 ? '启用' : '禁用'}`);\r\n    },\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.userForm.validate(valid => {\r\n        if (valid) {\r\n          if (this.userForm.userId) {\r\n            // 编辑用户\r\n            const index = this.userList.findIndex(item => item.userId === this.userForm.userId);\r\n            if (index !== -1) {\r\n              this.userList.splice(index, 1, this.userForm);\r\n              this.$message.success(\"修改成功\");\r\n            }\r\n          } else {\r\n            // 添加用户\r\n            this.userForm.userId = this.userList.length + 1;\r\n            this.userList.push(this.userForm);\r\n            this.total = this.userList.length;\r\n            this.$message.success(\"添加成功\");\r\n          }\r\n          this.dialogVisible = false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user-management-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.user-management-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.user-management-content {\r\n  .el-table {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .delete-btn {\r\n    color: #f56c6c;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n\r\n  .confirm-btn {\r\n    background-color: #409EFF;\r\n    border-color: #409EFF;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .cancel-btn {\r\n    color: #606266;\r\n    background-color: #fff;\r\n    border-color: #dcdfe6;\r\n  }\r\n}\r\n\r\n.user-dialog {\r\n  .el-dialog__header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n\r\n    .el-dialog__title {\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .el-dialog__headerbtn {\r\n      top: 15px;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 20px;\r\n  }\r\n\r\n  .el-form-item {\r\n    position: relative;\r\n    margin-bottom: 20px;\r\n\r\n    .required-mark {\r\n      position: absolute;\r\n      left: -10px;\r\n      top: 10px;\r\n      color: #f56c6c;\r\n      font-size: 14px;\r\n    }\r\n\r\n    .el-form-item__label {\r\n      color: #606266;\r\n      font-weight: normal;\r\n    }\r\n\r\n    .el-input__inner {\r\n      border-radius: 3px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}