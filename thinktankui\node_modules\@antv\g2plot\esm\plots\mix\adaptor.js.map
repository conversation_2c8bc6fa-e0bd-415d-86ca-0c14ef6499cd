{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/mix/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC1F,OAAO,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAIzD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAErD,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE1C;;;GAGG;AACH,SAAS,SAAS,CAAC,MAA0B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAI,CAAC,KAAK,EAAE,UAAC,CAAQ;QACX,IAAA,MAAM,GAAmF,CAAC,OAApF,EAAE,IAAI,GAA6E,CAAC,KAA9E,EAAE,IAAI,GAAuE,CAAC,KAAxE,EAAE,IAAI,GAAiE,CAAC,KAAlE,EAAE,UAAU,GAAqD,CAAC,WAAtD,EAAE,YAAY,GAAuC,CAAC,aAAxC,EAAE,WAAW,GAA0B,CAAC,YAA3B,EAAE,OAAO,GAAiB,CAAC,QAAlB,EAAE,UAAU,GAAK,CAAC,WAAN,CAAO;QAEnG,aAAa;QACb,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC;YAChC,MAAM,QAAA;SACP,CAAC,CAAC;QAEH,UAAU;QACV,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEpB,UAAU;QACV,IAAI,MAAM,GAAwB,EAAE,CAAC;QACrC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;gBACnC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;SACJ;QAED,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACtC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEvB,cAAc;QACd,IAAI,CAAC,IAAI,EAAE;YACT,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,IAAI,EAAE,UAAC,IAAU,EAAE,KAAa;gBACnC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;SACJ;QAED,gBAAgB;QAChB,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAEhC,cAAc;QACd,IAAI,CAAC,UAAU,EAAE,UAAC,QAAmB;YAC3B,IAAA,GAAG,GAAK,eAAe,CAAC;gBAC9B,KAAK,EAAE,QAAQ;gBACf,OAAO,EAAE,QAAQ;aAClB,CAAC,IAHS,CAGR;YAEH,SAAS;YACD,IAAA,MAAM,GAAK,QAAQ,OAAb,CAAc;YAC5B,IAAI,MAAM,EAAE;gBACV,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,IAAI,CAAC,YAAY,EAAE,UAAC,WAAwB;YAC1C,IAAI,WAAW,CAAC,MAAM,KAAK,KAAK,EAAE;gBAChC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aAC9C;iBAAM;gBACL,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;aACzD;QACH,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,WAAW,EAAE,UAAC,UAAU;YAC3B,QAAQ,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,cACjC,UAAU,EACb,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,OAAO,CAAC,CAAC,SAAS,KAAK,SAAS,EAAE;YACpC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzB;aAAM;YACL,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvB,+CAA+C;YAC/C,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAC,CAAW;gBACpC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,OAAO,EAAE;YACX,cAAc;YACd,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAChC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3B;IACH,CAAC,CAAC,CAAC;IAEH,SAAS;IACT,IAAI,CAAC,MAAM,EAAE;QACX,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM;QACL,IAAI,CAAC,MAAM,EAAE,UAAC,CAAS,EAAE,KAAa;YACpC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;KACJ;IAED,UAAU;IACV,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,MAA0B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAgB,OAAO,MAAvB,EAAE,KAAc,OAAO,KAAZ,EAAT,IAAI,mBAAG,EAAE,KAAA,CAAa;IAErC,IAAI,CAAC,KAAK,EAAE,UAAC,IAAI;QACP,IAAA,IAAI,GAAgC,IAAI,KAApC,EAAE,MAAM,GAAwB,IAAI,OAA5B,EAAE,KAAsB,IAAI,QAAd,EAAZ,OAAO,mBAAG,EAAE,KAAA,EAAE,GAAG,GAAK,IAAI,IAAT,CAAU;QACzC,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;QAE5B,IAAI,GAAG,EAAE;YACP,eAAe,CAAC,IAAI,EAAE,KAAK,wBAAO,OAAO,KAAE,IAAI,MAAA,IAAG,CAAC;YACnD,OAAO;SACR;QAED,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,YAAG,MAAM,QAAA,IAAK,IAAI,CAAC,OAAO,EAAE,sBAAsB,CAAC,EAAG,CAAC;QACxF,IAAI,OAAO,EAAE;YACX,gBAAgB;YAChB,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SACjC;QAED,eAAe,CAAC,IAAI,EAAE,QAAQ,aAAI,IAAI,MAAA,IAAK,OAAO,EAAG,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA0B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAElC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAEvC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA0B;IAChD,OAAO,IAAI,CACT,SAAS,EAAE,sBAAsB;IACjC,SAAS,EACT,SAAS,EACT,WAAW,EACX,SAAS,EACT,KAAK,EACL,OAAO,EACP,MAAM,EACN,UAAU,EAAE;IACZ,uBAAuB;KACxB,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { each } from '@antv/util';\nimport { animation, annotation, interaction, theme, tooltip } from '../../adaptor/common';\nimport { geometry as geometryAdaptor } from '../../adaptor/geometries/base';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { PLOT_CONTAINER_OPTIONS } from '../../core/plot';\nimport { Axis } from '../../types/axis';\nimport { Interaction } from '../../types/interaction';\nimport { Legend } from '../../types/legend';\nimport { deepAssign, flow, pick } from '../../utils';\nimport { IGeometry, IView, MixOptions } from './types';\nimport { execPlotAdaptor } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction multiView(params: Params<MixOptions>): Params<MixOptions> {\n  const { chart, options } = params;\n  const { views, legend } = options;\n\n  each(views, (v: IView) => {\n    const { region, data, meta, axes, coordinate, interactions, annotations, tooltip, geometries } = v;\n\n    // 1. 创建 view\n    const viewOfG2 = chart.createView({\n      region,\n    });\n\n    // 2. data\n    viewOfG2.data(data);\n\n    // 3. meta\n    let scales: Record<string, any> = {};\n    if (axes) {\n      each(axes, (axis: Axis, field: string) => {\n        scales[field] = pick(axis, AXIS_META_CONFIG_KEYS);\n      });\n    }\n\n    scales = deepAssign({}, meta, scales);\n    viewOfG2.scale(scales);\n\n    // 4. x y axis\n    if (!axes) {\n      viewOfG2.axis(false);\n    } else {\n      each(axes, (axis: Axis, field: string) => {\n        viewOfG2.axis(field, axis);\n      });\n    }\n\n    // 5. coordinate\n    viewOfG2.coordinate(coordinate);\n\n    // 6. geometry\n    each(geometries, (geometry: IGeometry) => {\n      const { ext } = geometryAdaptor({\n        chart: viewOfG2,\n        options: geometry,\n      });\n\n      // adjust\n      const { adjust } = geometry;\n      if (adjust) {\n        ext.geometry.adjust(adjust);\n      }\n    });\n\n    // 7. interactions\n    each(interactions, (interaction: Interaction) => {\n      if (interaction.enable === false) {\n        viewOfG2.removeInteraction(interaction.type);\n      } else {\n        viewOfG2.interaction(interaction.type, interaction.cfg);\n      }\n    });\n\n    // 8. annotations\n    each(annotations, (annotation) => {\n      viewOfG2.annotation()[annotation.type]({\n        ...annotation,\n      });\n    });\n\n    // 9. animation (先做动画)\n    if (typeof v.animation === 'boolean') {\n      viewOfG2.animate(false);\n    } else {\n      viewOfG2.animate(true);\n      // 9.1 所有的 Geometry 都使用同一动画（各个图形如有区别，todo 自行覆盖）\n      each(viewOfG2.geometries, (g: Geometry) => {\n        g.animate(v.animation);\n      });\n    }\n\n    if (tooltip) {\n      // 10. tooltip\n      viewOfG2.interaction('tooltip');\n      viewOfG2.tooltip(tooltip);\n    }\n  });\n\n  // legend\n  if (!legend) {\n    chart.legend(false);\n  } else {\n    each(legend, (l: Legend, field: string) => {\n      chart.legend(field, l);\n    });\n  }\n\n  // tooltip\n  chart.tooltip(options.tooltip);\n  return params;\n}\n\n/**\n * 支持嵌套使用 g2plot 内置图表\n * @param params\n */\nfunction multiPlot(params: Params<MixOptions>): Params<MixOptions> {\n  const { chart, options } = params;\n  const { plots, data = [] } = options;\n\n  each(plots, (plot) => {\n    const { type, region, options = {}, top } = plot;\n    const { tooltip } = options;\n\n    if (top) {\n      execPlotAdaptor(type, chart, { ...options, data });\n      return;\n    }\n\n    const viewOfG2 = chart.createView({ region, ...pick(options, PLOT_CONTAINER_OPTIONS) });\n    if (tooltip) {\n      // 配置 tooltip 交互\n      viewOfG2.interaction('tooltip');\n    }\n\n    execPlotAdaptor(type, viewOfG2, { data, ...options });\n  });\n\n  return params;\n}\n\n/**\n * 处理缩略轴的 adaptor (mix)\n * @param params\n */\nexport function slider(params: Params<MixOptions>): Params<MixOptions> {\n  const { chart, options } = params;\n\n  chart.option('slider', options.slider);\n\n  return params;\n}\n\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<MixOptions>) {\n  return flow(\n    animation, // 多 view 的图，动画配置放到最前面\n    multiView,\n    multiPlot,\n    interaction,\n    animation,\n    theme,\n    tooltip,\n    slider,\n    annotation()\n    // ... 其他的 adaptor flow\n  )(params);\n}\n"]}