{"version": 3, "file": "circle.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/mask/multiple/circle.ts"], "names": [], "mappings": ";AAAA,OAAO,gBAAgB,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AAEzC;;;GAGG;AACH;IAA8B,mCAAgB;IAA9C;QAAA,qEAGC;QAFW,eAAS,GAAG,QAAQ,CAAC;QACrB,kBAAY,GAAG,YAAY,CAAC;;IACxC,CAAC;IAAD,sBAAC;AAAD,CAAC,AAHD,CAA8B,gBAAgB,GAG7C;AAED,eAAe,eAAe,CAAC", "sourcesContent": ["import MultipleMaskBase from './base';\nimport { getMaskAttrs } from '../circle';\n\n/**\n * @ignore\n * 圆形辅助框 Action\n */\nclass CircleMultiMask extends MultipleMaskBase {\n  protected shapeType = 'circle';\n  protected getMaskAttrs = getMaskAttrs;\n}\n\nexport default CircleMultiMask;\n"]}