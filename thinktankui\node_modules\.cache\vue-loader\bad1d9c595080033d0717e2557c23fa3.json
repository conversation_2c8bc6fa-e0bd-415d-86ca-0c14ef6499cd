{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Crontab\\min.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Crontab\\min.vue", "mtime": 1749109381322}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["min.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "min.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n\t<el-form size=\"small\">\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\r\n\t\t\t\t分钟，允许的通配符[, - * /]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\r\n\t\t\t\t周期从\r\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"58\" /> -\r\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"59\" /> 分钟\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\r\n\t\t\t\t从\r\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"58\" /> 分钟开始，每\r\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"59 - average01 || 0\" /> 分钟执行一次\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\r\n\t\t\t\t\t<el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradioValue: 1,\r\n\t\t\tcycle01: 1,\r\n\t\t\tcycle02: 2,\r\n\t\t\taverage01: 0,\r\n\t\t\taverage02: 1,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-min',\r\n\tprops: ['check', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'min', '*', 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '2') {\r\n\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'checkboxString': 'checkboxChange',\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 58)\r\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\r\n\t\t\treturn cycle01 + '-' + cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 58)\r\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\r\n\t\t\treturn average01 + '/' + average02;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str == '' ? '*' : str;\r\n\t\t}\r\n\t}\r\n}\r\n</script>"]}]}