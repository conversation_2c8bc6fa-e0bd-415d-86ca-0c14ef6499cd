{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/heatmap/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACjH,OAAO,EAAE,QAAQ,IAAI,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAE5E,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAGxD,SAAS,QAAQ,CAAC,MAA8B;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GACV,OAAO,KADG,EAAE,IAAI,GAChB,OAAO,KADS,EAAE,MAAM,GACxB,OAAO,OADiB,EAAE,MAAM,GAChC,OAAO,OADyB,EAAE,UAAU,GAC5C,OAAO,WADqC,EAAE,SAAS,GACvD,OAAO,UADgD,EAAE,SAAS,GAClE,OAAO,UAD2D,EAAE,KAAK,GACzE,OAAO,MADkE,EAAE,KAAK,GAChF,OAAO,MADyE,EAAE,OAAO,GACzF,OAAO,QADkF,EAAE,YAAY,GACvG,OAAO,aADgG,EAAE,IAAI,GAC7G,OAAO,KADsG,CACrG;IAEV,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjB,IAAI,YAAY,GAAG,SAAS,CAAC;IAC7B,IAAI,IAAI,KAAK,SAAS,EAAE;QACtB,YAAY,GAAG,SAAS,CAAC;KAC1B;IAEK,IAAA,KAAwB,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,EAA9E,MAAM,YAAA,EAAE,SAAS,eAA6D,CAAC;IAEvF;;;;;OAKG;IACH,IAAI,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAI,SAAS,IAAI,SAAS,KAAK,CAAC,EAAE;QAChC,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE;YACxB,OAAO,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;SAClF;aAAM,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,EAAE;YACzC,OAAO,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;SAC1E;aAAM;YACL,gBAAgB,GAAG,SAAS,CAAC;SAC9B;KACF;IAED,eAAe,CACb,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,IAAI,EAAE,YAAY;YAClB,UAAU,YAAA;YACV,aAAa,EAAE,MAAM;YACrB,UAAU,EAAE,SAAS,IAAI,EAAE;YAC3B,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE;gBACP,OAAO,EAAE,SAAS;gBAClB,KAAK,EACH,KAAK;oBACL,CAAC,SAAS;wBACR,CAAC,CAAC,UAAC,KAAK;4BACJ,IAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,SAAS,CAAC,EAAd,CAAc,CAAC,CAAC;4BAC5C,IAAA,KAAe,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,SAAS,CAAC,KAAI,EAAE,EAApC,GAAG,SAAA,EAAE,GAAG,SAA4B,CAAC;4BAC3C,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,KAAK,CAAC,CAAC;4BAC/C,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAR,IAAI,EAAQ,KAAK,CAAC,CAAC;4BAC/C,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,gBAAgB,CAAC,CAAC;wBAChF,CAAC;wBACH,CAAC,CAAC,cAAM,OAAA,CAAC,KAAK,EAAE,CAAC,EAAE,gBAAgB,CAAC,EAA5B,CAA4B,CAAC;gBACzC,KAAK,EAAE,KAAK,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzE,KAAK,EAAE,YAAY;aACpB;SACF;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA8B;;IAClC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA8B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA8B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAwC,OAAO,OAA/C,EAAE,UAAU,GAA4B,OAAO,WAAnC,EAAE,SAAS,GAAiB,OAAO,UAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAE9D,8CAA8C;IAC9C,IAAM,UAAU,GAAG,MAAM,KAAK,KAAK,CAAC;IAEpC,IAAI,UAAU,EAAE;QACd,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KACvD;IAED,oDAAoD;IACpD,IAAI,SAAS,EAAE;QACb,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;KACzE;IAED,6BAA6B;IAC7B,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE;QAC9B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,KAAK,CAAC,MAA8B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAuB,OAAO,MAA9B,EAAE,UAAU,GAAW,OAAO,WAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAE5C,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAEjF,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM,IAAI,UAAU,EAAE;QACb,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,CAAC,UAAU,CAAC;YACpB,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA8B;;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,UAAU,GAAc,OAAO,WAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAExC,IAAM,gBAAgB,GAAG,UAAU,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;IAErF,IAAI,OAAO,EAAE;QACX,MAAA,MAAA,gBAAgB,CAAC,OAAO,0CAAE,IAAI,mDAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;KACxD;IAED,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAEnC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA8B;IACpD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,OAAO,CAAC,cAAc,CAAC,EACvB,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACP,KAAK,EACL,UAAU,EAAE,EACZ,WAAW,EACX,SAAS,EACT,KAAK,CACN,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { get, isNumber } from '@antv/util';\nimport { animation, annotation, interaction, pattern, scale, state, theme, tooltip } from '../../adaptor/common';\nimport { geometry as geometryAdaptor } from '../../adaptor/geometries/base';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { getTooltipMapping } from '../../utils/tooltip';\nimport { HeatmapOptions } from './types';\n\nfunction geometry(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { chart, options } = params;\n  const { data, type, xField, yField, colorField, sizeField, sizeRatio, shape, color, tooltip, heatmapStyle, meta } =\n    options;\n\n  chart.data(data);\n  let geometryType = 'polygon';\n  if (type === 'density') {\n    geometryType = 'heatmap';\n  }\n\n  const { fields, formatter } = getTooltipMapping(tooltip, [xField, yField, colorField]);\n\n  /**\n   * The ratio between the actual size and the max available size, must be in range `[0,1]`.\n   *\n   * If the `sizeRatio` attribute is undefined or it exceeds the range,\n   * `checkedSizeRatio` would be set to 1 as default.\n   */\n  let checkedSizeRatio = 1;\n  if (sizeRatio || sizeRatio === 0) {\n    if (!shape && !sizeField) {\n      console.warn('sizeRatio is not in effect: Must define shape or sizeField first');\n    } else if (sizeRatio < 0 || sizeRatio > 1) {\n      console.warn('sizeRatio is not in effect: It must be a number in [0,1]');\n    } else {\n      checkedSizeRatio = sizeRatio;\n    }\n  }\n\n  geometryAdaptor(\n    deepAssign({}, params, {\n      options: {\n        type: geometryType,\n        colorField,\n        tooltipFields: fields,\n        shapeField: sizeField || '',\n        label: undefined,\n        mapping: {\n          tooltip: formatter,\n          shape:\n            shape &&\n            (sizeField\n              ? (dautm) => {\n                  const field = data.map((row) => row[sizeField]);\n                  let { min, max } = meta?.[sizeField] || {};\n                  min = isNumber(min) ? min : Math.min(...field);\n                  max = isNumber(max) ? max : Math.max(...field);\n                  return [shape, (get(dautm, sizeField) - min) / (max - min), checkedSizeRatio];\n                }\n              : () => [shape, 1, checkedSizeRatio]),\n          color: color || (colorField && chart.getTheme().sequenceColors.join('-')),\n          style: heatmapStyle,\n        },\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(yField, false);\n  } else {\n    chart.axis(yField, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nfunction legend(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { chart, options } = params;\n  const { legend, colorField, sizeField, sizeLegend } = options;\n\n  /** legend 不为 false, 则展示图例, 优先展示 color 分类图例 */\n  const showLegend = legend !== false;\n\n  if (colorField) {\n    chart.legend(colorField, showLegend ? legend : false);\n  }\n\n  // 旧版本: 有 sizeField 就有 sizeLegend. 这里默认继承下 legend 配置\n  if (sizeField) {\n    chart.legend(sizeField, sizeLegend === undefined ? legend : sizeLegend);\n  }\n\n  /** 默认没有 sizeField，则隐藏连续图例 */\n  if (!showLegend && !sizeLegend) {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * fixme 后续确认下，数据标签的逻辑为啥和通用的不一致\n * 数据标签\n * @param params\n */\nfunction label(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { chart, options } = params;\n  const { label, colorField, type } = options;\n\n  const geometry = findGeometry(chart, type === 'density' ? 'heatmap' : 'polygon');\n\n  if (!label) {\n    geometry.label(false);\n  } else if (colorField) {\n    const { callback, ...cfg } = label;\n    geometry.label({\n      fields: [colorField],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * 极坐标\n * @param params\n */\nfunction coordinate(params: Params<HeatmapOptions>): Params<HeatmapOptions> {\n  const { chart, options } = params;\n  const { coordinate, reflect } = options;\n\n  const coordinateOption = deepAssign({ actions: [] }, coordinate ?? { type: 'rect' });\n\n  if (reflect) {\n    coordinateOption.actions?.push?.(['reflect', reflect]);\n  }\n\n  chart.coordinate(coordinateOption);\n\n  return params;\n}\n\n/**\n * 热力图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<HeatmapOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    pattern('heatmapStyle'),\n    meta,\n    coordinate,\n    geometry,\n    axis,\n    legend,\n    tooltip,\n    label,\n    annotation(),\n    interaction,\n    animation,\n    state\n  )(params);\n}\n"]}