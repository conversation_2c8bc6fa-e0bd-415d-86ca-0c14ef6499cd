{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/pie/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC1G,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AACnF,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AACnE,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAG9C,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,kBAAkB,EAAE,eAAe,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC9G,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAEhE;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA0B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAqD,OAAO,KAA5D,EAAE,UAAU,GAAyC,OAAO,WAAhD,EAAE,UAAU,GAA6B,OAAO,WAApC,EAAE,KAAK,GAAsB,OAAO,MAA7B,EAAE,QAAQ,GAAY,OAAO,SAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEzE,WAAW;IACX,IAAI,WAAW,GAAG,kBAAkB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAEvD,IAAI,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE;QACtC,0BAA0B;QAC1B,IAAM,iBAAe,GAAG,gBAAgB,CAAC;QACzC,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAC,CAAC;;YAAK,OAAA,uBAAM,CAAC,gBAAG,iBAAe,IAAG,CAAC,GAAG,WAAW,CAAC,MAAM,OAAG;QAArD,CAAqD,CAAC,CAAC;QAC5F,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAExB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;YAC/B,OAAO,EAAE;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,iBAAe;gBACvB,WAAW,EAAE,UAAU;gBACvB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE;oBACR,KAAK,OAAA;oBACL,KAAK,OAAA;oBACL,KAAK,EAAE,QAAQ;iBAChB;gBACD,IAAI,EAAE;oBACJ,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,IAAI;iBACjB;aACF;SACF,CAAC,CAAC;QAEH,QAAQ,CAAC,CAAC,CAAC,CAAC;KACb;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAExB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;YAC/B,OAAO,EAAE;gBACP,MAAM,EAAE,GAAG;gBACX,MAAM,EAAE,UAAU;gBAClB,WAAW,EAAE,UAAU;gBACvB,OAAO,EAAE,IAAI;gBACb,QAAQ,EAAE;oBACR,KAAK,OAAA;oBACL,KAAK,OAAA;oBACL,KAAK,EAAE,QAAQ;iBAChB;gBACD,IAAI,EAAE;oBACJ,cAAc,EAAE,IAAI;oBACpB,UAAU,EAAE,IAAI;iBACjB;aACF;SACF,CAAC,CAAC;QAEH,QAAQ,CAAC,CAAC,CAAC,CAAC;KACb;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA0B;;IAC9B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAiB,OAAO,KAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAErC,qBAAqB;IACrB,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACpC,KAAK,CAAC,KAAK,CAAC,MAAM;QAChB,GAAC,UAAU,IAAG,EAAE,IAAI,EAAE,KAAK,EAAE;YAC7B,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA0B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAwC,OAAO,OAA/C,EAAE,WAAW,GAA2B,OAAO,YAAlC,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAE9D,KAAK,CAAC,UAAU,CAAC;QACf,IAAI,EAAE,OAAO;QACb,GAAG,EAAE;YACH,MAAM,QAAA;YACN,WAAW,aAAA;YACX,UAAU,YAAA;YACV,QAAQ,UAAA;SACT;KACF,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA0B;IAC/B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA6B,OAAO,MAApC,EAAE,UAAU,GAAiB,OAAO,WAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAElD,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACrC,8BAA8B;IAC9B,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,IAAM,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;QAErC,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,OAAO,EAAE;YACZ,IAAA,SAAO,GAAK,QAAQ,QAAb,CAAc;YAC7B,QAAQ,CAAC,OAAO,GAAG,UAAC,IAAY,EAAE,MAAW,EAAE,KAAa;gBAC1D,IAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC/B,8CAA8C;gBAC9C,IAAM,UAAU,GAAG,KAAK,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;gBACrD,IAAM,OAAO,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,KAAK,CAAC,KAAK,CAAC,CAAC;gBACzC,OAAO,UAAU,CAAC,SAAO,CAAC;oBACxB,CAAC,CAAC,mFAAmF;wBACnF,SAAO,uBAAM,IAAI,KAAE,OAAO,SAAA,KAAI,MAAM,EAAE,KAAK,CAAC;oBAC9C,CAAC,CAAC,QAAQ,CAAC,SAAO,CAAC;wBACnB,CAAC,CAAC,QAAQ,CAAC,SAAiB,EAAE;4BAC1B,KAAK,OAAA;4BACL,IAAI,MAAA;4BACJ,sCAAsC;4BACtC,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG,CAAC,CAAC,CAAC,IAAI;yBACzF,CAAC;wBACJ,CAAC,CAAC,SAAO,CAAC;YACd,CAAC,CAAC;SACH;QAED,IAAM,qBAAqB,GAAG;YAC5B,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,YAAY;SACrB,CAAC;QACF,IAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAC3F,IAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChH,QAAQ,CAAC,MAAM,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAE9F,QAAQ,CAAC,KAAK,CAAC;YACb,6FAA6F;YAC7F,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAC5D,QAAQ,UAAA;YACR,GAAG,wBACE,QAAQ,KACX,MAAM,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EACnD,IAAI,EAAE,KAAK,GACZ;SACF,CAAC,CAAC;KACJ;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,yBAAyB,CAAC,OAAmB;IACnD,IAAA,WAAW,GAAsD,OAAO,YAA7D,EAAE,SAAS,GAA2C,OAAO,UAAlD,EAAE,UAAU,GAA+B,OAAO,WAAtC,EAAE,UAAU,GAAmB,OAAO,WAA1B,EAAE,IAAI,GAAa,OAAO,KAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjF,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,WAAW,IAAI,SAAS,EAAE;QACxB,IAAA,KAA2C,UAAU,CAAC,EAAE,EAAE,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,EAAtF,UAAQ,WAAA,EAAW,YAAU,aAAyD,CAAC;QACpG,IAAI,UAAQ,KAAK,KAAK,EAAE;YACtB,UAAQ,GAAG,UAAU,CACnB,EAAE,EACF;gBACE,SAAS,EAAE,UAAC,KAAY;oBACtB,sBAAsB;oBACtB,IAAM,IAAI,GAAG,KAAK;wBAChB,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;wBACnB,CAAC,CAAC,CAAC,KAAK,CAAC,UAAQ,CAAC,OAAO,CAAC;4BAC1B,CAAC,CAAC,UAAQ,CAAC,OAAO;4BAClB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;oBACrC,IAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;oBACzE,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC7B,CAAC;aACF,EACD,UAAQ,CACT,CAAC;SACH;QACD,IAAI,YAAU,KAAK,KAAK,EAAE;YACxB,YAAU,GAAG,UAAU,CACrB,EAAE,EACF;gBACE,SAAS,EAAE,UAAC,KAAY,EAAE,IAAU;oBAClC,IAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;oBAC9E,IAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;oBACzE,MAAM;oBACN,IAAI,KAAK,EAAE;wBACT,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC;qBACjC;oBACD,OAAO,CAAC,KAAK,CAAC,YAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAU,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBACpF,CAAC;aACF,EACD,YAAU,CACX,CAAC;SACH;QAED,OAAO,UAAU,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,UAAQ,EAAE,OAAO,EAAE,YAAU,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;KACzF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,MAA0B;IAC9C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC5B,IAAA,KAA6B,yBAAyB,CAAC,OAAO,CAAC,EAA7D,WAAW,iBAAA,EAAE,SAAS,eAAuC,CAAC;IACtE,cAAc;IACd,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9C,6BAA6B;IAC7B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;IAE3B,eAAe;IACf,IAAI,WAAW,IAAI,SAAS,EAAE;QAC5B,eAAe,CAAC,KAAK,EAAE,EAAE,SAAS,WAAA,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;KACxD;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,OAAO,CAAC,MAA0B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAmC,OAAO,QAA1C,EAAE,UAAU,GAAuB,OAAO,WAA9B,EAAE,UAAU,GAAW,OAAO,WAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAE1D,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KACxB;SAAM;QACL,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAE1D,mCAAmC;QACnC,IAAI,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;YAC/B,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACpC,IAAI,SAAS,GAAG,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAE1C,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE;gBACnC,MAAM,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBAClC,SAAS,GAAG,SAAS,IAAI,CAAC,UAAC,KAAK,IAAK,OAAA,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAjE,CAAiE,CAAC,CAAC;aACzG;YACD,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;SACtF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAA0B;IAC5C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC5B,IAAA,KAA2C,yBAAyB,CAAC,OAAO,CAAC,EAA3E,YAAY,kBAAA,EAAE,SAAS,eAAA,EAAE,WAAW,iBAAuC,CAAC;IAEpF,IAAI,CAAC,YAAY,EAAE,UAAC,CAAc;;QAChC,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;YACtB,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACjC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,sBAAsB,EAAE;YAC5C,2BAA2B;YAC3B,IAAI,aAAW,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,CAAA,MAAA,CAAC,CAAC,GAAG,0CAAE,KAAK,CAAA,EAAE;gBACjB,aAAW,GAAG;oBACZ;wBACE,OAAO,EAAE,oBAAoB;wBAC7B,MAAM,EAAE,UAAG,aAAa,YAAS;wBACjC,GAAG,EAAE,EAAE,SAAS,WAAA,EAAE,WAAW,aAAA,EAAE;qBAChC;iBACF,CAAC;aACH;YACD,IAAI,CAAC,MAAA,CAAC,CAAC,GAAG,0CAAE,KAAK,EAAE,UAAC,KAAK;gBACvB,aAAW,CAAC,IAAI,uBAAM,KAAK,KAAE,GAAG,EAAE,EAAE,SAAS,WAAA,EAAE,WAAW,aAAA,EAAE,IAAG,CAAC;YAClE,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,aAAW,EAAE,CAAC,CAAC,CAAC;SAC1E;aAAM;YACL,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SACxC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA0B;IAChD,0BAA0B;IAC1B,OAAO,IAAI,CACT,OAAO,CAAC,UAAU,CAAC,EACnB,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK;IACL,mBAAmB;IACnB,aAAa,EACb,WAAW,EACX,SAAS,CACV,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { each, get, isArray, isEmpty, isFunction, isNil, isNumber, isString, toString } from '@antv/util';\nimport { animation, annotation, legend, state, theme } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { getMappingFunction } from '../../adaptor/geometries/base';\nimport { pattern } from '../../adaptor/pattern';\nimport { Params } from '../../core/adaptor';\nimport { getLocale } from '../../core/locale';\nimport { Data, Datum } from '../../types';\nimport { Interaction } from '../../types/interaction';\nimport { deepAssign, flow, processIllegalData, renderStatistic, template, transformLabel } from '../../utils';\nimport { DEFAULT_OPTIONS } from './contants';\nimport { PIE_STATISTIC } from './interactions';\nimport { PieOptions } from './types';\nimport { adaptOffset, getTotalValue, isAllZero } from './utils';\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { data, angleField, colorField, color, pieStyle, shape } = options;\n\n  // 处理不合法的数据\n  let processData = processIllegalData(data, angleField);\n\n  if (isAllZero(processData, angleField)) {\n    // 数据全 0 处理，调整 position 映射\n    const percentageField = '$$percentage$$';\n    processData = processData.map((d) => ({ ...d, [percentageField]: 1 / processData.length }));\n    chart.data(processData);\n\n    const p = deepAssign({}, params, {\n      options: {\n        xField: '1',\n        yField: percentageField,\n        seriesField: colorField,\n        isStack: true,\n        interval: {\n          color,\n          shape,\n          style: pieStyle,\n        },\n        args: {\n          zIndexReversed: true,\n          sortZIndex: true,\n        },\n      },\n    });\n\n    interval(p);\n  } else {\n    chart.data(processData);\n\n    const p = deepAssign({}, params, {\n      options: {\n        xField: '1',\n        yField: angleField,\n        seriesField: colorField,\n        isStack: true,\n        interval: {\n          color,\n          shape,\n          style: pieStyle,\n        },\n        args: {\n          zIndexReversed: true,\n          sortZIndex: true,\n        },\n      },\n    });\n\n    interval(p);\n  }\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { meta, colorField } = options;\n\n  // meta 直接是 scale 的信息\n  const scales = deepAssign({}, meta);\n  chart.scale(scales, {\n    [colorField]: { type: 'cat' },\n  });\n\n  return params;\n}\n\n/**\n * coord 配置\n * @param params\n */\nfunction coordinate(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { radius, innerRadius, startAngle, endAngle } = options;\n\n  chart.coordinate({\n    type: 'theta',\n    cfg: {\n      radius,\n      innerRadius,\n      startAngle,\n      endAngle,\n    },\n  });\n\n  return params;\n}\n\n/**\n * label 配置\n * @param params\n */\nfunction label(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { label, colorField, angleField } = options;\n\n  const geometry = chart.geometries[0];\n  // label 为 false, 空 则不显示 label\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    const labelCfg = transformLabel(cfg);\n\n    // ① 提供模板字符串的 label content 配置\n    if (labelCfg.content) {\n      const { content } = labelCfg;\n      labelCfg.content = (data: object, dataum: any, index: number) => {\n        const name = data[colorField];\n        const value = data[angleField];\n        // dymatic get scale, scale is ready this time\n        const angleScale = chart.getScaleByField(angleField);\n        const percent = angleScale?.scale(value);\n        return isFunction(content)\n          ? // append percent (number) to data, users can get origin data from `dataum._origin`\n            content({ ...data, percent }, dataum, index)\n          : isString(content)\n          ? template(content as string, {\n              value,\n              name,\n              // percentage (string), default keep 2\n              percentage: isNumber(percent) && !isNil(value) ? `${(percent * 100).toFixed(2)}%` : null,\n            })\n          : content;\n      };\n    }\n\n    const LABEL_LAYOUT_TYPE_MAP = {\n      inner: '',\n      outer: 'pie-outer',\n      spider: 'pie-spider',\n    };\n    const labelLayoutType = labelCfg.type ? LABEL_LAYOUT_TYPE_MAP[labelCfg.type] : 'pie-outer';\n    const labelLayoutCfg = labelCfg.layout ? (!isArray(labelCfg.layout) ? [labelCfg.layout] : labelCfg.layout) : [];\n    labelCfg.layout = (labelLayoutType ? [{ type: labelLayoutType }] : []).concat(labelLayoutCfg);\n\n    geometry.label({\n      // fix: could not create scale, when field is undefined（attributes 中的 fields 定义都会被用来创建 scale）\n      fields: colorField ? [angleField, colorField] : [angleField],\n      callback,\n      cfg: {\n        ...labelCfg,\n        offset: adaptOffset(labelCfg.type, labelCfg.offset),\n        type: 'pie',\n      },\n    });\n  }\n  return params;\n}\n\n/**\n * statistic options 处理\n * 1. 默认继承 default options 的样式\n * 2. 默认使用 meta 的 formatter\n */\nexport function transformStatisticOptions(options: PieOptions): PieOptions {\n  const { innerRadius, statistic, angleField, colorField, meta, locale } = options;\n\n  const i18n = getLocale(locale);\n\n  if (innerRadius && statistic) {\n    let { title: titleOpt, content: contentOpt } = deepAssign({}, DEFAULT_OPTIONS.statistic, statistic);\n    if (titleOpt !== false) {\n      titleOpt = deepAssign(\n        {},\n        {\n          formatter: (datum: Datum) => {\n            // 交互中, datum existed.\n            const text = datum\n              ? datum[colorField]\n              : !isNil(titleOpt.content)\n              ? titleOpt.content\n              : i18n.get(['statistic', 'total']);\n            const metaFormatter = get(meta, [colorField, 'formatter']) || ((v) => v);\n            return metaFormatter(text);\n          },\n        },\n        titleOpt\n      );\n    }\n    if (contentOpt !== false) {\n      contentOpt = deepAssign(\n        {},\n        {\n          formatter: (datum: Datum, data: Data) => {\n            const dataValue = datum ? datum[angleField] : getTotalValue(data, angleField);\n            const metaFormatter = get(meta, [angleField, 'formatter']) || ((v) => v);\n            // 交互中\n            if (datum) {\n              return metaFormatter(dataValue);\n            }\n            return !isNil(contentOpt.content) ? contentOpt.content : metaFormatter(dataValue);\n          },\n        },\n        contentOpt\n      );\n    }\n\n    return deepAssign({}, { statistic: { title: titleOpt, content: contentOpt } }, options);\n  }\n  return options;\n}\n\n/**\n * statistic 中心文本配置\n * @param params\n */\nexport function pieAnnotation(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { innerRadius, statistic } = transformStatisticOptions(options);\n  // 先清空标注，再重新渲染\n  chart.getController('annotation').clear(true);\n\n  // 先进行其他 annotations，再去渲染统计文本\n  flow(annotation())(params);\n\n  /** 中心文本 指标卡 */\n  if (innerRadius && statistic) {\n    renderStatistic(chart, { statistic, plotType: 'pie' });\n  }\n\n  return params;\n}\n\n/**\n * 饼图 tooltip 配置\n * 1. 强制 tooltip.shared 为 false\n * @param params\n */\nfunction tooltip(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { tooltip, colorField, angleField, data } = options;\n\n  if (tooltip === false) {\n    chart.tooltip(tooltip);\n  } else {\n    chart.tooltip(deepAssign({}, tooltip, { shared: false }));\n\n    // 主要解决 all zero， 对于非 all zero 不再适用\n    if (isAllZero(data, angleField)) {\n      let fields = get(tooltip, 'fields');\n      let formatter = get(tooltip, 'formatter');\n\n      if (isEmpty(get(tooltip, 'fields'))) {\n        fields = [colorField, angleField];\n        formatter = formatter || ((datum) => ({ name: datum[colorField], value: toString(datum[angleField]) }));\n      }\n      chart.geometries[0].tooltip(fields.join('*'), getMappingFunction(fields, formatter));\n    }\n  }\n\n  return params;\n}\n\n/**\n * Interaction 配置 (饼图特殊的 interaction, 中心文本变更的时候，需要将一些配置参数传进去）\n * @param params\n */\nexport function interaction(params: Params<PieOptions>): Params<PieOptions> {\n  const { chart, options } = params;\n  const { interactions, statistic, annotations } = transformStatisticOptions(options);\n\n  each(interactions, (i: Interaction) => {\n    if (i.enable === false) {\n      chart.removeInteraction(i.type);\n    } else if (i.type === 'pie-statistic-active') {\n      // 只针对 start 阶段的配置，进行添加参数信息\n      let startStages = [];\n      if (!i.cfg?.start) {\n        startStages = [\n          {\n            trigger: 'element:mouseenter',\n            action: `${PIE_STATISTIC}:change`,\n            arg: { statistic, annotations },\n          },\n        ];\n      }\n      each(i.cfg?.start, (stage) => {\n        startStages.push({ ...stage, arg: { statistic, annotations } });\n      });\n      chart.interaction(i.type, deepAssign({}, i.cfg, { start: startStages }));\n    } else {\n      chart.interaction(i.type, i.cfg || {});\n    }\n  });\n\n  return params;\n}\n\n/**\n * 饼图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<PieOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow<Params<PieOptions>>(\n    pattern('pieStyle'),\n    geometry,\n    meta,\n    theme,\n    coordinate,\n    legend,\n    tooltip,\n    label,\n    state,\n    /** 指标卡中心文本 放在下层 */\n    pieAnnotation,\n    interaction,\n    animation\n  )(params);\n}\n"]}