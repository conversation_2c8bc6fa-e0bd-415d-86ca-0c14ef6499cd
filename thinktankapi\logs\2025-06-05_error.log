2025-06-05 13:38:07.043 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 13:38:07.044 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 13:38:08.159 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 13:38:08.159 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 13:38:08.161 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 13:38:08.824 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 13:38:09.569 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 13:38:09.569 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 13:38:28.215 | e2314c5673e84d1c81e26c4ccd027fa6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:28.458 | 3fe09eb6b3e3428db7140f5ec810010a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:38.616 | 01e407e3858746f984d34296dd567ea2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:38.936 | d4df64e76614443eb131da823a80bd43 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:39.650 | 32fefd93846a4f9997ea7e8cecf109a7 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.881 | b46827bb957d4a7498ef3db09b63afc5 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.955 | 602734b7617040bb91c2b4e7e7d48443 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:38:45.594 | 878ea43676b64f23a234783cf1e3d199 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.595 | 28e68eff23db4f0f9a1456bd8476e708 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.627 | a46da5c54262468dbd6a7faffc0a7aca | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:39:14.641 | 42e67e9a5a9a4dc681e329f955775cb5 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 14:19:11.893 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 14:19:11.893 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 14:19:13.244 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 14:19:13.245 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 14:19:13.246 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 14:19:13.871 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 14:19:14.513 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 14:19:14.515 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 14:19:35.425 | 6bc4e1a438cb43bdbc6c40c65a0b5ac9 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-05 14:19:35.483 | f487fca699b540e08dc92a59fcf948d0 | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-05 14:19:35.550 | 5233fe02108743e6beceba5ddefaba27 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为16b9e53c-5eb5-421a-8f57-f4a8fe6fe105的会话获取图片验证码成功
2025-06-05 14:19:53.699 | a89a85525bfc4542b2726e8e394122d8 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 14:19:53.953 | 76e6556166a243c899c05b97239e4ff2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:19:54.181 | c955186852fd43fe9357b8b4010e553f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:20:07.102 | a916a42b4ae949269d605c98523a6deb | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 14:20:07.260 | 3eefbad70fe1402fbe46e41273862057 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 14:20:07.327 | 7fd39f95308c44579934594adde76254 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 14:40:43.177 | c9e82b848cca4b06b75f45c9609d43d5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:40:43.433 | c925598f85214739b8df3109d02dfe9f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:41:11.350 | d1d82f0688344760b71288f7d5e6a0f0 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:41:11.682 | d78c077b29944a2486ad5c6480b425c6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 14:58:45.691 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 14:58:45.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 14:58:55.526 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 14:58:55.526 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 14:58:57.041 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 14:58:57.041 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 14:58:57.043 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 14:58:57.811 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 14:58:58.633 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 14:58:58.634 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 14:59:06.289 | 6e092fe9a02846408e68a45fb7bc229c | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:59:06.631 | d2f2150918084e17940ba70b8aa56328 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:03:12.091 | 3422cd57d62445929b8156d0dd2d3b71 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:03:12.359 | 7e1adf481a7244d29c62b1fa371b6dff | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:10:28.113 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:10:28.113 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:10:29.252 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:10:29.252 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:10:29.254 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:10:29.964 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:10:30.769 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:10:30.769 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:10:39.633 | 416992dfcf78426f804be0a70e64459f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:10:39.898 | 274d02ec41044d6aa07140741b86f759 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:47:29.800 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:47:29.801 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:47:31.145 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:47:31.145 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:47:31.148 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:47:32.060 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:47:32.789 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:47:32.789 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:52:54.603 | 7cd1abe0b04d4a2c9d580ba8e1e05639 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为f30da454-16c3-40e9-ba91-618bfc44bf00的会话获取图片验证码成功
2025-06-05 15:53:03.300 | f6a94c257d80442db609c0ca4733e542 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 15:53:03.532 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 15:53:03.533 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 15:53:06.459 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:53:06.459 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:53:07.404 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:53:07.404 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:53:07.408 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:53:08.067 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:53:08.660 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:53:08.660 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:53:08.819 | 2bb8b93ae1a647f6ba8aea214991a152 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:53:09.352 | 7da7c0a6e7dc497686e4d2ce738db706 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:53:59.951 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 15:53:59.953 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 15:54:02.435 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 15:54:02.436 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:54:03.563 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:54:03.564 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:54:03.567 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:54:04.157 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:54:04.824 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:54:04.824 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 15:56:30.972 | 04a107a1d8794ef4905d7292639569a5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:56:31.065 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 15:56:31.066 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 15:56:34.418 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 15:56:34.419 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:56:35.450 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:56:35.451 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:56:35.453 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:56:35.939 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:56:36.751 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:56:36.751 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 15:56:36.901 | 9d9a00a50fb14561aa23a989f4046af3 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:57:02.916 | d49ed30b6454461a8044fe32f496ab39 | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 15:59:48.497 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 15:59:48.498 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 15:59:49.563 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 15:59:49.564 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 15:59:49.565 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 15:59:50.120 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 15:59:50.855 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 15:59:50.855 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 15:59:53.488 | 1052959645024af186f4f2399240edf9 | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 15:59:57.241 | 91ea9681579047cc8cfaf0d42cb0e63e | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:59:57.442 | 0a11537fbd164bd78403eaed00dd987c | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:59:58.000 | 63a284908ddf4d719e458c47debb56a2 | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 15:59:58.622 | a4bba40f02d34be7be46a15b5593369c | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 16:04:27.928 |  | INFO     | server:lifespan:35 - RuoYi-FastAPI开始启动
2025-06-05 16:04:27.929 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:04:29.632 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:04:29.633 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:04:29.636 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:04:30.931 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:04:32.145 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:04:32.146 |  | INFO     | server:lifespan:42 - RuoYi-FastAPI启动成功
2025-06-05 16:06:42.818 | ed631e015a274816a6fc47edcdb4bd78 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 16:06:43.012 | 6e89b182f40c41c2973893f6fdd9e6a6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 16:06:47.573 | 43fa78e74777472798d8639a2590ccfa | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 16:08:42.814 | 2805a7f7a0964a4b95157de6b67e310f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 16:08:43.037 | 8c999f68708d4df1889d81db123e6398 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 16:08:46.255 | 5e603db4d022498894a1adeb0b526e7c | ERROR    | module_admin.controller.warning_controller:get_warning_list:43 - 获取警告列表失败: type object 'PageUtil' has no attribute 'get_page_result'
2025-06-05 16:09:02.041 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:09:02.042 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:09:12.724 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:09:12.725 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:09:22.527 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:09:22.527 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:09:23.472 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:09:23.473 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:09:23.476 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:09:23.967 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:09:24.642 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:09:24.643 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 16:09:44.703 | 153bf3f578294898ae2be1415b31a83c | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为b65610b7-7c8e-4d3a-8971-7ca017e713fc的会话获取图片验证码成功
2025-06-05 16:10:03.105 | 6a2cd7f79cfa49a99eec3ac40d608b96 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 16:10:03.358 | c40be72d9b9641cbbb4d77ef33b923f5 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 16:10:03.890 | 6cef6ca5d61c47a08206377a3f8b9d2e | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 16:35:23.067 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 16:35:23.068 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-06-05 16:35:26.813 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 16:35:26.813 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 16:35:27.922 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 16:35:27.922 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 16:35:27.924 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 16:35:28.599 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 16:35:29.314 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 16:35:29.315 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
