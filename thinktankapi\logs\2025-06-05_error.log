2025-06-05 13:38:07.043 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 13:38:07.044 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 13:38:08.159 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 13:38:08.159 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 13:38:08.161 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 13:38:08.824 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 13:38:09.569 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 13:38:09.569 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 13:38:28.215 | e2314c5673e84d1c81e26c4ccd027fa6 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:28.458 | 3fe09eb6b3e3428db7140f5ec810010a | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:38.616 | 01e407e3858746f984d34296dd567ea2 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 13:38:38.936 | d4df64e76614443eb131da823a80bd43 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 13:38:39.650 | 32fefd93846a4f9997ea7e8cecf109a7 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.881 | b46827bb957d4a7498ef3db09b63afc5 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:39.955 | 602734b7617040bb91c2b4e7e7d48443 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:38:45.594 | 878ea43676b64f23a234783cf1e3d199 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.595 | 28e68eff23db4f0f9a1456bd8476e708 | INFO     | module_admin.controller.dict_controller:query_system_dict_type_data:145 - 获取成功
2025-06-05 13:38:45.627 | a46da5c54262468dbd6a7faffc0a7aca | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 13:39:14.641 | 42e67e9a5a9a4dc681e329f955775cb5 | INFO     | module_admin.controller.menu_controller:get_system_menu_list:56 - 获取成功
2025-06-05 14:17:10.793 |  | INFO     | server:lifespan:34 - RuoYi-FastAPI开始启动
2025-06-05 14:17:10.794 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-06-05 14:17:12.453 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-06-05 14:17:12.454 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-06-05 14:17:12.456 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-06-05 14:17:13.401 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-06-05 14:17:14.130 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-06-05 14:17:14.130 |  | INFO     | server:lifespan:41 - RuoYi-FastAPI启动成功
2025-06-05 14:21:46.648 | 4f0de21346ac4c92a4b62439486b9c1e | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为478ce860-6855-40b8-b48a-bf5da237477a的会话获取图片验证码成功
2025-06-05 14:21:54.906 | ce73171007af4dc184031366cdf1476d | WARNING  | module_admin.service.login_service:__check_login_captcha:161 - 验证码错误
2025-06-05 14:21:54.906 | ce73171007af4dc184031366cdf1476d | WARNING  | module_admin.annotation.log_annotation:wrapper:123 - 验证码错误
2025-06-05 14:21:55.210 | dc957caeb51347af92a10942455f939f | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为7e938785-8285-4fa3-bdeb-884cf95ff210的会话获取图片验证码成功
2025-06-05 14:22:00.118 | 73d03e70d4ee40e3871302be7e60f917 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 14:22:00.838 | a8d51141be3a472a8b3124866115d046 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 14:22:01.167 | 3f5856f0b3f44c48a7a7a31cfeb78ede | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:11:29.257 | 33de90e57e054ad082136af01abbd136 | WARNING  | module_admin.service.login_service:get_current_user:259 - 用户token已失效，请重新登录
2025-06-05 15:11:29.358 | cca07c3256c14bce80b2a349a30e212a | INFO     | module_admin.controller.login_controller:logout:146 - 退出成功
2025-06-05 15:11:29.387 | a9bb3f5a953b433d8e798c439559f323 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为0f863353-b8b9-4d9d-a628-3b22ba4c9f0d的会话获取图片验证码成功
2025-06-05 15:11:40.852 | 3ebb3df930364ef1ac5223e38b1982b9 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-06-05 15:11:41.240 | 409748ecc4644acda1bc379f83d6dc1f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-06-05 15:11:41.887 | 75dbe22b98fd4e8abb48c135086e7128 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-06-05 15:23:28.833 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-06-05 15:23:28.834 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
