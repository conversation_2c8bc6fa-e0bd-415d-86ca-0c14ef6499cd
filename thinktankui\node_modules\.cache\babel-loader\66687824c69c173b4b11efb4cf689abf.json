{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\cursor.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\cursor.js", "mtime": 1749109532806}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_parchment", "require", "_text", "_interopRequireDefault", "<PERSON><PERSON><PERSON>", "_EmbedBlot", "scroll", "domNode", "selection", "_this", "_classCallCheck2", "default", "_callSuper2", "textNode", "document", "createTextNode", "CONTENTS", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_inherits2", "_createClass2", "key", "value", "detach", "parent", "<PERSON><PERSON><PERSON><PERSON>", "format", "name", "_superPropGet2", "target", "index", "statics", "scope", "<PERSON><PERSON>", "BLOCK_BLOT", "offset", "length", "optimize", "formatAt", "node", "position", "data", "remove", "restore", "composing", "range", "getNativeRange", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "insertBefore", "prevTextBlot", "prev", "TextBlot", "prevTextLength", "nextTextBlot", "next", "nextText", "text", "newText", "split", "join", "mergedTextBlot", "insertAt", "newTextNode", "create", "remapOffset", "start", "end", "startNode", "startOffset", "endNode", "endOffset", "update", "mutations", "context", "_this2", "some", "mutation", "type", "tagName", "isolate", "unwrap", "undefined", "EmbedBlot", "_defineProperty2", "_default", "exports"], "sources": ["../../src/blots/cursor.ts"], "sourcesContent": ["import { EmbedB<PERSON>, Scope } from 'parchment';\nimport type { Parent, ScrollBlot } from 'parchment';\nimport type Selection from '../core/selection.js';\nimport TextBlot from './text.js';\nimport type { EmbedContextRange } from './embed.js';\n\nclass Cursor extends EmbedBlot {\n  static blotName = 'cursor';\n  static className = 'ql-cursor';\n  static tagName = 'span';\n  static CONTENTS = '\\uFEFF'; // Zero width no break space\n\n  static value() {\n    return undefined;\n  }\n\n  selection: Selection;\n  textNode: Text;\n  savedLength: number;\n\n  constructor(scroll: ScrollBlot, domNode: HTMLElement, selection: Selection) {\n    super(scroll, domNode);\n    this.selection = selection;\n    this.textNode = document.createTextNode(Cursor.CONTENTS);\n    this.domNode.appendChild(this.textNode);\n    this.savedLength = 0;\n  }\n\n  detach() {\n    // super.detach() will also clear domNode.__blot\n    if (this.parent != null) this.parent.removeChild(this);\n  }\n\n  format(name: string, value: unknown) {\n    if (this.savedLength !== 0) {\n      super.format(name, value);\n      return;\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let target: Parent | this = this;\n    let index = 0;\n    while (target != null && target.statics.scope !== Scope.BLOCK_BLOT) {\n      index += target.offset(target.parent);\n      target = target.parent;\n    }\n    if (target != null) {\n      this.savedLength = Cursor.CONTENTS.length;\n      // @ts-expect-error TODO: allow empty context in Parchment\n      target.optimize();\n      target.formatAt(index, Cursor.CONTENTS.length, name, value);\n      this.savedLength = 0;\n    }\n  }\n\n  index(node: Node, offset: number) {\n    if (node === this.textNode) return 0;\n    return super.index(node, offset);\n  }\n\n  length() {\n    return this.savedLength;\n  }\n\n  position(): [Text, number] {\n    return [this.textNode, this.textNode.data.length];\n  }\n\n  remove() {\n    super.remove();\n    // @ts-expect-error Fix me later\n    this.parent = null;\n  }\n\n  restore(): EmbedContextRange | null {\n    if (this.selection.composing || this.parent == null) return null;\n    const range = this.selection.getNativeRange();\n    // Browser may push down styles/nodes inside the cursor blot.\n    // https://dvcs.w3.org/hg/editing/raw-file/tip/editing.html#push-down-values\n    while (\n      this.domNode.lastChild != null &&\n      this.domNode.lastChild !== this.textNode\n    ) {\n      // @ts-expect-error Fix me later\n      this.domNode.parentNode.insertBefore(\n        this.domNode.lastChild,\n        this.domNode,\n      );\n    }\n\n    const prevTextBlot = this.prev instanceof TextBlot ? this.prev : null;\n    const prevTextLength = prevTextBlot ? prevTextBlot.length() : 0;\n    const nextTextBlot = this.next instanceof TextBlot ? this.next : null;\n    // @ts-expect-error TODO: make TextBlot.text public\n    const nextText = nextTextBlot ? nextTextBlot.text : '';\n    const { textNode } = this;\n    // take text from inside this blot and reset it\n    const newText = textNode.data.split(Cursor.CONTENTS).join('');\n    textNode.data = Cursor.CONTENTS;\n\n    // proactively merge TextBlots around cursor so that optimization\n    // doesn't lose the cursor.  the reason we are here in cursor.restore\n    // could be that the user clicked in prevTextBlot or nextTextBlot, or\n    // the user typed something.\n    let mergedTextBlot;\n    if (prevTextBlot) {\n      mergedTextBlot = prevTextBlot;\n      if (newText || nextTextBlot) {\n        prevTextBlot.insertAt(prevTextBlot.length(), newText + nextText);\n        if (nextTextBlot) {\n          nextTextBlot.remove();\n        }\n      }\n    } else if (nextTextBlot) {\n      mergedTextBlot = nextTextBlot;\n      nextTextBlot.insertAt(0, newText);\n    } else {\n      const newTextNode = document.createTextNode(newText);\n      mergedTextBlot = this.scroll.create(newTextNode);\n      this.parent.insertBefore(mergedTextBlot, this);\n    }\n\n    this.remove();\n    if (range) {\n      // calculate selection to restore\n      const remapOffset = (node: Node, offset: number) => {\n        if (prevTextBlot && node === prevTextBlot.domNode) {\n          return offset;\n        }\n        if (node === textNode) {\n          return prevTextLength + offset - 1;\n        }\n        if (nextTextBlot && node === nextTextBlot.domNode) {\n          return prevTextLength + newText.length + offset;\n        }\n        return null;\n      };\n\n      const start = remapOffset(range.start.node, range.start.offset);\n      const end = remapOffset(range.end.node, range.end.offset);\n      if (start !== null && end !== null) {\n        return {\n          startNode: mergedTextBlot.domNode,\n          startOffset: start,\n          endNode: mergedTextBlot.domNode,\n          endOffset: end,\n        };\n      }\n    }\n    return null;\n  }\n\n  update(mutations: MutationRecord[], context: Record<string, unknown>) {\n    if (\n      mutations.some((mutation) => {\n        return (\n          mutation.type === 'characterData' && mutation.target === this.textNode\n        );\n      })\n    ) {\n      const range = this.restore();\n      if (range) context.range = range;\n    }\n  }\n\n  // Avoid .ql-cursor being a descendant of `<a/>`.\n  // The reason is Safari pushes down `<a/>` on text insertion.\n  // That will cause DOM nodes not sync with the model.\n  //\n  // For example ({I} is the caret), given the markup:\n  //    <a><span class=\"ql-cursor\">\\uFEFF{I}</span></a>\n  // When typing a char \"x\", `<a/>` will be pushed down inside the `<span>` first:\n  //    <span class=\"ql-cursor\"><a>\\uFEFF{I}</a></span>\n  // And then \"x\" will be inserted after `<a/>`:\n  //    <span class=\"ql-cursor\"><a>\\uFEFF</a>d{I}</span>\n  optimize(context?: unknown) {\n    // @ts-expect-error Fix me later\n    super.optimize(context);\n\n    let { parent } = this;\n    while (parent) {\n      if (parent.domNode.tagName === 'A') {\n        this.savedLength = Cursor.CONTENTS.length;\n        // @ts-expect-error TODO: make isolate generic\n        parent.isolate(this.offset(parent), this.length()).unwrap();\n        this.savedLength = 0;\n        break;\n      }\n      parent = parent.parent;\n    }\n  }\n\n  value() {\n    return '';\n  }\n}\n\nexport default Cursor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgC,IAG1BG,MAAM,0BAAAC,UAAA;EAcV,SAAAD,OAAYE,MAAkB,EAAEC,OAAoB,EAAEC,SAAoB,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAP,MAAA;IAC1EK,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAP,MAAA,GAAME,MAAM,EAAEC,OAAO;IACrBE,KAAA,CAAKD,SAAS,GAAGA,SAAS;IAC1BC,KAAA,CAAKI,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAACX,MAAM,CAACY,QAAQ,CAAC;IACxDP,KAAA,CAAKF,OAAO,CAACU,WAAW,CAACR,KAAA,CAAKI,QAAQ,CAAC;IACvCJ,KAAA,CAAKS,WAAW,GAAG,CAAC;IAAA,OAAAT,KAAA;EACtB;EAAA,IAAAU,UAAA,CAAAR,OAAA,EAAAP,MAAA,EAAAC,UAAA;EAAA,WAAAe,aAAA,CAAAT,OAAA,EAAAP,MAAA;IAAAiB,GAAA;IAAAC,KAAA,EAEA,SAAAC,MAAMA,CAAA,EAAG;MACP;MACA,IAAI,IAAI,CAACC,MAAM,IAAI,IAAI,EAAE,IAAI,CAACA,MAAM,CAACC,WAAW,CAAC,IAAI,CAAC;IACxD;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAEA,SAAAI,MAAMA,CAACC,IAAY,EAAEL,KAAc,EAAE;MACnC,IAAI,IAAI,CAACJ,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAAU,cAAA,CAAAjB,OAAA,EAAAP,MAAA,sBAAauB,IAAI,EAAEL,KAAK;QACxB;MACF;MACA;MACA;MACA,IAAIO,MAAqB,GAAG,IAAI;MAChC,IAAIC,KAAK,GAAG,CAAC;MACb,OAAOD,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACE,OAAO,CAACC,KAAK,KAAKC,gBAAK,CAACC,UAAU,EAAE;QAClEJ,KAAK,IAAID,MAAM,CAACM,MAAM,CAACN,MAAM,CAACL,MAAM,CAAC;QACrCK,MAAM,GAAGA,MAAM,CAACL,MAAM;MACxB;MACA,IAAIK,MAAM,IAAI,IAAI,EAAE;QAClB,IAAI,CAACX,WAAW,GAAGd,MAAM,CAACY,QAAQ,CAACoB,MAAM;QACzC;QACAP,MAAM,CAACQ,QAAQ,CAAC,CAAC;QACjBR,MAAM,CAACS,QAAQ,CAACR,KAAK,EAAE1B,MAAM,CAACY,QAAQ,CAACoB,MAAM,EAAET,IAAI,EAAEL,KAAK,CAAC;QAC3D,IAAI,CAACJ,WAAW,GAAG,CAAC;MACtB;IACF;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAQ,KAAKA,CAACS,IAAU,EAAEJ,MAAc,EAAE;MAChC,IAAII,IAAI,KAAK,IAAI,CAAC1B,QAAQ,EAAE,OAAO,CAAC;MACpC,WAAAe,cAAA,CAAAjB,OAAA,EAAAP,MAAA,qBAAmBmC,IAAI,EAAEJ,MAAM;IACjC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAAc,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAAClB,WAAW;IACzB;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAkB,QAAQA,CAAA,EAAmB;MACzB,OAAO,CAAC,IAAI,CAAC3B,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC4B,IAAI,CAACL,MAAM,CAAC;IACnD;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAEA,SAAAoB,MAAMA,CAAA,EAAG;MACP,IAAAd,cAAA,CAAAjB,OAAA,EAAAP,MAAA;MACA;MACA,IAAI,CAACoB,MAAM,GAAG,IAAI;IACpB;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAqB,OAAOA,CAAA,EAA6B;MAClC,IAAI,IAAI,CAACnC,SAAS,CAACoC,SAAS,IAAI,IAAI,CAACpB,MAAM,IAAI,IAAI,EAAE,OAAO,IAAI;MAChE,IAAMqB,KAAK,GAAG,IAAI,CAACrC,SAAS,CAACsC,cAAc,CAAC,CAAC;MAC7C;MACA;MACA,OACE,IAAI,CAACvC,OAAO,CAACwC,SAAS,IAAI,IAAI,IAC9B,IAAI,CAACxC,OAAO,CAACwC,SAAS,KAAK,IAAI,CAAClC,QAAQ,EACxC;QACA;QACA,IAAI,CAACN,OAAO,CAACyC,UAAU,CAACC,YAAY,CAClC,IAAI,CAAC1C,OAAO,CAACwC,SAAS,EACtB,IAAI,CAACxC,OACP,CAAC;MACH;MAEA,IAAM2C,YAAY,GAAG,IAAI,CAACC,IAAI,YAAYC,aAAQ,GAAG,IAAI,CAACD,IAAI,GAAG,IAAI;MACrE,IAAME,cAAc,GAAGH,YAAY,GAAGA,YAAY,CAACd,MAAM,CAAC,CAAC,GAAG,CAAC;MAC/D,IAAMkB,YAAY,GAAG,IAAI,CAACC,IAAI,YAAYH,aAAQ,GAAG,IAAI,CAACG,IAAI,GAAG,IAAI;MACrE;MACA,IAAMC,QAAQ,GAAGF,YAAY,GAAGA,YAAY,CAACG,IAAI,GAAG,EAAE;MACtD,IAAQ5C,QAAA,GAAa,IAAI,CAAjBA,QAAA;MACR;MACA,IAAM6C,OAAO,GAAG7C,QAAQ,CAAC4B,IAAI,CAACkB,KAAK,CAACvD,MAAM,CAACY,QAAQ,CAAC,CAAC4C,IAAI,CAAC,EAAE,CAAC;MAC7D/C,QAAQ,CAAC4B,IAAI,GAAGrC,MAAM,CAACY,QAAQ;;MAE/B;MACA;MACA;MACA;MACA,IAAI6C,cAAc;MAClB,IAAIX,YAAY,EAAE;QAChBW,cAAc,GAAGX,YAAY;QAC7B,IAAIQ,OAAO,IAAIJ,YAAY,EAAE;UAC3BJ,YAAY,CAACY,QAAQ,CAACZ,YAAY,CAACd,MAAM,CAAC,CAAC,EAAEsB,OAAO,GAAGF,QAAQ,CAAC;UAChE,IAAIF,YAAY,EAAE;YAChBA,YAAY,CAACZ,MAAM,CAAC,CAAC;UACvB;QACF;MACF,CAAC,MAAM,IAAIY,YAAY,EAAE;QACvBO,cAAc,GAAGP,YAAY;QAC7BA,YAAY,CAACQ,QAAQ,CAAC,CAAC,EAAEJ,OAAO,CAAC;MACnC,CAAC,MAAM;QACL,IAAMK,WAAW,GAAGjD,QAAQ,CAACC,cAAc,CAAC2C,OAAO,CAAC;QACpDG,cAAc,GAAG,IAAI,CAACvD,MAAM,CAAC0D,MAAM,CAACD,WAAW,CAAC;QAChD,IAAI,CAACvC,MAAM,CAACyB,YAAY,CAACY,cAAc,EAAE,IAAI,CAAC;MAChD;MAEA,IAAI,CAACnB,MAAM,CAAC,CAAC;MACb,IAAIG,KAAK,EAAE;QACT;QACA,IAAMoB,WAAW,GAAG,SAAdA,WAAWA,CAAI1B,IAAU,EAAEJ,MAAc,EAAK;UAClD,IAAIe,YAAY,IAAIX,IAAI,KAAKW,YAAY,CAAC3C,OAAO,EAAE;YACjD,OAAO4B,MAAM;UACf;UACA,IAAII,IAAI,KAAK1B,QAAQ,EAAE;YACrB,OAAOwC,cAAc,GAAGlB,MAAM,GAAG,CAAC;UACpC;UACA,IAAImB,YAAY,IAAIf,IAAI,KAAKe,YAAY,CAAC/C,OAAO,EAAE;YACjD,OAAO8C,cAAc,GAAGK,OAAO,CAACtB,MAAM,GAAGD,MAAM;UACjD;UACA,OAAO,IAAI;QACb,CAAC;QAED,IAAM+B,KAAK,GAAGD,WAAW,CAACpB,KAAK,CAACqB,KAAK,CAAC3B,IAAI,EAAEM,KAAK,CAACqB,KAAK,CAAC/B,MAAM,CAAC;QAC/D,IAAMgC,GAAG,GAAGF,WAAW,CAACpB,KAAK,CAACsB,GAAG,CAAC5B,IAAI,EAAEM,KAAK,CAACsB,GAAG,CAAChC,MAAM,CAAC;QACzD,IAAI+B,KAAK,KAAK,IAAI,IAAIC,GAAG,KAAK,IAAI,EAAE;UAClC,OAAO;YACLC,SAAS,EAAEP,cAAc,CAACtD,OAAO;YACjC8D,WAAW,EAAEH,KAAK;YAClBI,OAAO,EAAET,cAAc,CAACtD,OAAO;YAC/BgE,SAAS,EAAEJ;UACb,CAAC;QACH;MACF;MACA,OAAO,IAAI;IACb;EAAA;IAAA9C,GAAA;IAAAC,KAAA,EAEA,SAAAkD,MAAMA,CAACC,SAA2B,EAAEC,OAAgC,EAAE;MAAA,IAAAC,MAAA;MACpE,IACEF,SAAS,CAACG,IAAI,CAAE,UAAAC,QAAQ,EAAK;QAC3B,OACEA,QAAQ,CAACC,IAAI,KAAK,eAAe,IAAID,QAAQ,CAAChD,MAAM,KAAK8C,MAAI,CAAC9D,QAAQ;MAE1E,CAAC,CAAC,EACF;QACA,IAAMgC,KAAK,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC;QAC5B,IAAIE,KAAK,EAAE6B,OAAO,CAAC7B,KAAK,GAAGA,KAAK;MAClC;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;IAAAxB,GAAA;IAAAC,KAAA,EACA,SAAAe,QAAQA,CAACqC,OAAiB,EAAE;MAC1B;MACA,IAAA9C,cAAA,CAAAjB,OAAA,EAAAP,MAAA,wBAAesE,OAAO;MAEtB,IAAMlD,MAAA,GAAW,IAAI,CAAfA,MAAA;MACN,OAAOA,MAAM,EAAE;QACb,IAAIA,MAAM,CAACjB,OAAO,CAACwE,OAAO,KAAK,GAAG,EAAE;UAClC,IAAI,CAAC7D,WAAW,GAAGd,MAAM,CAACY,QAAQ,CAACoB,MAAM;UACzC;UACAZ,MAAM,CAACwD,OAAO,CAAC,IAAI,CAAC7C,MAAM,CAACX,MAAM,CAAC,EAAE,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC6C,MAAM,CAAC,CAAC;UAC3D,IAAI,CAAC/D,WAAW,GAAG,CAAC;UACpB;QACF;QACAM,MAAM,GAAGA,MAAM,CAACA,MAAM;MACxB;IACF;EAAA;IAAAH,GAAA;IAAAC,KAAA,EAEA,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,EAAE;IACX;EAAA;IAAAD,GAAA;IAAAC,KAAA;IAxL4B;;IAE5B,SAAOA,KAAKA,CAAA,EAAG;MACb,OAAO4D,SAAS;IAClB;EAAA;AAAA,EARmBC,oBAAS;AAAA,IAAAC,gBAAA,CAAAzE,OAAA,EAAxBP,MAAM,cACQ,QAAQ;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EADtBP,MAAM,eAES,WAAW;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EAF1BP,MAAM,aAGO,MAAM;AAAA,IAAAgF,gBAAA,CAAAzE,OAAA,EAHnBP,MAAM,cAIQ,QAAQ;AAAA,IAAAiF,QAAA,GAAAC,OAAA,CAAA3E,OAAA,GA2LbP,MAAM", "ignoreList": []}]}