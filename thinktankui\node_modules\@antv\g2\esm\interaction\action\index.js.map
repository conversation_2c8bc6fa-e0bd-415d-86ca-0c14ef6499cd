{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,QAAQ,CAAC,CAAC,eAAe;AAC3D,OAAO,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC", "sourcesContent": ["export { default as Action } from './base'; // 导出 Action 基类\nexport { createAction, registerAction, getActionClass } from './register';\n"]}