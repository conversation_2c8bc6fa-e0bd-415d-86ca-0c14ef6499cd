{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\container.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\blots\\container.js", "mtime": 1749109532690}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfY3JlYXRlQ2xhc3MyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY3JlYXRlQ2xhc3MuanMiKSk7CnZhciBfY2xhc3NDYWxsQ2hlY2syID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvY2xhc3NDYWxsQ2hlY2suanMiKSk7CnZhciBfY2FsbFN1cGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NhbGxTdXBlci5qcyIpKTsKdmFyIF9pbmhlcml0czIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9wYXJjaG1lbnQgPSByZXF1aXJlKCJwYXJjaG1lbnQiKTsKdmFyIENvbnRhaW5lciA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoX0NvbnRhaW5lckJsb3QpIHsKICBmdW5jdGlvbiBDb250YWluZXIoKSB7CiAgICAoMCwgX2NsYXNzQ2FsbENoZWNrMi5kZWZhdWx0KSh0aGlzLCBDb250YWluZXIpOwogICAgcmV0dXJuICgwLCBfY2FsbFN1cGVyMi5kZWZhdWx0KSh0aGlzLCBDb250YWluZXIsIGFyZ3VtZW50cyk7CiAgfQogICgwLCBfaW5oZXJpdHMyLmRlZmF1bHQpKENvbnRhaW5lciwgX0NvbnRhaW5lckJsb3QpOwogIHJldHVybiAoMCwgX2NyZWF0ZUNsYXNzMi5kZWZhdWx0KShDb250YWluZXIpOwp9KF9wYXJjaG1lbnQuQ29udGFpbmVyQmxvdCk7CnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IENvbnRhaW5lcjs="}, {"version": 3, "names": ["_parchment", "require", "Container", "_ContainerBlot", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "ContainerBlot", "_default", "exports"], "sources": ["../../src/blots/container.ts"], "sourcesContent": ["import { ContainerBlot } from 'parchment';\n\nclass Container extends ContainerBlot {}\n\nexport default Container;\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAAyC,IAEnCC,SAAS,0BAAAC,cAAA;EAAA,SAAAD,UAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,SAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,SAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,SAAA,EAAAC,cAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,SAAA;AAAA,EAASQ,wBAAa;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAP,OAAA,GAEtBH,SAAS", "ignoreList": []}]}