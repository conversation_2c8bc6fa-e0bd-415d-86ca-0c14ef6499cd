{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue?vue&type=style&index=0&id=29234bee&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue", "mtime": 1749109381327}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5zY3JlZW5mdWxsLXN2ZyB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBmaWxsOiAjNWE1ZTY2OzsNCiAgd2lkdGg6IDIwcHg7DQogIGhlaWdodDogMjBweDsNCiAgdmVydGljYWwtYWxpZ246IDEwcHg7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Screenfull", "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      if (!screenfull.isEnabled) {\r\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\r\n        return false\r\n      }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"]}]}