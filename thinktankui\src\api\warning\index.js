import request from '@/utils/request'

// ==================== 预警记录相关接口 ====================

// 查询预警记录列表
export function listWarningRecord(query) {
  return request({
    url: '/warning/record/list',
    method: 'get',
    params: query
  })
}

// 查询预警记录详细
export function getWarningRecord(recordId) {
  return request({
    url: '/warning/record/' + recordId,
    method: 'get'
  })
}

// 新增预警记录
export function addWarningRecord(data) {
  return request({
    url: '/warning/record',
    method: 'post',
    data: data
  })
}

// 修改预警记录
export function updateWarningRecord(data) {
  return request({
    url: '/warning/record',
    method: 'put',
    data: data
  })
}

// 删除预警记录
export function delWarningRecord(recordIds) {
  return request({
    url: '/warning/record/' + recordIds,
    method: 'delete'
  })
}

// ==================== 预警方案相关接口 ====================

// 查询预警方案列表
export function listWarningScheme(query) {
  return request({
    url: '/warning/scheme/list',
    method: 'get',
    params: query
  })
}

// 查询预警方案详细
export function getWarningScheme(schemeId) {
  return request({
    url: '/warning/scheme/' + schemeId,
    method: 'get'
  })
}

// 新增预警方案
export function addWarningScheme(data) {
  return request({
    url: '/warning/scheme',
    method: 'post',
    data: data
  })
}

// 修改预警方案
export function updateWarningScheme(data) {
  return request({
    url: '/warning/scheme',
    method: 'put',
    data: data
  })
}

// 删除预警方案
export function delWarningScheme(schemeIds) {
  return request({
    url: '/warning/scheme/' + schemeIds,
    method: 'delete'
  })
}

// ==================== 预警设置相关接口 ====================

// 保存预警设置
export function saveWarningSettings(data) {
  return request({
    url: '/warning/settings',
    method: 'post',
    data: data
  })
}

// 获取预警设置
export function getWarningSettings(schemeId) {
  return request({
    url: '/warning/settings/' + schemeId,
    method: 'get'
  })
}

// ==================== 统计相关接口 ====================

// 获取预警统计信息
export function getWarningStatistics() {
  return request({
    url: '/warning/statistics',
    method: 'get'
  })
}

// ==================== 兼容性接口（保持原有接口名称） ====================

// 查询警告列表（兼容原有接口）
export function listWarning(query) {
  return listWarningRecord(query)
}

// 新建方案（兼容原有接口）
export function createScheme(data) {
  return addWarningScheme(data)
}

// 获取方案列表（兼容原有接口）
export function getSchemeList(query) {
  return listWarningScheme(query)
}

// 删除方案（兼容原有接口）
export function deleteScheme(schemeId) {
  return delWarningScheme(schemeId)
}

// 保存关键词设置（兼容原有接口）
export function saveKeywordSettings(data) {
  // 这里可以扩展为独立的关键词设置接口
  return saveWarningSettings(data)
}

// 获取关键词设置（兼容原有接口）
export function getKeywordSettings(schemeId) {
  return getWarningSettings(schemeId)
}

// 保存自动预警设置（兼容原有接口）
export function saveAutoWarningSettings(data) {
  // 这里可以扩展为独立的自动预警设置接口
  return saveWarningSettings(data)
}

// 获取自动预警设置（兼容原有接口）
export function getAutoWarningSettings(schemeId) {
  return getWarningSettings(schemeId)
}

// 切换预警开关
export function toggleWarningSwitch(data) {
  return request({
    url: '/warning/switch',
    method: 'post',
    data: data
  })
}

// 手动预警
export function manualWarning(data) {
  return request({
    url: '/warning/manual',
    method: 'post',
    data: data
  })
}

// 导出警告数据
export function exportWarning(query) {
  return request({
    url: '/warning/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
