{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\TagsView\\index.vue?vue&type=style&index=0&id=fac8ca64&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1749109381332}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4OA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/TagsView", "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\r\n      <router-link\r\n        v-for=\"tag in visitedViews\"\r\n        ref=\"tag\"\r\n        :key=\"tag.path\"\r\n        :class=\"isActive(tag)?'active':''\"\r\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\r\n        tag=\"span\"\r\n        class=\"tags-view-item\"\r\n        :style=\"activeStyle(tag)\"\r\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\r\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\r\n      >\r\n        {{ tag.title }}\r\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\r\n      </router-link>\r\n    </scroll-pane>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前</li>\r\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\r\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\r\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\r\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ScrollPane from './ScrollPane'\r\nimport path from 'path'\r\n\r\nexport default {\r\n  components: { ScrollPane },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: []\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n      this.moveToCurrentTag()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n  },\r\n  methods: {\r\n    isActive(route) {\r\n      return route.path === this.$route.path\r\n    },\r\n    activeStyle(tag) {\r\n      if (!this.isActive(tag)) return {};\r\n      return {\r\n        \"background-color\": this.theme,\r\n        \"border-color\": this.theme\r\n      };\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    isFirstView() {\r\n      try {\r\n        return this.selectedTag.fullPath === '/index' || this.selectedTag.fullPath === this.visitedViews[1].fullPath\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    isLastView() {\r\n      try {\r\n        return this.selectedTag.fullPath === this.visitedViews[this.visitedViews.length - 1].fullPath\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n      }\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$tab.refreshPage(view);\r\n      if (this.$route.meta.link) {\r\n        this.$store.dispatch('tagsView/delIframeView', this.$route)\r\n      }\r\n    },\r\n    closeSelectedTag(view) {\r\n      this.$tab.closePage(view).then(({ visitedViews }) => {\r\n        if (this.isActive(view)) {\r\n          this.toLastView(visitedViews, view)\r\n        }\r\n      })\r\n    },\r\n    closeRightTags() {\r\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeLeftTags() {\r\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath === this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag.fullPath).catch(()=>{});\r\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$tab.closeAllPage().then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === this.$route.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({ path: '/redirect' + view.fullPath })\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    },\r\n    handleScroll() {\r\n      this.closeMenu()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container {\r\n  height: 34px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n    li {\r\n      margin: 0;\r\n      padding: 7px 16px;\r\n      cursor: pointer;\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}