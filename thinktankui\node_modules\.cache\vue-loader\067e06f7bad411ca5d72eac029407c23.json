{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\TopNav\\index.vue", "mtime": 1749109381328}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjb25zdGFudFJvdXRlcyB9IGZyb20gIkAvcm91dGVyIjsNCmltcG9ydCB7IGlzSHR0cCB9IGZyb20gIkAvdXRpbHMvdmFsaWRhdGUiOw0KDQovLyDpmpDol4/kvqfovrnmoI/ot6/nlLENCmNvbnN0IGhpZGVMaXN0ID0gWycvaW5kZXgnLCAnL3VzZXIvcHJvZmlsZSddOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmhtumDqOagj+WIneWni+aVsA0KICAgICAgdmlzaWJsZU51bWJlcjogNSwNCiAgICAgIC8vIOW9k+WJjea/gOa0u+iPnOWNleeahCBpbmRleA0KICAgICAgY3VycmVudEluZGV4OiB1bmRlZmluZWQNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHRoZW1lKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRoZW1lOw0KICAgIH0sDQogICAgLy8g6aG26YOo5pi+56S66I+c5Y2VDQogICAgdG9wTWVudXMoKSB7DQogICAgICBsZXQgdG9wTWVudXMgPSBbXTsNCiAgICAgIHRoaXMucm91dGVycy5tYXAoKG1lbnUpID0+IHsNCiAgICAgICAgaWYgKG1lbnUuaGlkZGVuICE9PSB0cnVlKSB7DQogICAgICAgICAgLy8g5YW85a656aG26YOo5qCP5LiA57qn6I+c5Y2V5YaF6YOo6Lez6L2sDQogICAgICAgICAgaWYgKG1lbnUucGF0aCA9PT0gIi8iKSB7DQogICAgICAgICAgICB0b3BNZW51cy5wdXNoKG1lbnUuY2hpbGRyZW5bMF0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0b3BNZW51cy5wdXNoKG1lbnUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICByZXR1cm4gdG9wTWVudXM7DQogICAgfSwNCiAgICAvLyDmiYDmnInnmoTot6/nlLHkv6Hmga8NCiAgICByb3V0ZXJzKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnBlcm1pc3Npb24udG9wYmFyUm91dGVyczsNCiAgICB9LA0KICAgIC8vIOiuvue9ruWtkOi3r+eUsQ0KICAgIGNoaWxkcmVuTWVudXMoKSB7DQogICAgICB2YXIgY2hpbGRyZW5NZW51cyA9IFtdOw0KICAgICAgdGhpcy5yb3V0ZXJzLm1hcCgocm91dGVyKSA9PiB7DQogICAgICAgIGZvciAodmFyIGl0ZW0gaW4gcm91dGVyLmNoaWxkcmVuKSB7DQogICAgICAgICAgaWYgKHJvdXRlci5jaGlsZHJlbltpdGVtXS5wYXJlbnRQYXRoID09PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIGlmKHJvdXRlci5wYXRoID09PSAiLyIpIHsNCiAgICAgICAgICAgICAgcm91dGVyLmNoaWxkcmVuW2l0ZW1dLnBhdGggPSAiLyIgKyByb3V0ZXIuY2hpbGRyZW5baXRlbV0ucGF0aDsNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGlmKCFpc0h0dHAocm91dGVyLmNoaWxkcmVuW2l0ZW1dLnBhdGgpKSB7DQogICAgICAgICAgICAgICAgcm91dGVyLmNoaWxkcmVuW2l0ZW1dLnBhdGggPSByb3V0ZXIucGF0aCArICIvIiArIHJvdXRlci5jaGlsZHJlbltpdGVtXS5wYXRoOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICByb3V0ZXIuY2hpbGRyZW5baXRlbV0ucGFyZW50UGF0aCA9IHJvdXRlci5wYXRoOw0KICAgICAgICAgIH0NCiAgICAgICAgICBjaGlsZHJlbk1lbnVzLnB1c2gocm91dGVyLmNoaWxkcmVuW2l0ZW1dKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICByZXR1cm4gY29uc3RhbnRSb3V0ZXMuY29uY2F0KGNoaWxkcmVuTWVudXMpOw0KICAgIH0sDQogICAgLy8g6buY6K6k5r+A5rS755qE6I+c5Y2VDQogICAgYWN0aXZlTWVudSgpIHsNCiAgICAgIGNvbnN0IHBhdGggPSB0aGlzLiRyb3V0ZS5wYXRoOw0KICAgICAgbGV0IGFjdGl2ZVBhdGggPSBwYXRoOw0KICAgICAgaWYgKHBhdGggIT09IHVuZGVmaW5lZCAmJiBwYXRoLmxhc3RJbmRleE9mKCIvIikgPiAwICYmIGhpZGVMaXN0LmluZGV4T2YocGF0aCkgPT09IC0xKSB7DQogICAgICAgIGNvbnN0IHRtcFBhdGggPSBwYXRoLnN1YnN0cmluZygxLCBwYXRoLmxlbmd0aCk7DQogICAgICAgIGlmICghdGhpcy4kcm91dGUubWV0YS5saW5rKSB7DQogICAgICAgICAgYWN0aXZlUGF0aCA9ICIvIiArIHRtcFBhdGguc3Vic3RyaW5nKDAsIHRtcFBhdGguaW5kZXhPZigiLyIpKTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmKCF0aGlzLiRyb3V0ZS5jaGlsZHJlbikgew0KICAgICAgICBhY3RpdmVQYXRoID0gcGF0aDsNCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC90b2dnbGVTaWRlQmFySGlkZScsIHRydWUpOw0KICAgICAgfQ0KICAgICAgdGhpcy5hY3RpdmVSb3V0ZXMoYWN0aXZlUGF0aCk7DQogICAgICByZXR1cm4gYWN0aXZlUGF0aDsNCiAgICB9LA0KICB9LA0KICBiZWZvcmVNb3VudCgpIHsNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5zZXRWaXNpYmxlTnVtYmVyKQ0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLnNldFZpc2libGVOdW1iZXIpDQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5zZXRWaXNpYmxlTnVtYmVyKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmoLnmja7lrr3luqborqHnrpforr7nva7mmL7npLrmoI/mlbANCiAgICBzZXRWaXNpYmxlTnVtYmVyKCkgew0KICAgICAgY29uc3Qgd2lkdGggPSBkb2N1bWVudC5ib2R5LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLndpZHRoIC8gMzsNCiAgICAgIHRoaXMudmlzaWJsZU51bWJlciA9IHBhcnNlSW50KHdpZHRoIC8gODUpOw0KICAgIH0sDQogICAgLy8g6I+c5Y2V6YCJ5oup5LqL5Lu2DQogICAgaGFuZGxlU2VsZWN0KGtleSwga2V5UGF0aCkgew0KICAgICAgdGhpcy5jdXJyZW50SW5kZXggPSBrZXk7DQogICAgICBjb25zdCByb3V0ZSA9IHRoaXMucm91dGVycy5maW5kKGl0ZW0gPT4gaXRlbS5wYXRoID09PSBrZXkpOw0KICAgICAgaWYgKGlzSHR0cChrZXkpKSB7DQogICAgICAgIC8vIGh0dHAocyk6Ly8g6Lev5b6E5paw56qX5Y+j5omT5byADQogICAgICAgIHdpbmRvdy5vcGVuKGtleSwgIl9ibGFuayIpOw0KICAgICAgfSBlbHNlIGlmICghcm91dGUgfHwgIXJvdXRlLmNoaWxkcmVuKSB7DQogICAgICAgIC8vIOayoeacieWtkOi3r+eUsei3r+W+hOWGhemDqOaJk+W8gA0KICAgICAgICBjb25zdCByb3V0ZU1lbnUgPSB0aGlzLmNoaWxkcmVuTWVudXMuZmluZChpdGVtID0+IGl0ZW0ucGF0aCA9PT0ga2V5KTsNCiAgICAgICAgaWYgKHJvdXRlTWVudSAmJiByb3V0ZU1lbnUucXVlcnkpIHsNCiAgICAgICAgICBsZXQgcXVlcnkgPSBKU09OLnBhcnNlKHJvdXRlTWVudS5xdWVyeSk7DQogICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiBrZXksIHF1ZXJ5OiBxdWVyeSB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6IGtleSB9KTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3RvZ2dsZVNpZGVCYXJIaWRlJywgdHJ1ZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDmmL7npLrlt6bkvqfogZTliqjoj5zljZUNCiAgICAgICAgdGhpcy5hY3RpdmVSb3V0ZXMoa2V5KTsNCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC90b2dnbGVTaWRlQmFySGlkZScsIGZhbHNlKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOW9k+WJjea/gOa0u+eahOi3r+eUsQ0KICAgIGFjdGl2ZVJvdXRlcyhrZXkpIHsNCiAgICAgIHZhciByb3V0ZXMgPSBbXTsNCiAgICAgIGlmICh0aGlzLmNoaWxkcmVuTWVudXMgJiYgdGhpcy5jaGlsZHJlbk1lbnVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jaGlsZHJlbk1lbnVzLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgIGlmIChrZXkgPT0gaXRlbS5wYXJlbnRQYXRoIHx8IChrZXkgPT0gImluZGV4IiAmJiAiIiA9PSBpdGVtLnBhdGgpKSB7DQogICAgICAgICAgICByb3V0ZXMucHVzaChpdGVtKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYocm91dGVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJTRVRfU0lERUJBUl9ST1VURVJTIiwgcm91dGVzKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhcHAvdG9nZ2xlU2lkZUJhckhpZGUnLCB0cnVlKTsNCiAgICAgIH0NCiAgICB9DQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item :style=\"{'--theme': theme}\" :index=\"item.path\" :key=\"index\" v-if=\"index < visibleNumber\">\r\n        <svg-icon\r\n        v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n        :icon-class=\"item.meta.icon\"/>\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu :style=\"{'--theme': theme}\" index=\"more\" :key=\"visibleNumber\" v-if=\"topMenus.length > visibleNumber\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          :index=\"item.path\"\r\n          :key=\"index\"\r\n          v-if=\"index >= visibleNumber\">\r\n          <svg-icon\r\n            v-if=\"item.meta && item.meta.icon && item.meta.icon !== '#'\"\r\n            :icon-class=\"item.meta.icon\"/>\r\n          {{ item.meta.title }}\r\n        </el-menu-item>\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { constantRoutes } from \"@/router\";\r\nimport { isHttp } from \"@/utils/validate\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden !== true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path === \"/\") {\r\n            topMenus.push(menu.children[0]);\r\n          } else {\r\n            topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath === undefined) {\r\n            if(router.path === \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if(!isHttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path !== undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) === -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        if (!this.$route.meta.link) {\r\n          activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        }\r\n      } else if(!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path === key);\r\n      if (isHttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\r\n        if (routeMenu && routeMenu.query) {\r\n          let query = JSON.parse(routeMenu.query);\r\n          this.$router.push({ path: key, query: query });\r\n        } else {\r\n          this.$router.push({ path: key });\r\n        }\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if(routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      } else {\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"]}]}