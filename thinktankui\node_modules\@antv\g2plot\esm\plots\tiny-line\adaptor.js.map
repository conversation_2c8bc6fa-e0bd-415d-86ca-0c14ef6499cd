{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-line/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC7E,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAEtC,OAAO,EAAE,IAAI,EAAE,CAAC;AAEhB;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA+B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA4C,OAAO,KAAnD,EAAE,KAAK,GAAqC,OAAO,MAA5C,EAAE,SAAS,GAA0B,OAAO,UAAjC,EAAS,YAAY,GAAK,OAAO,MAAZ,CAAa;IAEhE,IAAM,UAAU,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAC;IAEvC,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvB,mBAAmB;IACnB,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrC,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,IAAI,EAAE;gBACJ,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;aACjB;YACD,KAAK,EAAE,YAAY;SACpB;KACF,CAAC,CAAC;IACH,IAAM,WAAW,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAEhG,IAAI,CAAC,OAAO,CAAC,CAAC;IACd,KAAK,CAAC,WAAW,CAAC,CAAC;IAEnB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AAC/E,CAAC", "sourcesContent": ["import { animation, annotation, theme, tooltip } from '../../adaptor/common';\nimport { line, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { meta } from '../tiny-area/adaptor';\nimport { X_FIELD, Y_FIELD } from './constants';\nimport { TinyLineOptions } from './types';\nimport { getTinyData } from './utils';\n\nexport { meta };\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<TinyLineOptions>): Params<TinyLineOptions> {\n  const { chart, options } = params;\n  const { data, color, lineStyle, point: pointMapping } = options;\n\n  const pointState = pointMapping?.state;\n\n  const seriesData = getTinyData(data);\n\n  chart.data(seriesData);\n\n  // line geometry 处理\n  const primary = deepAssign({}, params, {\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      line: {\n        color,\n        style: lineStyle,\n      },\n      point: pointMapping,\n    },\n  });\n  const pointParams = deepAssign({}, primary, { options: { tooltip: false, state: pointState } });\n\n  line(primary);\n  point(pointParams);\n\n  chart.axis(false);\n  chart.legend(false);\n\n  return params;\n}\n\n/**\n * 迷你折线图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<TinyLineOptions>) {\n  return flow(geometry, meta, theme, tooltip, animation, annotation())(params);\n}\n"]}