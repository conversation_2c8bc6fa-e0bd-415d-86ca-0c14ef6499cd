{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Settings\\index.vue?vue&type=style&index=0&id=126b135a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1749109381331}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zZXR0aW5nLWRyYXdlci1jb250ZW50IHsKICAuc2V0dGluZy1kcmF3ZXItdGl0bGUgewogICAgbWFyZ2luLWJvdHRvbTogMTJweDsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC44NSk7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBsaW5lLWhlaWdodDogMjJweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogIH0KCiAgLnNldHRpbmctZHJhd2VyLWJsb2NrLWNoZWNib3ggewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAuc2V0dGluZy1kcmF3ZXItYmxvY2stY2hlY2JveC1pdGVtIHsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDJweDsKICAgICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICAgaW1nIHsKICAgICAgICB3aWR0aDogNDhweDsKICAgICAgICBoZWlnaHQ6IDQ4cHg7CiAgICAgIH0KCiAgICAgIC5zZXR0aW5nLWRyYXdlci1ibG9jay1jaGVjYm94LXNlbGVjdEljb24gewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICB0b3A6IDA7CiAgICAgICAgcmlnaHQ6IDA7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgaGVpZ2h0OiAxMDAlOwogICAgICAgIHBhZGRpbmctdG9wOiAxNXB4OwogICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsKICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICBmb250LXdlaWdodDogNzAwOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgfQogICAgfQogIH0KfQoKLmRyYXdlci1jb250YWluZXIgewogIHBhZGRpbmc6IDIwcHg7CiAgZm9udC1zaXplOiAxNHB4OwogIGxpbmUtaGVpZ2h0OiAxLjU7CiAgd29yZC13cmFwOiBicmVhay13b3JkOwoKICAuZHJhd2VyLXRpdGxlIHsKICAgIG1hcmdpbi1ib3R0b206IDEycHg7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAuODUpOwogICAgZm9udC1zaXplOiAxNHB4OwogICAgbGluZS1oZWlnaHQ6IDIycHg7CiAgfQoKICAuZHJhd2VyLWl0ZW0gewogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjY1KTsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIHBhZGRpbmc6IDEycHggMDsKICB9CgogIC5kcmF3ZXItc3dpdGNoIHsKICAgIGZsb2F0OiByaWdodAogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\r\n  <el-drawer size=\"280px\" :visible=\"visible\" :with-header=\"false\" :append-to-body=\"true\" :show-close=\"false\">\r\n    <div class=\"drawer-container\">\r\n      <div>\r\n        <div class=\"setting-drawer-content\">\r\n          <div class=\"setting-drawer-title\">\r\n            <h3 class=\"drawer-title\">主题风格设置</h3>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox\">\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\r\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\r\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\r\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\r\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"drawer-item\">\r\n            <span>主题颜色</span>\r\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n          </div>\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <h3 class=\"drawer-title\">系统布局配置</h3>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 Tags-Views</span>\r\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>固定 Header</span>\r\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>显示 Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>动态标题</span>\r\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\r\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\r\n      </div>\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      theme: this.$store.state.settings.theme,\r\n      sideTheme: this.$store.state.settings.sideTheme\r\n    };\r\n  },\r\n  computed: {\r\n    visible: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    dynamicTitle: {\r\n      get() {\r\n        return this.$store.state.settings.dynamicTitle\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'dynamicTitle',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n      this.theme = val;\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'sideTheme',\r\n        value: val\r\n      })\r\n      this.sideTheme = val;\r\n    },\r\n    saveSetting() {\r\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\r\n      this.$cache.local.set(\r\n        \"layout-setting\",\r\n        `{\r\n            \"topNav\":${this.topNav},\r\n            \"tagsView\":${this.tagsView},\r\n            \"fixedHeader\":${this.fixedHeader},\r\n            \"sidebarLogo\":${this.sidebarLogo},\r\n            \"dynamicTitle\":${this.dynamicTitle},\r\n            \"sideTheme\":\"${this.sideTheme}\",\r\n            \"theme\":\"${this.theme}\"\r\n          }`\r\n      );\r\n      setTimeout(this.$modal.closeLoading(), 1000)\r\n    },\r\n    resetSetting() {\r\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\r\n      this.$cache.local.remove(\"layout-setting\")\r\n      setTimeout(\"window.location.reload()\", 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .setting-drawer-content {\r\n    .setting-drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .setting-drawer-block-checbox {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      margin-bottom: 20px;\r\n\r\n      .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .setting-drawer-block-checbox-selectIcon {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          padding-top: 15px;\r\n          padding-left: 24px;\r\n          color: #1890ff;\r\n          font-weight: 700;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .drawer-container {\r\n    padding: 20px;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    word-wrap: break-word;\r\n\r\n    .drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n\r\n    .drawer-item {\r\n      color: rgba(0, 0, 0, .65);\r\n      font-size: 14px;\r\n      padding: 12px 0;\r\n    }\r\n\r\n    .drawer-switch {\r\n      float: right\r\n    }\r\n  }\r\n</style>\r\n"]}]}