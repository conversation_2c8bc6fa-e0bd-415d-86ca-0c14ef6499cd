{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/bar/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,OAAO,IAAI,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAE7D,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAEzC;;;;GAIG;AACH,SAAS,cAAc,CAAC,MAA0B;IACxC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,MAAM,GAA2B,OAAO,OAAlC,EAAE,MAAM,GAAmB,OAAO,OAA1B,EAAE,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjD,IAAM,QAAQ,GAAG;QACf,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,KAAK;QACZ,GAAG,EAAE,MAAM;QACX,MAAM,EAAE,OAAO;KAChB,CAAC;IAEF,IAAM,YAAY,GAChB,KAAK,KAAK,KAAK;QACb,CAAC,YACG,QAAQ,EAAE,QAAQ,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,KAAI,MAAM,CAAC,IAC1C,KAAK,EAEZ,CAAC,CAAC,KAAK,CAAC;IACZ,IAAM,cAAc,GAClB,KAAK,KAAK,KAAK;QACb,CAAC,YACG,QAAQ,EAAE,QAAQ,CAAC,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,KAAI,QAAQ,CAAC,IAC5C,KAAK,EAEZ,CAAC,CAAC,KAAK,CAAC;IAEZ,6BACK,MAAM,KACT,OAAO,wBACF,OAAO,KACV,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM;YACd,kCAAkC;YAClC,KAAK,EAAE,YAAoB,EAC3B,KAAK,EAAE,cAAsB,OAE/B;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA0B;IAC/B,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAC1B,kEAAkE;IAClE,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;QAC5B,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;QACxB,+DAA+D;QAC/D,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,KAAK,CAAC,MAAM,GAAG;gBACb,EAAE,IAAI,EAAE,0BAA0B,EAAE;gBACpC,EAAE,IAAI,EAAE,uBAAuB,EAAE;gBACjC,EAAE,IAAI,EAAE,cAAc,EAAE;gBACxB,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;aACnD,CAAC;SACH;KACF;IAED,OAAO,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE,EAAE,CAAC,CAAC;AACxD,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA0B;IAChC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IAE3B,eAAe;IACP,IAAA,WAAW,GAAc,OAAO,YAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IACnC,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IACzB,IAAI,WAAW,EAAE;QACf,IAAI,MAAM,KAAK,KAAK,EAAE;YACpB,MAAM,cACJ,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,IACzC,CAAC,MAAM,IAAI,EAAE,CAAC,CAClB,CAAC;SACH;KACF;SAAM;QACL,MAAM,GAAG,KAAK,CAAC;KAChB;IAED,OAAO,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,MAAM,QAAA,EAAE,EAAE,CAAC,CAAC;AACzD,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA0B;IAC5C,0CAA0C;IAClC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IAC3B,IAAM,iBAAiB,GAAG,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;IACzG,OAAO,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;AAChF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,MAA0B;IACzC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAE1B,IAAA,QAAQ,GAA6D,OAAO,SAApE,EAAE,aAAa,GAA8C,OAAO,cAArD,EAAE,WAAW,GAAiC,OAAO,YAAxC,EAAE,WAAW,GAAoB,OAAO,YAA3B,EAAE,aAAa,GAAK,OAAO,cAAZ,CAAa;IAErF,OAAO,aAAa,CAClB;QACE,KAAK,OAAA;QACL,OAAO,wBACF,OAAO;YACV,yBAAyB;YACzB,WAAW,EAAE,QAAQ,EACrB,gBAAgB,EAAE,aAAa,EAC/B,cAAc,EAAE,WAAW,EAC3B,cAAc,EAAE,WAAW,EAC3B,gBAAgB,EAAE,aAAa,GAChC;KACF,EACD,IAAI,CACiB,CAAC;AAC1B,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA0B;IAChD,0BAA0B;IAC1B,OAAO,IAAI,CAAqB,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;AACxG,CAAC", "sourcesContent": ["import { tooltip } from '../../adaptor/common';\nimport { Params } from '../../core/adaptor';\nimport { Axis } from '../../types';\nimport { deepAssign, flow } from '../../utils';\nimport { adaptor as columnAdaptor } from '../column/adaptor';\nimport { BarOptions } from './types';\nexport { meta } from '../column/adaptor';\n\n/**\n * 处理默认配置项\n * 1. switch xField、 yField\n * 2. switch xAxis、 yAxis and adjust axis.position configuration\n */\nfunction defaultOptions(params: Params<BarOptions>): Params<BarOptions> {\n  const { options } = params;\n  const { xField, yField, xAxis, yAxis } = options;\n\n  const position = {\n    left: 'bottom',\n    right: 'top',\n    top: 'left',\n    bottom: 'right',\n  };\n\n  const verticalAxis =\n    yAxis !== false\n      ? {\n          position: position[yAxis?.position || 'left'],\n          ...yAxis,\n        }\n      : false;\n  const horizontalAxis =\n    xAxis !== false\n      ? {\n          position: position[xAxis?.position || 'bottom'],\n          ...xAxis,\n        }\n      : false;\n\n  return {\n    ...params,\n    options: {\n      ...options,\n      xField: yField,\n      yField: xField,\n      // 条形图 xAxis，yAxis 不可以做 deepAssign\n      xAxis: verticalAxis as Axis,\n      yAxis: horizontalAxis as Axis,\n    },\n  };\n}\n\n/**\n * label 适配器\n * @param params\n */\nfunction label(params: Params<BarOptions>): Params<BarOptions> {\n  const { options } = params;\n  const { label } = options;\n  // label of bar charts default position is left, if plot has label\n  if (label && !label.position) {\n    label.position = 'left';\n    // 配置默认的 label layout： 如果用户没有指定 layout 和 position， 则自动配置 layout\n    if (!label.layout) {\n      label.layout = [\n        { type: 'interval-adjust-position' },\n        { type: 'interval-hide-overlap' },\n        { type: 'adjust-color' },\n        { type: 'limit-in-plot', cfg: { action: 'hide' } },\n      ];\n    }\n  }\n\n  return deepAssign({}, params, { options: { label } });\n}\n\n/**\n * legend 适配器\n * @param params\n */\nfunction legend(params: Params<BarOptions>): Params<BarOptions> {\n  const { options } = params;\n\n  // 默认 legend 位置\n  const { seriesField, isStack } = options;\n  let { legend } = options;\n  if (seriesField) {\n    if (legend !== false) {\n      legend = {\n        position: isStack ? 'top-left' : 'right-top',\n        ...(legend || {}),\n      };\n    }\n  } else {\n    legend = false;\n  }\n\n  return deepAssign({}, params, { options: { legend } });\n}\n\n/**\n * coordinate 适配器\n * @param params\n */\nfunction coordinate(params: Params<BarOptions>): Params<BarOptions> {\n  // transpose column to bar 对角变换 & y 方向镜像变换\n  const { options } = params;\n  const coordinateOptions = [{ type: 'transpose' }, { type: 'reflectY' }].concat(options.coordinate || []);\n  return deepAssign({}, params, { options: { coordinate: coordinateOptions } });\n}\n\n/**\n * 柱形图适配器\n * @param params\n */\nexport function geometry(params: Params<BarOptions>) {\n  const { chart, options } = params;\n\n  const { barStyle, barWidthRatio, minBarWidth, maxBarWidth, barBackground } = options;\n\n  return columnAdaptor(\n    {\n      chart,\n      options: {\n        ...options,\n        // rename attrs as column\n        columnStyle: barStyle,\n        columnWidthRatio: barWidthRatio,\n        minColumnWidth: minBarWidth,\n        maxColumnWidth: maxBarWidth,\n        columnBackground: barBackground,\n      },\n    },\n    true\n  ) as Params<BarOptions>;\n}\n\n/**\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<BarOptions>): Params<BarOptions> {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow<Params<BarOptions>>(defaultOptions, label, legend, tooltip, coordinate, geometry)(params);\n}\n"]}