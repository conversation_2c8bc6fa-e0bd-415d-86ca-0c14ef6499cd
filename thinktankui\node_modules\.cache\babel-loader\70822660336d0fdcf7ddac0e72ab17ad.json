{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\tool\\gen.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\tool\\gen.js", "mtime": 1749109381298}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTable", "query", "request", "url", "method", "params", "listDbTable", "getGenTable", "tableId", "updateGenTable", "data", "importTable", "createTable", "previewTable", "delTable", "genCode", "tableName", "synchDb"], "sources": ["D:/thinktank/thinktankui/src/api/tool/gen.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询生成表数据\r\nexport function listTable(query) {\r\n  return request({\r\n    url: '/tool/gen/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n// 查询db数据库列表\r\nexport function listDbTable(query) {\r\n  return request({\r\n    url: '/tool/gen/db/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询表详细信息\r\nexport function getGenTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改代码生成信息\r\nexport function updateGenTable(data) {\r\n  return request({\r\n    url: '/tool/gen',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导入表\r\nexport function importTable(data) {\r\n  return request({\r\n    url: '/tool/gen/importTable',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 创建表\r\nexport function createTable(data) {\r\n  return request({\r\n    url: '/tool/gen/createTable',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 预览生成代码\r\nexport function previewTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/preview/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除表数据\r\nexport function delTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 生成代码（自定义路径）\r\nexport function genCode(tableName) {\r\n  return request({\r\n    url: '/tool/gen/genCode/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 同步数据库\r\nexport function synchDb(tableName) {\r\n  return request({\r\n    url: '/tool/gen/synchDb/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASK,WAAWA,CAACL,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACF,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,YAAYA,CAACL,OAAO,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGK,OAAO;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,QAAQA,CAACN,OAAO,EAAE;EAChC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,OAAOA,CAACC,SAAS,EAAE;EACjC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGa,SAAS;IACrCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,OAAOA,CAACD,SAAS,EAAE;EACjC,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGa,SAAS;IACrCZ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}