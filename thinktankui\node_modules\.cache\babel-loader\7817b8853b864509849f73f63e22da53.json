{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\directive\\permission\\hasPermi.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\directive\\permission\\hasPermi.js", "mtime": 1749109381328}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3Iuc29tZS5qcyIpOwp2YXIgX3N0b3JlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3N0b3JlIikpOwovKioNCiogdi1oYXNQZXJtaSDmk43kvZzmnYPpmZDlpITnkIYNCiogQ29weXJpZ2h0IChjKSAyMDE5IHJ1b3lpDQoqLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgaW5zZXJ0ZWQ6IGZ1bmN0aW9uIGluc2VydGVkKGVsLCBiaW5kaW5nLCB2bm9kZSkgewogICAgdmFyIHZhbHVlID0gYmluZGluZy52YWx1ZTsKICAgIHZhciBhbGxfcGVybWlzc2lvbiA9ICIqOio6KiI7CiAgICB2YXIgcGVybWlzc2lvbnMgPSBfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzICYmIF9zdG9yZS5kZWZhdWx0LmdldHRlcnMucGVybWlzc2lvbnM7CiAgICBpZiAodmFsdWUgJiYgdmFsdWUgaW5zdGFuY2VvZiBBcnJheSAmJiB2YWx1ZS5sZW5ndGggPiAwKSB7CiAgICAgIHZhciBwZXJtaXNzaW9uRmxhZyA9IHZhbHVlOwogICAgICB2YXIgaGFzUGVybWlzc2lvbnMgPSBwZXJtaXNzaW9ucy5zb21lKGZ1bmN0aW9uIChwZXJtaXNzaW9uKSB7CiAgICAgICAgcmV0dXJuIGFsbF9wZXJtaXNzaW9uID09PSBwZXJtaXNzaW9uIHx8IHBlcm1pc3Npb25GbGFnLmluY2x1ZGVzKHBlcm1pc3Npb24pOwogICAgICB9KTsKICAgICAgaWYgKCFoYXNQZXJtaXNzaW9ucykgewogICAgICAgIGVsLnBhcmVudE5vZGUgJiYgZWwucGFyZW50Tm9kZS5yZW1vdmVDaGlsZChlbCk7CiAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgIHRocm93IG5ldyBFcnJvcigiXHU4QkY3XHU4QkJFXHU3RjZFXHU2NENEXHU0RjVDXHU2NzQzXHU5NjUwXHU2ODA3XHU3QjdFXHU1MDNDIik7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_default", "exports", "default", "inserted", "el", "binding", "vnode", "value", "all_permission", "permissions", "store", "getters", "Array", "length", "permissionFlag", "hasPermissions", "some", "permission", "includes", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "Error"], "sources": ["D:/thinktank/thinktankui/src/directive/permission/hasPermi.js"], "sourcesContent": [" /**\r\n * v-hasPermi 操作权限处理\r\n * Copyright (c) 2019 ruoyi\r\n */\r\n\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  inserted(el, binding, vnode) {\r\n    const { value } = binding\r\n    const all_permission = \"*:*:*\";\r\n    const permissions = store.getters && store.getters.permissions\r\n\r\n    if (value && value instanceof Array && value.length > 0) {\r\n      const permissionFlag = value\r\n\r\n      const hasPermissions = permissions.some(permission => {\r\n        return all_permission === permission || permissionFlag.includes(permission)\r\n      })\r\n\r\n      if (!hasPermissions) {\r\n        el.parentNode && el.parentNode.removeChild(el)\r\n      }\r\n    } else {\r\n      throw new Error(`请设置操作权限标签值`)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAKA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AALC;AACD;AACA;AACA;AAHC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOc;EACbC,QAAQ,WAARA,QAAQA,CAACC,EAAE,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC3B,IAAQC,KAAK,GAAKF,OAAO,CAAjBE,KAAK;IACb,IAAMC,cAAc,GAAG,OAAO;IAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;IAE9D,IAAIF,KAAK,IAAIA,KAAK,YAAYK,KAAK,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAE;MACvD,IAAMC,cAAc,GAAGP,KAAK;MAE5B,IAAMQ,cAAc,GAAGN,WAAW,CAACO,IAAI,CAAC,UAAAC,UAAU,EAAI;QACpD,OAAOT,cAAc,KAAKS,UAAU,IAAIH,cAAc,CAACI,QAAQ,CAACD,UAAU,CAAC;MAC7E,CAAC,CAAC;MAEF,IAAI,CAACF,cAAc,EAAE;QACnBX,EAAE,CAACe,UAAU,IAAIf,EAAE,CAACe,UAAU,CAACC,WAAW,CAAChB,EAAE,CAAC;MAChD;IACF,CAAC,MAAM;MACL,MAAM,IAAIiB,KAAK,+DAAa,CAAC;IAC/B;EACF;AACF,CAAC", "ignoreList": []}]}