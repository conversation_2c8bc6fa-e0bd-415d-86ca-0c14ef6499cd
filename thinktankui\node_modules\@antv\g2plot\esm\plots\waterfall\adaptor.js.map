{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/waterfall/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC/F,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC3E,OAAO,SAAS,CAAC;AAEjB,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;;GAIG;AACH,SAAS,cAAc,CAAC,MAAgC;IAChD,IAAA,KAAoB,MAAM,CAAC,OAAO,EAAhC,MAAM,YAAA,EAAE,KAAK,WAAmB,CAAC;IAEzC,IAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAEvE,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,IAAI,gBAAgB,EAAE;QAChE,aAAa;QACb,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,gBAAgB,CAAC;KAC/C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAgC;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAEhC,IAAA,IAAI,GAYF,OAAO,KAZL,EACJ,MAAM,GAWJ,OAAO,OAXH,EACN,MAAM,GAUJ,OAAO,OAVH,EACN,KAAK,GASH,OAAO,MATJ,EACL,UAAU,GAQR,OAAO,WARC,EACV,gBAAgB,GAOd,OAAO,iBAPO,EAChB,cAAc,GAMZ,OAAO,eANK,EACd,UAAU,GAKR,OAAO,WALC,EACV,WAAW,GAIT,OAAO,YAJE,EACX,KAAK,GAGH,OAAO,MAHJ,EACL,KAAK,GAEH,OAAO,MAFJ,EACL,UAAU,GACR,OAAO,WADC,CACA;IAEZ,OAAO;IACP,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IAEvD,sBAAsB;IACtB,IAAM,YAAY,GAChB,KAAK;QACL,UAAU,KAAY;YACpB,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE;gBAC1B,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;aAC1C;YACD,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;QAC5F,CAAC,CAAC;IAEJ,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,OAAO;YACf,WAAW,EAAE,MAAM;YACnB,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;YAClD,UAAU,EAAE,gBAAgB;YAC5B,QAAQ,EAAE;gBACR,KAAK,EAAE,cAAc;gBACrB,YAAY;gBACZ,KAAK,EAAE,KAAK,IAAI,WAAW;gBAC3B,KAAK,EAAE,YAAY;aACpB;SACF;KACF,CAAC,CAAC;IACK,IAAA,GAAG,GAAK,QAAQ,CAAC,CAAC,CAAC,IAAhB,CAAiB;IAC5B,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAoB,CAAC;IAE1C,2CAA2C;IAC3C,QAAQ,CAAC,UAAU,uBACd,UAAU,KACb,UAAU,YAAA,IACV,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAgC;;IACpC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAkC,OAAO,MAAzC,EAAE,KAAK,GAA2B,OAAO,MAAlC,EAAE,MAAM,GAAmB,OAAO,OAA1B,EAAE,MAAM,GAAW,OAAO,OAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAEvD,IAAM,YAAY,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAE1E,OAAO,IAAI,CACT,KAAK;QAED,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,OAAO,IAAG,KAAK;aAElB,UAAU,CAAC,EAAE,EAAE,IAAI,YAAI,GAAC,OAAO,IAAG,YAAY,EAAE,GAAC,UAAU,IAAG,YAAY,EAAE,GAAC,cAAc,IAAG,YAAY,MAAG,CAC9G,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAgC;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC5B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1B,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC5B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAAgC;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA6C,OAAO,OAApD,EAAE,KAAK,GAAsC,OAAO,MAA7C,EAAE,UAAU,GAA0B,OAAO,WAAjC,EAAE,WAAW,GAAa,OAAO,YAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEnE,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM;QACL,IAAM,KAAK,GAAG;YACZ;gBACE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACvC,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE;aAChE;YACD;gBACE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACvC,KAAK,EAAE,UAAU;gBACjB,MAAM,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE;aACjE;SACF,CAAC;QAEF,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;gBACvB,KAAK,EAAE,OAAO;gBACd,MAAM,EAAE;oBACN,MAAM,EAAE,QAAQ;oBAChB,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;iBACrD;aACF,CAAC,CAAC;SACJ;QACD,KAAK,CAAC,MAAM,CACV,UAAU,CACR,EAAE,EACF;YACE,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,KAAK;YACf,KAAK,OAAA;SACN,EACD,MAAM,CACP,CACF,CAAC;QACF,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;KAC1C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAAgC;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAwB,OAAO,MAA/B,EAAE,SAAS,GAAa,OAAO,UAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE7C,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEjD,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,SAAS,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC;YAClF,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC;IAC9C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAqB,OAAO,QAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE5C,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,YACX,cAAc,EAAE,KAAK,EACrB,WAAW,EAAE,KAAK,EAClB,MAAM,EAAE,IAAI;YACZ,qBAAqB;YACrB,MAAM,EAAE,CAAC,MAAM,CAAC,IACb,OAAO,EACV,CAAC;QACH,8BAA8B;QAC9B,IAAM,UAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACrC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAC,CAAC,CAAC,UAAQ,CAAC,OAAO,CAAC,UAAG,MAAM,cAAI,MAAM,CAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC5G;SAAM;QACL,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC;IACtD,OAAO,IAAI,CACT,cAAc,EACd,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS,EACT,UAAU,EAAE,CACb,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { get } from '@antv/util';\nimport { animation, annotation, interaction, scale, state, theme } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { getLocale } from '../../core/locale';\nimport { Datum } from '../../types';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { ABSOLUTE_FIELD, DIFF_FIELD, IS_TOTAL, Y_FIELD } from './constant';\nimport './shape';\nimport { WaterfallOptions } from './types';\nimport { transformData } from './utils';\n\n/**\n *  处理默认配置项\n * @param params\n * @returns\n */\nfunction defaultOptions(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { locale, total } = params.options;\n\n  const localeTotalLabel = getLocale(locale).get(['waterfall', 'total']);\n\n  if (total && typeof total.label !== 'string' && localeTotalLabel) {\n    // @ts-ignore\n    params.options.total.label = localeTotalLabel;\n  }\n\n  return params;\n}\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { chart, options } = params;\n  const {\n    data,\n    xField,\n    yField,\n    total,\n    leaderLine,\n    columnWidthRatio,\n    waterfallStyle,\n    risingFill,\n    fallingFill,\n    color,\n    shape,\n    customInfo,\n  } = options;\n\n  // 数据处理\n  chart.data(transformData(data, xField, yField, total));\n\n  // 瀑布图自带的 colorMapping\n  const colorMapping =\n    color ||\n    function (datum: Datum) {\n      if (get(datum, [IS_TOTAL])) {\n        return get(total, ['style', 'fill'], '');\n      }\n      return get(datum, [Y_FIELD, 1]) - get(datum, [Y_FIELD, 0]) > 0 ? risingFill : fallingFill;\n    };\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: xField,\n      yField: Y_FIELD,\n      seriesField: xField,\n      rawFields: [yField, DIFF_FIELD, IS_TOTAL, Y_FIELD],\n      widthRatio: columnWidthRatio,\n      interval: {\n        style: waterfallStyle,\n        // 支持外部自定义形状\n        shape: shape || 'waterfall',\n        color: colorMapping,\n      },\n    },\n  });\n  const { ext } = interval(p);\n  const geometry = ext.geometry as Geometry;\n\n  // 将 waterfall leaderLineCfg 传入到自定义 shape 中\n  geometry.customInfo({\n    ...customInfo,\n    leaderLine,\n  });\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField, meta } = options;\n\n  const Y_FIELD_META = deepAssign({}, { alias: yField }, get(meta, yField));\n\n  return flow(\n    scale(\n      {\n        [xField]: xAxis,\n        [yField]: yAxis,\n        [Y_FIELD]: yAxis,\n      },\n      deepAssign({}, meta, { [Y_FIELD]: Y_FIELD_META, [DIFF_FIELD]: Y_FIELD_META, [ABSOLUTE_FIELD]: Y_FIELD_META })\n    )\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(yField, false);\n    chart.axis(Y_FIELD, false);\n  } else {\n    chart.axis(yField, yAxis);\n    chart.axis(Y_FIELD, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置 todo 添加 hover 交互\n * @param params\n */\nfunction legend(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { chart, options } = params;\n  const { legend, total, risingFill, fallingFill, locale } = options;\n\n  const i18n = getLocale(locale);\n\n  if (legend === false) {\n    chart.legend(false);\n  } else {\n    const items = [\n      {\n        name: i18n.get(['general', 'increase']),\n        value: 'increase',\n        marker: { symbol: 'square', style: { r: 5, fill: risingFill } },\n      },\n      {\n        name: i18n.get(['general', 'decrease']),\n        value: 'decrease',\n        marker: { symbol: 'square', style: { r: 5, fill: fallingFill } },\n      },\n    ];\n\n    if (total) {\n      items.push({\n        name: total.label || '',\n        value: 'total',\n        marker: {\n          symbol: 'square',\n          style: deepAssign({}, { r: 5 }, get(total, 'style')),\n        },\n      });\n    }\n    chart.legend(\n      deepAssign(\n        {},\n        {\n          custom: true,\n          position: 'top',\n          items,\n        },\n        legend\n      )\n    );\n    chart.removeInteraction('legend-filter');\n  }\n\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { chart, options } = params;\n  const { label, labelMode, xField } = options;\n\n  const geometry = findGeometry(chart, 'interval');\n\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    geometry.label({\n      fields: labelMode === 'absolute' ? [ABSOLUTE_FIELD, xField] : [DIFF_FIELD, xField],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nexport function tooltip(params: Params<WaterfallOptions>): Params<WaterfallOptions> {\n  const { chart, options } = params;\n  const { tooltip, xField, yField } = options;\n\n  if (tooltip !== false) {\n    chart.tooltip({\n      showCrosshairs: false,\n      showMarkers: false,\n      shared: true,\n      // tooltip 默认展示 y 字段值\n      fields: [yField],\n      ...tooltip,\n    });\n    // 瀑布图默认以 yField 作为 tooltip 内容\n    const geometry = chart.geometries[0];\n    tooltip?.formatter ? geometry.tooltip(`${xField}*${yField}`, tooltip.formatter) : geometry.tooltip(yField);\n  } else {\n    chart.tooltip(false);\n  }\n\n  return params;\n}\n\n/**\n * 瀑布图适配器\n * @param params\n */\nexport function adaptor(params: Params<WaterfallOptions>) {\n  return flow(\n    defaultOptions,\n    theme,\n    geometry,\n    meta,\n    axis,\n    legend,\n    tooltip,\n    label,\n    state,\n    interaction,\n    animation,\n    annotation()\n  )(params);\n}\n"]}