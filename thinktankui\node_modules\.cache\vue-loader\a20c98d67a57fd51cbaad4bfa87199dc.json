{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\index.vue", "mtime": 1749109381333}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUmlnaHRQYW5lbCBmcm9tICdAL2NvbXBvbmVudHMvUmlnaHRQYW5lbCcNCmltcG9ydCB7IEFwcE1haW4sIE5hdmJhciwgU2V0dGluZ3MsIFNpZGViYXIsIFRhZ3NWaWV3IH0gZnJvbSAnLi9jb21wb25lbnRzJw0KaW1wb3J0IFJlc2l6ZU1peGluIGZyb20gJy4vbWl4aW4vUmVzaXplSGFuZGxlcicNCmltcG9ydCB7IG1hcFN0YXRlIH0gZnJvbSAndnVleCcNCmltcG9ydCB2YXJpYWJsZXMgZnJvbSAnQC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdMYXlvdXQnLA0KICBjb21wb25lbnRzOiB7DQogICAgQXBwTWFpbiwNCiAgICBOYXZiYXIsDQogICAgUmlnaHRQYW5lbCwNCiAgICBTZXR0aW5ncywNCiAgICBTaWRlYmFyLA0KICAgIFRhZ3NWaWV3DQogIH0sDQogIG1peGluczogW1Jlc2l6ZU1peGluXSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBTdGF0ZSh7DQogICAgICB0aGVtZTogc3RhdGUgPT4gc3RhdGUuc2V0dGluZ3MudGhlbWUsDQogICAgICBzaWRlVGhlbWU6IHN0YXRlID0+IHN0YXRlLnNldHRpbmdzLnNpZGVUaGVtZSwNCiAgICAgIHNpZGViYXI6IHN0YXRlID0+IHN0YXRlLmFwcC5zaWRlYmFyLA0KICAgICAgZGV2aWNlOiBzdGF0ZSA9PiBzdGF0ZS5hcHAuZGV2aWNlLA0KICAgICAgbmVlZFRhZ3NWaWV3OiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy50YWdzVmlldywNCiAgICAgIGZpeGVkSGVhZGVyOiBzdGF0ZSA9PiBzdGF0ZS5zZXR0aW5ncy5maXhlZEhlYWRlcg0KICAgIH0pLA0KICAgIGNsYXNzT2JqKCkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaGlkZVNpZGViYXI6ICF0aGlzLnNpZGViYXIub3BlbmVkLA0KICAgICAgICBvcGVuU2lkZWJhcjogdGhpcy5zaWRlYmFyLm9wZW5lZCwNCiAgICAgICAgd2l0aG91dEFuaW1hdGlvbjogdGhpcy5zaWRlYmFyLndpdGhvdXRBbmltYXRpb24sDQogICAgICAgIG1vYmlsZTogdGhpcy5kZXZpY2UgPT09ICdtb2JpbGUnDQogICAgICB9DQogICAgfSwNCiAgICB2YXJpYWJsZXMoKSB7DQogICAgICByZXR1cm4gdmFyaWFibGVzOw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrT3V0c2lkZSgpIHsNCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdhcHAvY2xvc2VTaWRlQmFyJywgeyB3aXRob3V0QW5pbWF0aW9uOiBmYWxzZSB9KQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout", "sourcesContent": ["<template>\r\n  <div :class=\"classObj\" class=\"app-wrapper\" :style=\"{'--current-color': theme}\">\r\n    <div v-if=\"device==='mobile'&&sidebar.opened\" class=\"drawer-bg\" @click=\"handleClickOutside\"/>\r\n    <sidebar v-if=\"!sidebar.hide\" class=\"sidebar-container\"/>\r\n    <div :class=\"{hasTagsView:needTagsView,sidebarHide:sidebar.hide}\" class=\"main-container\">\r\n      <div :class=\"{'fixed-header':fixedHeader}\">\r\n        <navbar/>\r\n        <tags-view v-if=\"needTagsView\"/>\r\n      </div>\r\n      <app-main/>\r\n      <right-panel>\r\n        <settings/>\r\n      </right-panel>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport RightPanel from '@/components/RightPanel'\r\nimport { AppMain, Navbar, Settings, Sidebar, TagsView } from './components'\r\nimport ResizeMixin from './mixin/ResizeHandler'\r\nimport { mapState } from 'vuex'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'Layout',\r\n  components: {\r\n    AppMain,\r\n    Navbar,\r\n    RightPanel,\r\n    Settings,\r\n    Sidebar,\r\n    TagsView\r\n  },\r\n  mixins: [ResizeMixin],\r\n  computed: {\r\n    ...mapState({\r\n      theme: state => state.settings.theme,\r\n      sideTheme: state => state.settings.sideTheme,\r\n      sidebar: state => state.app.sidebar,\r\n      device: state => state.app.device,\r\n      needTagsView: state => state.settings.tagsView,\r\n      fixedHeader: state => state.settings.fixedHeader\r\n    }),\r\n    classObj() {\r\n      return {\r\n        hideSidebar: !this.sidebar.opened,\r\n        openSidebar: this.sidebar.opened,\r\n        withoutAnimation: this.sidebar.withoutAnimation,\r\n        mobile: this.device === 'mobile'\r\n      }\r\n    },\r\n    variables() {\r\n      return variables;\r\n    }\r\n  },\r\n  methods: {\r\n    handleClickOutside() {\r\n      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"~@/assets/styles/mixin.scss\";\r\n  @import \"~@/assets/styles/variables.scss\";\r\n\r\n  .app-wrapper {\r\n    @include clearfix;\r\n    position: relative;\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    &.mobile.openSidebar {\r\n      position: fixed;\r\n      top: 0;\r\n    }\r\n  }\r\n\r\n  .drawer-bg {\r\n    background: #000;\r\n    opacity: 0.3;\r\n    width: 100%;\r\n    top: 0;\r\n    height: 100%;\r\n    position: absolute;\r\n    z-index: 999;\r\n  }\r\n\r\n  .fixed-header {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    z-index: 9;\r\n    width: calc(100% - #{$base-sidebar-width});\r\n    transition: width 0.28s;\r\n  }\r\n\r\n  .hideSidebar .fixed-header {\r\n    width: calc(100% - 54px);\r\n  }\r\n\r\n  .sidebarHide .fixed-header {\r\n    width: 100%;\r\n  }\r\n\r\n  .mobile .fixed-header {\r\n    width: 100%;\r\n  }\r\n</style>\r\n"]}]}