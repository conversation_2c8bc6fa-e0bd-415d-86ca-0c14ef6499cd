{"version": 3, "file": "dim-rect.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/mask/multiple/dim-rect.ts"], "names": [], "mappings": ";AAAA,OAAO,gBAAgB,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC;;GAEG;AACH;IAA+B,oCAAgB;IAA/C;QAAA,qEAOC;QANW,SAAG,GAAG,GAAG,CAAC;QACV,YAAM,GAAG,IAAI,CAAC;;IAK1B,CAAC;IAJW,oCAAS,GAAnB,UAAoB,MAAM;QACxB,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAChD,OAAO,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IACH,uBAAC;AAAD,CAAC,AAPD,CAA+B,gBAAgB,GAO9C;AAED,eAAe,gBAAgB,CAAC", "sourcesContent": ["import MultipleRectMask from './rect';\nimport { getRegion } from '../dim-rect';\n\n/**\n * @ignore\n */\nclass DimRectMultiMask extends MultipleRectMask {\n  protected dim = 'x';\n  protected inPlot = true;\n  protected getRegion(points) {\n    const coord = this.context.view.getCoordinate();\n    return getRegion(points, this.dim, this.inPlot, coord);\n  }\n}\n\nexport default DimRectMultiMask;\n"]}