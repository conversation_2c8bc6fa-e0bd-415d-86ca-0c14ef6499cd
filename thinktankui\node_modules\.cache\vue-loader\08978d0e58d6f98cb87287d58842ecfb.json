{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue?vue&type=style&index=0&id=9733a8b8&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\IconsDialog.vue", "mtime": 1749109381356}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmljb24tdWwgew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmc6IDA7DQogIGZvbnQtc2l6ZTogMDsNCiAgbGkgew0KICAgIGxpc3Qtc3R5bGUtdHlwZTogbm9uZTsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICB3aWR0aDogMTYuNjYlOw0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICAgaGVpZ2h0OiAxMDhweDsNCiAgICBwYWRkaW5nOiAxNXB4IDZweCA2cHggNnB4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICY6aG92ZXIgew0KICAgICAgYmFja2dyb3VuZDogI2YyZjJmMjsNCiAgICB9DQogICAgJi5hY3RpdmUtaXRlbXsNCiAgICAgIGJhY2tncm91bmQ6ICNlMWYzZmI7DQogICAgICBjb2xvcjogIzdhNmRmMA0KICAgIH0NCiAgICA+IGkgew0KICAgICAgZm9udC1zaXplOiAzMHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDUwcHg7DQogICAgfQ0KICB9DQp9DQouaWNvbi1kaWFsb2cgew0KICA6OnYtZGVlcCAuZWwtZGlhbG9nIHsNCiAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICBtYXJnaW4tdG9wOiA0dmggIWltcG9ydGFudDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgbWF4LWhlaWdodDogOTJ2aDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICAgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgICAgIHBhZGRpbmctdG9wOiAxNHB4Ow0KICAgIH0NCiAgICAuZWwtZGlhbG9nX19ib2R5IHsNCiAgICAgIG1hcmdpbjogMCAyMHB4IDIwcHggMjBweDsNCiAgICAgIHBhZGRpbmc6IDA7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["IconsDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IconsDialog.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div class=\"icon-dialog\">\r\n    <el-dialog\r\n      v-bind=\"$attrs\"\r\n      width=\"980px\"\r\n      :modal-append-to-body=\"false\"\r\n      v-on=\"$listeners\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n    >\r\n      <div slot=\"title\">\r\n        选择图标\r\n        <el-input\r\n          v-model=\"key\"\r\n          size=\"mini\"\r\n          :style=\"{width: '260px'}\"\r\n          placeholder=\"请输入图标名称\"\r\n          prefix-icon=\"el-icon-search\"\r\n          clearable\r\n        />\r\n      </div>\r\n      <ul class=\"icon-ul\">\r\n        <li\r\n          v-for=\"icon in iconList\"\r\n          :key=\"icon\"\r\n          :class=\"active===icon?'active-item':''\"\r\n          @click=\"onSelect(icon)\"\r\n        >\r\n          <i :class=\"icon\" />\r\n          <div>{{ icon }}</div>\r\n        </li>\r\n      </ul>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport iconList from '@/utils/generator/icon.json'\r\n\r\nconst originList = iconList.map(name => `el-icon-${name}`)\r\n\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['current'],\r\n  data() {\r\n    return {\r\n      iconList: originList,\r\n      active: null,\r\n      key: ''\r\n    }\r\n  },\r\n  watch: {\r\n    key(val) {\r\n      if (val) {\r\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\r\n      } else {\r\n        this.iconList = originList\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      this.active = this.current\r\n      this.key = ''\r\n    },\r\n    onClose() {},\r\n    onSelect(icon) {\r\n      this.active = icon\r\n      this.$emit('select', icon)\r\n      this.$emit('update:visible', false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.icon-ul {\r\n  margin: 0;\r\n  padding: 0;\r\n  font-size: 0;\r\n  li {\r\n    list-style-type: none;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    display: inline-block;\r\n    width: 16.66%;\r\n    box-sizing: border-box;\r\n    height: 108px;\r\n    padding: 15px 6px 6px 6px;\r\n    cursor: pointer;\r\n    overflow: hidden;\r\n    &:hover {\r\n      background: #f2f2f2;\r\n    }\r\n    &.active-item{\r\n      background: #e1f3fb;\r\n      color: #7a6df0\r\n    }\r\n    > i {\r\n      font-size: 30px;\r\n      line-height: 50px;\r\n    }\r\n  }\r\n}\r\n.icon-dialog {\r\n  ::v-deep .el-dialog {\r\n    border-radius: 8px;\r\n    margin-bottom: 0;\r\n    margin-top: 4vh !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    max-height: 92vh;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n    .el-dialog__header {\r\n      padding-top: 14px;\r\n    }\r\n    .el-dialog__body {\r\n      margin: 0 20px 20px 20px;\r\n      padding: 0;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}