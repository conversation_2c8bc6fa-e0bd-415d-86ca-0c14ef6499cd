{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/violin/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAClD,OAAO,EAAE,UAAU,IAAI,cAAc,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACjG,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,kBAAkB,CAAC;AACpD,OAAO,EACL,YAAY,EACZ,cAAc,EACd,aAAa,EACb,eAAe,EACf,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,OAAO,GACR,MAAM,YAAY,CAAC;AAEpB,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAE9C,IAAM,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;AAE7D,IAAM,SAAS,GAAG;IAChB;QACE,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,GAAG,EAAE;KACX;CACX,CAAC;AAEF,WAAW;AACX,SAAS,IAAI,CAAC,MAA6B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAClC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC;IACzC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,YAAY;AACZ,SAAS,UAAU,CAAC,MAA6B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAA2D,OAAO,YAAlE,EAAE,KAAK,GAAoD,OAAO,MAA3D,EAAE,KAAkD,OAAO,MAAzC,EAAhB,KAAK,mBAAG,QAAQ,KAAA,EAAE,WAAW,GAAqB,OAAO,YAA5B,EAAE,OAAO,GAAY,OAAO,QAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEtF,IAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;IACtD,MAAM,CAAC;QACL,KAAK,EAAE,IAAI;QACX,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,cAAc;YACtB,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;YAChD,SAAS,EAAE,iBAAiB;YAC5B,OAAO,aACL,MAAM,EAAE,cAAc,IACnB,OAAO,CACX;YACD,MAAM,EAAE;gBACN,KAAK,EAAE,WAAW;gBAClB,KAAK,OAAA;gBACL,KAAK,OAAA;aACN;YACD,KAAK,OAAA;SACN;KACF,CAAC,CAAC;IACH,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAErC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS;AACT,SAAS,OAAO,CAAC,MAA6B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAA0B,OAAO,YAAjC,EAAE,KAAK,GAAmB,OAAO,MAA1B,EAAE,OAAO,GAAU,OAAO,QAAjB,EAAE,GAAG,GAAK,OAAO,IAAZ,CAAa;IAErD,+BAA+B;IAC/B,IAAI,GAAG,KAAK,KAAK;QAAE,OAAO,MAAM,CAAC;IAEjC,MAAM;IACN,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,eAAe,EAAE,CAAC,CAAC;IAC7D,QAAQ,CAAC;QACP,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,aAAa;YACrB,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;YAChD,OAAO,aACL,MAAM,EAAE,cAAc,IACnB,OAAO,CACX;YACD,KAAK,EAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/C,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE;oBACL,SAAS,EAAE,CAAC;iBACb;aACF;SACF;KACF,CAAC,CAAC;IACH,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAE3C,OAAO;IACP,IAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAChE,QAAQ,CAAC;QACP,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,cAAc;YACtB,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;YAChD,OAAO,aACL,MAAM,EAAE,cAAc,IACnB,OAAO,CACX;YACD,KAAK,EAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/C,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE;oBACL,WAAW,EAAE,CAAC;iBACf;aACF;SACF;KACF,CAAC,CAAC;IACH,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAE7C,MAAM;IACN,IAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC,CAAC;IAC5D,KAAK,CAAC;QACJ,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO;YAChD,OAAO,aACL,MAAM,EAAE,cAAc,IACnB,OAAO,CACX;YACD,KAAK,EAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC/C,KAAK,EAAE;gBACL,KAAK,OAAA;gBACL,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE;oBACL,IAAI,EAAE,OAAO;oBACb,SAAS,EAAE,CAAC;iBACb;aACF;SACF;KACF,CAAC,CAAC;IACH,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAE3C,eAAe;IACf,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEvB,gBAAgB;IAChB,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACzB,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACzB,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAE3B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,IAAI,CAAC,MAA6B;;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAmB,OAAO,KAA1B,EAAE,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEvC,IAAM,QAAQ,GAAG,EAAE,CAAC;IAEpB,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI;QACtC,GAAC,OAAO,wBACN,IAAI,EAAE,IAAI,IACP,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;YACrC,8EAA8E;YAC9E,gBAAgB;YAChB,IAAI,EAAE,KAAK,GACZ;QACD,GAAC,cAAc,eACb,IAAI,EAAE,IAAI,IACP,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACtC;QACD,GAAC,aAAa,eACZ,IAAI,EAAE,cAAc,IACjB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACtC;QACD,GAAC,cAAc,eACb,IAAI,EAAE,cAAc,IACjB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACtC;QACD,GAAC,YAAY,eACX,IAAI,EAAE,cAAc,IACjB,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC,CACtC;YACD,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,IAAI,CAAC,MAA6B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjC,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IAEjD,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KAClC;SAAM;QACL,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;KAClC;IAED,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAElB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,MAAM,CAAC,MAA6B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAyB,OAAO,OAAhC,EAAE,WAAW,GAAY,OAAO,YAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE/C,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM;QACL,IAAM,aAAW,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC;QACxD,iBAAiB;QACjB,IAAM,aAAa,GAAG,IAAI,CAAC,MAAa,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YACzC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,EAAE;gBACzD,GAAG,CAAC,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;aACzD;SACF;QACD,KAAK,CAAC,MAAM,CAAC,aAAW,EAAE,aAAa,CAAC,CAAC;QACzC,0BAA0B;QAC1B,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,MAAM,CAAC,aAAW,EAAE,MAAM,CAAC,EAAhC,CAAgC,CAAC,CAAC;SAC/D;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CAAC,MAA6B;IACvC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACvD,cAAc,EAAE,uBAAM,MAAM,KAAE,KAAK,EAAE,UAAU,IAAG,CAAC;IAEnD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,MAA6B;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE9B,sCAAsC;IACtC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,IAAI;QACrB,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACpC,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B;IACnD,OAAO,IAAI,CACT,KAAK,EACL,IAAI,EACJ,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACN,WAAW,EACX,UAAU,EACV,SAAS,CACV,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { each, get, omit, set } from '@antv/util';\nimport { annotation as baseAnnotation, interaction, theme, tooltip } from '../../adaptor/common';\nimport { interval, point, violin } from '../../adaptor/geometries';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findViewById, flow, pick } from '../../utils';\nimport { addViewAnimation } from '../../utils/view';\nimport {\n  MEDIAN_FIELD,\n  MEDIAN_VIEW_ID,\n  MIN_MAX_FIELD,\n  MIN_MAX_VIEW_ID,\n  QUANTILE_FIELD,\n  QUANTILE_VIEW_ID,\n  VIOLIN_SIZE_FIELD,\n  VIOLIN_VIEW_ID,\n  VIOLIN_Y_FIELD,\n  X_FIELD,\n} from './constant';\nimport { ViolinOptions } from './types';\nimport { transformViolinData } from './utils';\n\nconst TOOLTIP_FIELDS = ['low', 'high', 'q1', 'q3', 'median'];\n\nconst adjustCfg = [\n  {\n    type: 'dodge',\n    marginRatio: 1 / 32,\n  } as const,\n];\n\n/** 处理数据 */\nfunction data(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  chart.data(transformViolinData(options));\n  return params;\n}\n\n/** 小提琴轮廓 */\nfunction violinView(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { seriesField, color, shape = 'violin', violinStyle, tooltip, state } = options;\n\n  const view = chart.createView({ id: VIOLIN_VIEW_ID });\n  violin({\n    chart: view,\n    options: {\n      xField: X_FIELD,\n      yField: VIOLIN_Y_FIELD,\n      seriesField: seriesField ? seriesField : X_FIELD,\n      sizeField: VIOLIN_SIZE_FIELD,\n      tooltip: {\n        fields: TOOLTIP_FIELDS,\n        ...tooltip,\n      },\n      violin: {\n        style: violinStyle,\n        color,\n        shape,\n      },\n      state,\n    },\n  });\n  view.geometries[0].adjust(adjustCfg);\n\n  return params;\n}\n\n/** 箱线 */\nfunction boxView(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { seriesField, color, tooltip, box } = options;\n\n  // 如果配置 `box` 为 false ，不渲染内部箱线图\n  if (box === false) return params;\n\n  // 边缘线\n  const minMaxView = chart.createView({ id: MIN_MAX_VIEW_ID });\n  interval({\n    chart: minMaxView,\n    options: {\n      xField: X_FIELD,\n      yField: MIN_MAX_FIELD,\n      seriesField: seriesField ? seriesField : X_FIELD,\n      tooltip: {\n        fields: TOOLTIP_FIELDS,\n        ...tooltip,\n      },\n      state: typeof box === 'object' ? box.state : {},\n      interval: {\n        color,\n        size: 1,\n        style: {\n          lineWidth: 0,\n        },\n      },\n    },\n  });\n  minMaxView.geometries[0].adjust(adjustCfg);\n\n  // 四分点位\n  const quantileView = chart.createView({ id: QUANTILE_VIEW_ID });\n  interval({\n    chart: quantileView,\n    options: {\n      xField: X_FIELD,\n      yField: QUANTILE_FIELD,\n      seriesField: seriesField ? seriesField : X_FIELD,\n      tooltip: {\n        fields: TOOLTIP_FIELDS,\n        ...tooltip,\n      },\n      state: typeof box === 'object' ? box.state : {},\n      interval: {\n        color,\n        size: 8,\n        style: {\n          fillOpacity: 1,\n        },\n      },\n    },\n  });\n  quantileView.geometries[0].adjust(adjustCfg);\n\n  // 中位值\n  const medianView = chart.createView({ id: MEDIAN_VIEW_ID });\n  point({\n    chart: medianView,\n    options: {\n      xField: X_FIELD,\n      yField: MEDIAN_FIELD,\n      seriesField: seriesField ? seriesField : X_FIELD,\n      tooltip: {\n        fields: TOOLTIP_FIELDS,\n        ...tooltip,\n      },\n      state: typeof box === 'object' ? box.state : {},\n      point: {\n        color,\n        size: 1,\n        style: {\n          fill: 'white',\n          lineWidth: 0,\n        },\n      },\n    },\n  });\n  medianView.geometries[0].adjust(adjustCfg);\n\n  // 关闭辅助 view 的轴\n  quantileView.axis(false);\n  minMaxView.axis(false);\n  medianView.axis(false);\n\n  // 关闭辅助 view 的图例\n  medianView.legend(false);\n  minMaxView.legend(false);\n  quantileView.legend(false);\n\n  return params;\n}\n\n/**\n * meta 配置\n */\nfunction meta(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { meta, xAxis, yAxis } = options;\n\n  const baseMeta = {};\n\n  const scales = deepAssign(baseMeta, meta, {\n    [X_FIELD]: {\n      sync: true,\n      ...pick(xAxis, AXIS_META_CONFIG_KEYS),\n      // fix:  dodge is not support linear attribute, please use category attribute!\n      // 强制 x 轴类型为分类类型\n      type: 'cat',\n    },\n    [VIOLIN_Y_FIELD]: {\n      sync: true,\n      ...pick(yAxis, AXIS_META_CONFIG_KEYS),\n    },\n    [MIN_MAX_FIELD]: {\n      sync: VIOLIN_Y_FIELD,\n      ...pick(yAxis, AXIS_META_CONFIG_KEYS),\n    },\n    [QUANTILE_FIELD]: {\n      sync: VIOLIN_Y_FIELD,\n      ...pick(yAxis, AXIS_META_CONFIG_KEYS),\n    },\n    [MEDIAN_FIELD]: {\n      sync: VIOLIN_Y_FIELD,\n      ...pick(yAxis, AXIS_META_CONFIG_KEYS),\n    },\n  });\n\n  chart.scale(scales);\n\n  return params;\n}\n\n/**\n * axis 配置\n */\nfunction axis(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis } = options;\n\n  const view = findViewById(chart, VIOLIN_VIEW_ID);\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    view.axis(X_FIELD, false);\n  } else {\n    view.axis(X_FIELD, xAxis);\n  }\n\n  if (yAxis === false) {\n    view.axis(VIOLIN_Y_FIELD, false);\n  } else {\n    view.axis(VIOLIN_Y_FIELD, yAxis);\n  }\n\n  chart.axis(false);\n\n  return params;\n}\n\n/**\n *\n * @param params\n * @returns\n */\nfunction legend(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { legend, seriesField, shape } = options;\n\n  if (legend === false) {\n    chart.legend(false);\n  } else {\n    const legendField = seriesField ? seriesField : X_FIELD;\n    // fixme 暂不明为啥有描边\n    const legendOptions = omit(legend as any, ['selected']);\n    if (!shape || !shape.startsWith('hollow')) {\n      if (!get(legendOptions, ['marker', 'style', 'lineWidth'])) {\n        set(legendOptions, ['marker', 'style', 'lineWidth'], 0);\n      }\n    }\n    chart.legend(legendField, legendOptions);\n    // 特殊的处理 fixme G2 层得解决这个问题\n    if (get(legend, 'selected')) {\n      each(chart.views, (view) => view.legend(legendField, legend));\n    }\n  }\n\n  return params;\n}\n\n/**\n * annotation, apply to violin view.\n * @param params\n * @returns\n */\nfunction annotation(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart } = params;\n\n  const violinView = findViewById(chart, VIOLIN_VIEW_ID);\n  baseAnnotation()({ ...params, chart: violinView });\n\n  return params;\n}\n\n/**\n * 动画\n * @param params\n */\nexport function animation(params: Params<ViolinOptions>): Params<ViolinOptions> {\n  const { chart, options } = params;\n  const { animation } = options;\n\n  // 所有的 Geometry 都使用同一动画（各个图形如有区别，自行覆盖）\n  each(chart.views, (view) => {\n    addViewAnimation(view, animation);\n  });\n\n  return params;\n}\n\n/**\n * 小提琴图适配器\n * @param params\n */\nexport function adaptor(params: Params<ViolinOptions>) {\n  return flow(\n    theme,\n    data,\n    violinView,\n    boxView,\n    meta,\n    tooltip,\n    axis,\n    legend,\n    interaction,\n    annotation,\n    animation\n  )(params);\n}\n"]}