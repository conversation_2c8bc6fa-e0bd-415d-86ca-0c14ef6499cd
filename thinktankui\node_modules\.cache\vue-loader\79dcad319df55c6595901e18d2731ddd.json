{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\PanelGroup.vue?vue&type=style&index=0&id=0333a520&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\PanelGroup.vue", "mtime": 1749109381342}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PanelGroup.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PanelGroup.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <el-row :gutter=\"40\" class=\"panel-group\">\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('newVisitis')\">\r\n        <div class=\"card-panel-icon-wrapper icon-people\">\r\n          <svg-icon icon-class=\"peoples\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            访客\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"102400\" :duration=\"2600\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('messages')\">\r\n        <div class=\"card-panel-icon-wrapper icon-message\">\r\n          <svg-icon icon-class=\"message\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            消息\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"81212\" :duration=\"3000\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('purchases')\">\r\n        <div class=\"card-panel-icon-wrapper icon-money\">\r\n          <svg-icon icon-class=\"money\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            金额\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"9280\" :duration=\"3200\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n    <el-col :xs=\"12\" :sm=\"12\" :lg=\"6\" class=\"card-panel-col\">\r\n      <div class=\"card-panel\" @click=\"handleSetLineChartData('shoppings')\">\r\n        <div class=\"card-panel-icon-wrapper icon-shopping\">\r\n          <svg-icon icon-class=\"shopping\" class-name=\"card-panel-icon\" />\r\n        </div>\r\n        <div class=\"card-panel-description\">\r\n          <div class=\"card-panel-text\">\r\n            订单\r\n          </div>\r\n          <count-to :start-val=\"0\" :end-val=\"13600\" :duration=\"3600\" class=\"card-panel-num\" />\r\n        </div>\r\n      </div>\r\n    </el-col>\r\n  </el-row>\r\n</template>\r\n\r\n<script>\r\nimport CountTo from 'vue-count-to'\r\n\r\nexport default {\r\n  components: {\r\n    CountTo\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.$emit('handleSetLineChartData', type)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.panel-group {\r\n  margin-top: 18px;\r\n\r\n  .card-panel-col {\r\n    margin-bottom: 32px;\r\n  }\r\n\r\n  .card-panel {\r\n    height: 108px;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    color: #666;\r\n    background: #fff;\r\n    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);\r\n    border-color: rgba(0, 0, 0, .05);\r\n\r\n    &:hover {\r\n      .card-panel-icon-wrapper {\r\n        color: #fff;\r\n      }\r\n\r\n      .icon-people {\r\n        background: #40c9c6;\r\n      }\r\n\r\n      .icon-message {\r\n        background: #36a3f7;\r\n      }\r\n\r\n      .icon-money {\r\n        background: #f4516c;\r\n      }\r\n\r\n      .icon-shopping {\r\n        background: #34bfa3\r\n      }\r\n    }\r\n\r\n    .icon-people {\r\n      color: #40c9c6;\r\n    }\r\n\r\n    .icon-message {\r\n      color: #36a3f7;\r\n    }\r\n\r\n    .icon-money {\r\n      color: #f4516c;\r\n    }\r\n\r\n    .icon-shopping {\r\n      color: #34bfa3\r\n    }\r\n\r\n    .card-panel-icon-wrapper {\r\n      float: left;\r\n      margin: 14px 0 0 14px;\r\n      padding: 16px;\r\n      transition: all 0.38s ease-out;\r\n      border-radius: 6px;\r\n    }\r\n\r\n    .card-panel-icon {\r\n      float: left;\r\n      font-size: 48px;\r\n    }\r\n\r\n    .card-panel-description {\r\n      float: right;\r\n      font-weight: bold;\r\n      margin: 26px;\r\n      margin-left: 0px;\r\n\r\n      .card-panel-text {\r\n        line-height: 18px;\r\n        color: rgba(0, 0, 0, 0.45);\r\n        font-size: 16px;\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      .card-panel-num {\r\n        font-size: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width:550px) {\r\n  .card-panel-description {\r\n    display: none;\r\n  }\r\n\r\n  .card-panel-icon-wrapper {\r\n    float: none !important;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin: 0 !important;\r\n\r\n    .svg-icon {\r\n      display: block;\r\n      margin: 14px auto !important;\r\n      float: none !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}