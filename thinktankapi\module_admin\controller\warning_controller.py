from datetime import datetime
from fastapi import APIRouter, Depends, Request
from pydantic_validation_decorator import <PERSON><PERSON>te<PERSON>ields
from sqlalchemy.ext.asyncio import AsyncSession
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.warning_vo import (
    WarningRecordPageQueryModel, WarningSchemePageQueryModel, WarningRecordModel, 
    WarningSchemeModel, WarningSettingsModel, DeleteWarningRecordModel, DeleteWarningSchemeModel
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_admin.service.warning_service import WarningRecordService, WarningSchemeService, WarningSettingsService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil


warningController = APIRouter(prefix='/warning', dependencies=[Depends(LoginService.get_current_user)])


# ==================== 预警记录相关接口 ====================

@warningController.get(
    '/record/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:record:list'))]
)
async def get_warning_record_list(
    request: Request,
    record_page_query: WarningRecordPageQueryModel = Depends(WarningRecordPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警记录列表
    """
    try:
        record_page_query_result = await WarningRecordService.get_warning_record_list_services(
            query_db, record_page_query, is_page=True
        )
        logger.info('获取预警记录列表成功')
        return ResponseUtil.success(model_content=record_page_query_result)
    except Exception as e:
        logger.error(f'获取预警记录列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警记录列表失败: {str(e)}')


@warningController.post('/record', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:add'))])
@ValidateFields(validate_model='add_warning_record')
@Log(title='预警记录管理', business_type=BusinessType.INSERT)
async def add_warning_record(
    request: Request,
    add_record: WarningRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增预警记录
    """
    try:
        add_record.create_by = current_user.user.user_name
        add_record.update_by = current_user.user.user_name
        
        add_record_result = await WarningRecordService.add_warning_record_services(query_db, add_record)
        logger.info(add_record_result.message)
        return ResponseUtil.success(msg=add_record_result.message)
    except Exception as e:
        logger.error(f'新增预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增预警记录失败: {str(e)}')


@warningController.put('/record', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:edit'))])
@ValidateFields(validate_model='edit_warning_record')
@Log(title='预警记录管理', business_type=BusinessType.UPDATE)
async def edit_warning_record(
    request: Request,
    edit_record: WarningRecordModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑预警记录
    """
    try:
        edit_record.update_by = current_user.user.user_name
        
        edit_record_result = await WarningRecordService.edit_warning_record_services(query_db, edit_record)
        logger.info(edit_record_result.message)
        return ResponseUtil.success(msg=edit_record_result.message)
    except Exception as e:
        logger.error(f'编辑预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑预警记录失败: {str(e)}')


@warningController.delete('/record/{record_ids}', dependencies=[Depends(CheckUserInterfaceAuth('warning:record:remove'))])
@Log(title='预警记录管理', business_type=BusinessType.DELETE)
async def delete_warning_record(
    request: Request, 
    record_ids: str, 
    query_db: AsyncSession = Depends(get_db)
):
    """
    删除预警记录
    """
    try:
        delete_record = DeleteWarningRecordModel(record_ids=record_ids)
        delete_record_result = await WarningRecordService.delete_warning_record_services(query_db, delete_record)
        logger.info(delete_record_result.message)
        return ResponseUtil.success(msg=delete_record_result.message)
    except Exception as e:
        logger.error(f'删除预警记录失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除预警记录失败: {str(e)}')


@warningController.get(
    '/record/{record_id}', response_model=WarningRecordModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:record:query'))]
)
async def query_detail_warning_record(
    request: Request, 
    record_id: int, 
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取预警记录详细信息
    """
    try:
        record_detail_result = await WarningRecordService.warning_record_detail_services(query_db, record_id)
        if record_detail_result:
            logger.info(f'获取record_id为{record_id}的信息成功')
            return ResponseUtil.success(data=record_detail_result)
        else:
            return ResponseUtil.failure(msg='预警记录信息不存在')
    except Exception as e:
        logger.error(f'获取预警记录详细信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警记录详细信息失败: {str(e)}')


# ==================== 预警方案相关接口 ====================

@warningController.get(
    '/scheme/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:list'))]
)
async def get_warning_scheme_list(
    request: Request,
    scheme_page_query: WarningSchemePageQueryModel = Depends(WarningSchemePageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警方案列表
    """
    try:
        scheme_page_query_result = await WarningSchemeService.get_warning_scheme_list_services(
            query_db, scheme_page_query, is_page=True
        )
        logger.info('获取预警方案列表成功')
        return ResponseUtil.success(model_content=scheme_page_query_result)
    except Exception as e:
        logger.error(f'获取预警方案列表失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警方案列表失败: {str(e)}')


@warningController.post('/scheme', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:add'))])
@ValidateFields(validate_model='add_warning_scheme')
@Log(title='预警方案管理', business_type=BusinessType.INSERT)
async def add_warning_scheme(
    request: Request,
    add_scheme: WarningSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增预警方案
    """
    try:
        add_scheme.create_by = current_user.user.user_name
        add_scheme.update_by = current_user.user.user_name
        
        add_scheme_result = await WarningSchemeService.add_warning_scheme_services(query_db, add_scheme)
        logger.info(add_scheme_result.message)
        return ResponseUtil.success(msg=add_scheme_result.message, data=add_scheme_result.data)
    except Exception as e:
        logger.error(f'新增预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'新增预警方案失败: {str(e)}')


@warningController.put('/scheme', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:edit'))])
@ValidateFields(validate_model='edit_warning_scheme')
@Log(title='预警方案管理', business_type=BusinessType.UPDATE)
async def edit_warning_scheme(
    request: Request,
    edit_scheme: WarningSchemeModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑预警方案
    """
    try:
        edit_scheme.update_by = current_user.user.user_name
        
        edit_scheme_result = await WarningSchemeService.edit_warning_scheme_services(query_db, edit_scheme)
        logger.info(edit_scheme_result.message)
        return ResponseUtil.success(msg=edit_scheme_result.message)
    except Exception as e:
        logger.error(f'编辑预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'编辑预警方案失败: {str(e)}')


@warningController.delete('/scheme/{scheme_ids}', dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:remove'))])
@Log(title='预警方案管理', business_type=BusinessType.DELETE)
async def delete_warning_scheme(
    request: Request, 
    scheme_ids: str, 
    query_db: AsyncSession = Depends(get_db)
):
    """
    删除预警方案
    """
    try:
        delete_scheme = DeleteWarningSchemeModel(scheme_ids=scheme_ids)
        delete_scheme_result = await WarningSchemeService.delete_warning_scheme_services(query_db, delete_scheme)
        logger.info(delete_scheme_result.message)
        return ResponseUtil.success(msg=delete_scheme_result.message)
    except Exception as e:
        logger.error(f'删除预警方案失败: {str(e)}')
        return ResponseUtil.error(msg=f'删除预警方案失败: {str(e)}')


@warningController.get(
    '/scheme/{scheme_id}', response_model=WarningSchemeModel, dependencies=[Depends(CheckUserInterfaceAuth('warning:scheme:query'))]
)
async def query_detail_warning_scheme(
    request: Request, 
    scheme_id: int, 
    query_db: AsyncSession = Depends(get_db)
):
    """
    获取预警方案详细信息
    """
    try:
        scheme_detail_result = await WarningSchemeService.warning_scheme_detail_services(query_db, scheme_id)
        if scheme_detail_result:
            logger.info(f'获取scheme_id为{scheme_id}的信息成功')
            return ResponseUtil.success(data=scheme_detail_result)
        else:
            return ResponseUtil.failure(msg='预警方案信息不存在')
    except Exception as e:
        logger.error(f'获取预警方案详细信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警方案详细信息失败: {str(e)}')


# ==================== 预警设置相关接口 ====================

@warningController.post('/settings', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings'))])
@Log(title='预警设置', business_type=BusinessType.UPDATE)
async def save_warning_settings(
    request: Request,
    settings: WarningSettingsModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    保存预警设置
    """
    try:
        settings.create_by = current_user.user.user_name
        settings.update_by = current_user.user.user_name
        
        result = await WarningSettingsService.save_warning_settings_services(query_db, settings)
        logger.info(result.message)
        return ResponseUtil.success(msg=result.message)
    except Exception as e:
        logger.error(f'保存预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'保存预警设置失败: {str(e)}')


@warningController.get('/settings/{scheme_id}', dependencies=[Depends(CheckUserInterfaceAuth('warning:settings'))])
async def get_warning_settings(
    request: Request,
    scheme_id: int,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警设置
    """
    try:
        settings = await WarningSettingsService.get_warning_settings_services(query_db, scheme_id)
        if settings:
            logger.info(f'获取方案{scheme_id}的预警设置成功')
            return ResponseUtil.success(data=settings)
        else:
            return ResponseUtil.failure(msg='预警设置不存在')
    except Exception as e:
        logger.error(f'获取预警设置失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警设置失败: {str(e)}')


# ==================== 统计相关接口 ====================

@warningController.get('/statistics', dependencies=[Depends(CheckUserInterfaceAuth('warning:statistics'))])
async def get_warning_statistics(
    request: Request,
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取预警统计信息
    """
    try:
        statistics = await WarningRecordService.get_warning_statistics_services(query_db)
        logger.info('获取预警统计信息成功')
        return ResponseUtil.success(data=statistics)
    except Exception as e:
        logger.error(f'获取预警统计信息失败: {str(e)}')
        return ResponseUtil.error(msg=f'获取预警统计信息失败: {str(e)}')
