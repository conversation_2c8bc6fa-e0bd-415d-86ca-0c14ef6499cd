{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/line/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EACL,SAAS,EACT,UAAU,EACV,WAAW,EACX,WAAW,EACX,KAAK,EACL,SAAS,EACT,MAAM,EACN,KAAK,EACL,OAAO,GACR,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAGrD;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA2B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAuF,OAAO,KAA9F,EAAE,KAAK,GAAgF,OAAO,MAAvF,EAAE,SAAS,GAAqE,OAAO,UAA5E,EAAE,SAAS,GAA0D,OAAO,UAAjE,EAAS,YAAY,GAAqC,OAAO,MAA5C,EAAQ,WAAW,GAAkB,OAAO,KAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAC3G,IAAM,UAAU,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAC;IACvC,IAAM,SAAS,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,CAAC;IAErC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,mBAAmB;IACnB,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrC,OAAO,EAAE;YACP,UAAU,EAAE,WAAW;YACvB,IAAI,EAAE;gBACJ,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,SAAS;aACjB;YACD,sCAAsC;YACtC,gBAAgB;YAChB,KAAK,EAAE,YAAY,eACjB,KAAK,OAAA,EACL,KAAK,EAAE,QAAQ,IACZ,YAAY,CAChB;YACD,OAAO;YACP,IAAI,EAAE,WAAW,eACf,KAAK,OAAA,IACF,WAAW,CACf;YACD,mDAAmD;YACnD,KAAK,EAAE,SAAS;SACjB;KACF,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAC3F,IAAM,UAAU,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IAE9F,IAAI,CAAC,OAAO,CAAC,CAAC;IACd,KAAK,CAAC,MAAM,CAAC,CAAC;IACd,IAAI,CAAC,UAAU,CAAC,CAAC;IAEjB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA2B;;IACtC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAkC,OAAO,MAAzC,EAAE,KAAK,GAA2B,OAAO,MAAlC,EAAE,MAAM,GAAmB,OAAO,OAA1B,EAAE,MAAM,GAAW,OAAO,OAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAEvD,OAAO,IAAI,CACT,KAAK;QAED,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;;QAGf,GAAC,MAAM,IAAG;YACR,IAAI,EAAE,KAAK;SACZ;QACD,GAAC,MAAM,IAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC;YAE5C,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA2B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAC5B,IAAI,OAAO,EAAE;QACX,IAAI,CAAC,GAAG,OAAc,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACT;QACD,IAAM,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,SAAS,EAAE,CAAC,CAAC,EAAd,CAAc,CAAC,CAAC;QAE7C,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;KAC7C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA2B;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA2B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAkB,OAAO,OAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAExC,IAAI,MAAM,IAAI,WAAW,EAAE;QACzB,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;KACnC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA2B;IAChC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAM,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAEjD,8BAA8B;IAC9B,IAAI,CAAC,KAAK,EAAE;QACV,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC3B;SAAM;QACG,IAAA,MAAM,GAAuB,KAAK,OAA5B,EAAE,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAApC,sBAA4B,CAAF,CAAW;QAC3C,YAAY,CAAC,KAAK,CAAC;YACjB,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YAC1B,QAAQ,UAAA;YACR,GAAG,aACD,MAAM,EAAE;oBACN,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;iBACnD,IACE,cAAc,CAAC,GAAG,CAAC,CACvB;SACF,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA4C;IACzD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,EAAE;QACX,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,UAAC,CAAW;YACjC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA2B;IACjD,0BAA0B;IAC1B,OAAO,IAAI,CACT,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EAAE,EACZ,WAAW,CACZ,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { each, isArray } from '@antv/util';\nimport {\n  animation,\n  annotation,\n  interaction,\n  limitInPlot,\n  scale,\n  scrollbar,\n  slider,\n  theme,\n  tooltip,\n} from '../../adaptor/common';\nimport { area, line, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { adjustYMetaByZero } from '../../utils/data';\nimport { LineOptions } from './types';\n\n/**\n * geometry 配置处理\n * @param params\n */\nfunction geometry(params: Params<LineOptions>): Params<LineOptions> {\n  const { chart, options } = params;\n  const { data, color, lineStyle, lineShape, point: pointMapping, area: areaMapping, seriesField } = options;\n  const pointState = pointMapping?.state;\n  const areaState = areaMapping?.state;\n\n  chart.data(data);\n\n  // line geometry 处理\n  const primary = deepAssign({}, params, {\n    options: {\n      shapeField: seriesField,\n      line: {\n        color,\n        style: lineStyle,\n        shape: lineShape,\n      },\n      // 颜色保持一致，因为如果颜色不一致，会导致 tooltip 中元素重复。\n      // 如果存在，才设置，否则为空\n      point: pointMapping && {\n        color,\n        shape: 'circle',\n        ...pointMapping,\n      },\n      // 面积配置\n      area: areaMapping && {\n        color,\n        ...areaMapping,\n      },\n      // label 不传递给各个 geometry adaptor，由 label adaptor 处理\n      label: undefined,\n    },\n  });\n  const second = deepAssign({}, primary, { options: { tooltip: false, state: pointState } });\n  const areaParams = deepAssign({}, primary, { options: { tooltip: false, state: areaState } });\n\n  line(primary);\n  point(second);\n  area(areaParams);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<LineOptions>): Params<LineOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField, data } = options;\n\n  return flow(\n    scale(\n      {\n        [xField]: xAxis,\n        [yField]: yAxis,\n      },\n      {\n        [xField]: {\n          type: 'cat',\n        },\n        [yField]: adjustYMetaByZero(data, yField),\n      }\n    )\n  )(params);\n}\n\n/**\n * 坐标系配置. 支持 reflect 镜像处理\n * @param params\n */\nfunction coordinate(params: Params<LineOptions>): Params<LineOptions> {\n  const { chart, options } = params;\n  const { reflect } = options;\n  if (reflect) {\n    let p = reflect as any;\n    if (!isArray(p)) {\n      p = [p];\n    }\n    const actions = p.map((d) => ['reflect', d]);\n\n    chart.coordinate({ type: 'rect', actions });\n  }\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nexport function axis(params: Params<LineOptions>): Params<LineOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(yField, false);\n  } else {\n    chart.axis(yField, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<LineOptions>): Params<LineOptions> {\n  const { chart, options } = params;\n  const { legend, seriesField } = options;\n\n  if (legend && seriesField) {\n    chart.legend(seriesField, legend);\n  } else if (legend === false) {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<LineOptions>): Params<LineOptions> {\n  const { chart, options } = params;\n  const { label, yField } = options;\n\n  const lineGeometry = findGeometry(chart, 'line');\n\n  // label 为 false, 空 则不显示 label\n  if (!label) {\n    lineGeometry.label(false);\n  } else {\n    const { fields, callback, ...cfg } = label;\n    lineGeometry.label({\n      fields: fields || [yField],\n      callback,\n      cfg: {\n        layout: [\n          { type: 'limit-in-plot' },\n          { type: 'path-adjust-position' },\n          { type: 'point-adjust-position' },\n          { type: 'limit-in-plot', cfg: { action: 'hide' } },\n        ],\n        ...transformLabel(cfg),\n      },\n    });\n  }\n\n  return params;\n}\n\n/**\n * 统一处理 adjust\n * @param params\n */\nexport function adjust(params: Params<Pick<LineOptions, 'isStack'>>): Params<any> {\n  const { chart, options } = params;\n  const { isStack } = options;\n\n  if (isStack) {\n    each(chart.geometries, (g: Geometry) => {\n      g.adjust('stack');\n    });\n  }\n\n  return params;\n}\n\n/**\n * 折线图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<LineOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    geometry,\n    meta,\n    adjust,\n    theme,\n    coordinate,\n    axis,\n    legend,\n    tooltip,\n    label,\n    slider,\n    scrollbar,\n    interaction,\n    animation,\n    annotation(),\n    limitInPlot\n  )(params);\n}\n"]}