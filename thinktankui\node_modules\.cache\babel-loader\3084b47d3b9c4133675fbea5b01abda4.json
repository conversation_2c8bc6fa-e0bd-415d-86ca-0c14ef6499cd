{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\generator\\css.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\generator\\css.js", "mtime": 1749109381338}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLm1ha2VVcENzcyA9IG1ha2VVcENzczsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBzdHlsZXMgPSB7CiAgJ2VsLXJhdGUnOiAnLmVsLXJhdGV7ZGlzcGxheTogaW5saW5lLWJsb2NrOyB2ZXJ0aWNhbC1hbGlnbjogdGV4dC10b3A7fScsCiAgJ2VsLXVwbG9hZCc6ICcuZWwtdXBsb2FkX190aXB7bGluZS1oZWlnaHQ6IDEuMjt9Jwp9OwpmdW5jdGlvbiBhZGRDc3MoY3NzTGlzdCwgZWwpIHsKICB2YXIgY3NzID0gc3R5bGVzW2VsLnRhZ107CiAgY3NzICYmIGNzc0xpc3QuaW5kZXhPZihjc3MpID09PSAtMSAmJiBjc3NMaXN0LnB1c2goY3NzKTsKICBpZiAoZWwuY2hpbGRyZW4pIHsKICAgIGVsLmNoaWxkcmVuLmZvckVhY2goZnVuY3Rpb24gKGVsMikgewogICAgICByZXR1cm4gYWRkQ3NzKGNzc0xpc3QsIGVsMik7CiAgICB9KTsKICB9Cn0KZnVuY3Rpb24gbWFrZVVwQ3NzKGNvbmYpIHsKICB2YXIgY3NzTGlzdCA9IFtdOwogIGNvbmYuZmllbGRzLmZvckVhY2goZnVuY3Rpb24gKGVsKSB7CiAgICByZXR1cm4gYWRkQ3NzKGNzc0xpc3QsIGVsKTsKICB9KTsKICByZXR1cm4gY3NzTGlzdC5qb2luKCdcbicpOwp9"}, {"version": 3, "names": ["styles", "addCss", "cssList", "el", "css", "tag", "indexOf", "push", "children", "for<PERSON>ach", "el2", "makeUpCss", "conf", "fields", "join"], "sources": ["D:/thinktank/thinktankui/src/utils/generator/css.js"], "sourcesContent": ["const styles = {\r\n  'el-rate': '.el-rate{display: inline-block; vertical-align: text-top;}',\r\n  'el-upload': '.el-upload__tip{line-height: 1.2;}'\r\n}\r\n\r\nfunction addCss(cssList, el) {\r\n  const css = styles[el.tag]\r\n  css && cssList.indexOf(css) === -1 && cssList.push(css)\r\n  if (el.children) {\r\n    el.children.forEach(el2 => addCss(cssList, el2))\r\n  }\r\n}\r\n\r\nexport function makeUpCss(conf) {\r\n  const cssList = []\r\n  conf.fields.forEach(el => addCss(cssList, el))\r\n  return cssList.join('\\n')\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAMA,MAAM,GAAG;EACb,SAAS,EAAE,4DAA4D;EACvE,WAAW,EAAE;AACf,CAAC;AAED,SAASC,MAAMA,CAACC,OAAO,EAAEC,EAAE,EAAE;EAC3B,IAAMC,GAAG,GAAGJ,MAAM,CAACG,EAAE,CAACE,GAAG,CAAC;EAC1BD,GAAG,IAAIF,OAAO,CAACI,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIF,OAAO,CAACK,IAAI,CAACH,GAAG,CAAC;EACvD,IAAID,EAAE,CAACK,QAAQ,EAAE;IACfL,EAAE,CAACK,QAAQ,CAACC,OAAO,CAAC,UAAAC,GAAG;MAAA,OAAIT,MAAM,CAACC,OAAO,EAAEQ,GAAG,CAAC;IAAA,EAAC;EAClD;AACF;AAEO,SAASC,SAASA,CAACC,IAAI,EAAE;EAC9B,IAAMV,OAAO,GAAG,EAAE;EAClBU,IAAI,CAACC,MAAM,CAACJ,OAAO,CAAC,UAAAN,EAAE;IAAA,OAAIF,MAAM,CAACC,OAAO,EAAEC,EAAE,CAAC;EAAA,EAAC;EAC9C,OAAOD,OAAO,CAACY,IAAI,CAAC,IAAI,CAAC;AAC3B", "ignoreList": []}]}