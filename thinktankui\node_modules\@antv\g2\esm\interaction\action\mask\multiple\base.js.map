{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/mask/multiple/base.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,MAAM,MAAM,YAAY,CAAC;AAGhC;;;GAGG;AACH;IAAwC,oCAAM;IAA9C;QAAA,qEA+SC;QA9SC,UAAU;QACA,gBAAU,GAAG,EAAE,CAAC;QAC1B,cAAc;QACJ,cAAQ,GAAG,KAAK,CAAC;QAC3B,UAAU;QACA,YAAM,GAAG,KAAK,CAAC;QACzB,aAAa;QACH,kBAAY,GAAG,IAAI,CAAC;QACpB,kBAAY,GAAG,IAAI,CAAC;QACpB,eAAS,GAAG,MAAM,CAAC;QACnB,cAAQ,GAAG,YAAY,CAAC;;IAoSpC,CAAC;IAlSC;;OAEG;IACO,0CAAe,GAAzB;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,OAAO;YACL,CAAC,EAAE,KAAK,CAAC,CAAC;YACV,CAAC,EAAE,KAAK,CAAC,CAAC;SACX,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,oCAAS,GAAnB,UAAoB,IAAI;QACtB,IAAM,SAAS,GAAG,UAAG,IAAI,CAAC,QAAQ,cAAI,IAAI,CAAE,CAAC;QAC7C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,IAAM,MAAM,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,IAAI,EAAE,IAAI,CAAC,QAAQ;YACnB,GAAG,EAAE,UAAC,GAAW,IAAK,OAAA,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAtD,CAAsD;SAC7E,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,QAAA;YACN,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,CAAC,EAAE,KAAK,CAAC,CAAC;YACV,CAAC,EAAE,KAAK,CAAC,CAAC;SACX,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,qCAAU,GAAlB,UAAmB,KAAa;QAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC9C,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,IAAI;YACf,KAAK,aACH,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,GAAG,IACT,SAAS,CACb;SACF,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAQD;;OAEG;IACO,sCAAW,GAArB,UAAsB,MAAM;QAC1B,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,+BAAI,GAAX;QACE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,IAAI,EAAE,EAAhB,CAAgB,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;OAEG;IACI,gCAAK,GAAZ,UAAa,GAAgC;QAC3C,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,mBAAmB;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACvB,2CAA2C;QAC3C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,oCAAS,GAAhB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,+BAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAChD,OAAO;SACR;QACD,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAM,EAAE,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAC3C,IAAM,EAAE,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAE3C,gCAAgC;QAChC,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK;gBACrC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;gBACd,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;SAClC;IACH,CAAC;IAED;;;OAGG;IACO,qCAAU,GAApB,UAAqB,SAAuB;QAA5C,iBAKC;QAJC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,KAAK;YACtC,IAAM,KAAK,GAAG,OAAO,CAAC,EAAE,EAAE,KAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;YAChE,KAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,iCAAM,GAAb;QACE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACI,kCAAO,GAAd;QACE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,8BAAG,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,+BAAI,GAAX;QACE,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,IAAI,EAAE,EAAhB,CAAgB,CAAC,CAAC;YACzD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;OAEG;IACI,iCAAM,GAAb;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACd,uCAAuC;YACvC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACI,mCAAQ,GAAf;QACE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,MAAM,EAAE,EAAlB,CAAkB,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,gCAAK,GAAZ;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC1C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,MAAM,EAAE,EAAlB,CAAkB,CAAC,CAAC;YAC3D,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;SAC/B;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,kCAAO,GAAd;QACE,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,iBAAM,OAAO,WAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACO,0CAAe,GAAzB;;QACE,gCAAW,CAAC,MAAA,IAAI,CAAC,YAAY,mCAAI,EAAE,CAAC,UAAE;IACxC,CAAC;IAED;;OAEG;IACO,2CAAgB,GAA1B;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAI,CAAC,YAAY,0CAAO,YAAY,YAAE,CAAC,YAAY,CAAC,SAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACO,8CAAmB,GAA7B;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvD,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9B,IAAI,CAAC,YAAY,0CAAO,YAAY,YAAE,UAAU,SAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACO,2CAAgB,GAA1B;QACE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACO,8CAAmB,GAA7B,UAA8B,SAAkB;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,EAAnC,CAAmC,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACO,+CAAoB,GAA9B;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAC,SAAS;YACnC,IAAA,KAAuB,SAAS,CAAC,KAAK,EAApC,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,CAAC,OAAoB,CAAC;YAC7C,IAAM,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvD,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACL,CAAC;IACH,uBAAC;AAAD,CAAC,AA/SD,CAAwC,MAAM,GA+S7C;AAED,eAAe,gBAAgB,CAAC", "sourcesContent": ["import { deepMix } from '@antv/util';\nimport Action from '../../base';\nimport { LooseObject } from '../../../../interface';\n\n/**\n * @ignore\n * 辅助框 Action 的基类\n */\nabstract class MultipleMaskBase extends Action {\n  // mask 图形\n  protected maskShapes = [];\n  // 开始 mask 的标记\n  protected starting = false;\n  // 开始移动的标记\n  protected moving = false;\n  // 记录 mask 节点\n  protected recordPoints = null;\n  protected preMovePoint = null;\n  protected shapeType = 'path';\n  protected maskType = 'multi-mask';\n\n  /**\n   * 获取当前的位置\n   */\n  protected getCurrentPoint() {\n    const event = this.context.event;\n    return {\n      x: event.x,\n      y: event.y,\n    };\n  }\n\n  /**\n   * 触发 mask 的事件\n   * @param type\n   */\n  protected emitEvent(type) {\n    const eventName = `${this.maskType}:${type}`;\n    const view = this.context.view;\n    const event = this.context.event;\n    const target = {\n      type: this.shapeType,\n      name: this.maskType,\n      get: (key: string) => (target.hasOwnProperty(key) ? target[key] : undefined),\n    };\n    view.emit(eventName, {\n      target,\n      maskShapes: this.maskShapes,\n      multiPoints: this.recordPoints,\n      x: event.x,\n      y: event.y,\n    });\n  }\n\n  /**\n   * 创建 mask\n   * @param index\n   */\n  private createMask(index: number) {\n    const view = this.context.view;\n    const points = this.recordPoints[index];\n    const maskAttrs = this.getMaskAttrs(points);\n    const maskShape = view.foregroundGroup.addShape({\n      type: this.shapeType,\n      name: 'mask',\n      draggable: true,\n      attrs: {\n        fill: '#C5D4EB',\n        opacity: 0.3,\n        ...maskAttrs,\n      },\n    });\n    this.maskShapes.push(maskShape);\n  }\n\n  /**\n   * 获取 mask shape attributes\n   * @param points\n   */\n  protected abstract getMaskAttrs(points: number[]): LooseObject;\n\n  /**\n   * 生成 mask 的路径\n   */\n  protected getMaskPath(points) {\n    return [];\n  }\n\n  /**\n   * 显示\n   */\n  public show() {\n    if (this.maskShapes.length > 0) {\n      this.maskShapes.forEach((maskShape) => maskShape.show());\n      this.emitEvent('show');\n    }\n  }\n\n  /**\n   * 开始\n   */\n  public start(arg?: { maskStyle: LooseObject }) {\n    this.recordPointStart();\n\n    this.starting = true;\n    // 开始时，保证移动结束\n    this.moving = false;\n    // 开始第 index 个 mask\n    const index = this.recordPoints.length - 1;\n    this.createMask(index);\n    // 开始时设置 capture: false，可以避免创建、resize 时触发事件\n    this.updateShapesCapture(false);\n    this.updateMask(arg?.maskStyle);\n    this.emitEvent('start');\n  }\n\n  /**\n   * 开始移动\n   */\n  public moveStart() {\n    this.moving = true;\n    this.preMovePoint = this.getCurrentPoint();\n    this.updateShapesCapture(false);\n  }\n\n  /**\n   * 移动 mask\n   */\n  public move() {\n    if (!this.moving || this.maskShapes.length === 0) {\n      return;\n    }\n    const currentPoint = this.getCurrentPoint();\n    const preMovePoint = this.preMovePoint;\n    const dx = currentPoint.x - preMovePoint.x;\n    const dy = currentPoint.y - preMovePoint.y;\n\n    // 只移动当前 event (x, y) 所在的某个 mask\n    const index = this.getCurMaskShapeIndex();\n    if (index > -1) {\n      this.recordPoints[index].forEach((point) => {\n        point.x += dx;\n        point.y += dy;\n      });\n      this.updateMask();\n      this.emitEvent('change');\n      this.preMovePoint = currentPoint;\n    }\n  }\n\n  /**\n   * 更新\n   * @param maskStyle\n   */\n  protected updateMask(maskStyle?: LooseObject) {\n    this.recordPoints.forEach((points, index) => {\n      const attrs = deepMix({}, this.getMaskAttrs(points), maskStyle);\n      this.maskShapes[index].attr(attrs);\n    });\n  }\n\n  /**\n   * 大小变化\n   */\n  public resize() {\n    if (this.starting && this.maskShapes.length > 0) {\n      this.recordPointContinue();\n\n      this.updateMask();\n      this.emitEvent('change');\n    }\n  }\n\n  /**\n   * 结束移动\n   */\n  public moveEnd() {\n    this.moving = false;\n    this.preMovePoint = null;\n    this.updateShapesCapture(true);\n  }\n\n  /**\n   * 结束\n   */\n  public end() {\n    this.starting = false;\n    this.emitEvent('end');\n    this.updateShapesCapture(true);\n  }\n\n  /**\n   * 隐藏\n   */\n  public hide() {\n    if (this.maskShapes.length > 0) {\n      this.maskShapes.forEach((maskShape) => maskShape.hide());\n      this.emitEvent('hide');\n    }\n  }\n\n  /**\n   * 清除某个 mask\n   */\n  public remove() {\n    const index = this.getCurMaskShapeIndex();\n    if (index > -1) {\n      // event (x, y) 在的某个 mask 区域内时，清除该 mask\n      this.recordPoints.splice(index, 1);\n      this.maskShapes[index].remove();\n      this.maskShapes.splice(index, 1);\n      this.preMovePoint = null;\n      this.updateShapesCapture(true);\n      this.emitEvent('change');\n    }\n  }\n\n  /**\n   * 清除全部 mask\n   */\n  public clearAll() {\n    this.recordPointClear();\n    this.maskShapes.forEach((maskShape) => maskShape.remove());\n    this.maskShapes = [];\n    this.preMovePoint = null;\n  }\n\n  /**\n   * 清除\n   */\n  public clear() {\n    const index = this.getCurMaskShapeIndex();\n    if (index === -1) {\n      this.recordPointClear();\n      this.maskShapes.forEach((maskShape) => maskShape.remove());\n      this.maskShapes = [];\n      this.emitEvent('clearAll');\n    } else {\n      this.recordPoints.splice(index, 1);\n      this.maskShapes[index].remove();\n      this.maskShapes.splice(index, 1);\n      this.preMovePoint = null;\n      this.emitEvent('clearSingle');\n    }\n    this.preMovePoint = null;\n  }\n\n  /**\n   * 销毁\n   */\n  public destroy() {\n    this.clear();\n    super.destroy();\n  }\n\n  /**\n   * 获取 mask 节点记录\n   */\n  protected getRecordPoints() {\n    return [...(this.recordPoints ?? [])];\n  }\n\n  /**\n   * 创建 mask 节点记录\n   */\n  protected recordPointStart() {\n    const recordPoints = this.getRecordPoints();\n    const currentPoint = this.getCurrentPoint();\n    this.recordPoints = [...recordPoints, [currentPoint]];\n  }\n\n  /**\n   * 持续记录 mask 节点\n   */\n  protected recordPointContinue() {\n    const recordPoints = this.getRecordPoints();\n    const currentPoint = this.getCurrentPoint();\n    const lastPoints = recordPoints.splice(-1, 1)[0] || [];\n    lastPoints.push(currentPoint);\n    this.recordPoints = [...recordPoints, lastPoints];\n  }\n\n  /**\n   * 清除 mask 节点 记录\n   */\n  protected recordPointClear() {\n    this.recordPoints = [];\n  }\n\n  /**\n   * 设置 capture\n   * false: 避免创建、resize 时触发事件\n   * true: 正常触发其它事件\n   * @param isCapture\n   */\n  protected updateShapesCapture(isCapture: boolean) {\n    this.maskShapes.forEach((maskShape) => maskShape.set('capture', isCapture));\n  }\n\n  /**\n   *\n   * @returns 获取当前 event (x, y) 所在 maskShape 的 index\n   */\n  protected getCurMaskShapeIndex() {\n    const currentPoint = this.getCurrentPoint();\n    return this.maskShapes.findIndex((maskShape) => {\n      const { width, height, r } = maskShape.attrs;\n      const isEmpty = width === 0 || height === 0 || r === 0;\n      return !isEmpty && maskShape.isHit(currentPoint.x, currentPoint.y);\n    });\n  }\n}\n\nexport default MultipleMaskBase;\n"]}