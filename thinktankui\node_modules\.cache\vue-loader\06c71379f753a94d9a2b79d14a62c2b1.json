{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\swagger\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\swagger\\index.vue", "mtime": 1749109381358}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KDQppbXBvcnQgaUZyYW1lIGZyb20gIkAvY29tcG9uZW50cy9pRnJhbWUvaW5kZXgiOw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiU3dhZ2dlciIsDQogIGNvbXBvbmVudHM6IHsgaUZyYW1lIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZG9jcyINCiAgICB9Ow0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/swagger", "sourcesContent": ["<template>\r\n  <i-frame :src=\"url\" />\r\n</template>\r\n<script>\r\nimport iFrame from \"@/components/iFrame/index\";\r\nexport default {\r\n  name: \"Swagger\",\r\n  components: { iFrame },\r\n  data() {\r\n    return {\r\n      url: process.env.VUE_APP_BASE_API + \"/docs\"\r\n    };\r\n  },\r\n};\r\n</script>\r\n"]}]}