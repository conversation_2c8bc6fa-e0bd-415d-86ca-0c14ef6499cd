{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/liquid/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AACrF,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAEhE,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAuE,OAAO,QAA9E,EAAE,WAAW,GAA0D,OAAO,YAAjE,EAAE,MAAM,GAAkD,OAAO,OAAzD,EAAE,OAAO,GAAyC,OAAO,QAAhD,EAAE,IAAI,GAAmC,OAAO,KAA1C,EAAE,KAAK,GAA4B,OAAO,MAAnC,EAAE,UAAU,GAAgB,OAAO,WAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE9F,KAAK,CAAC,KAAK,CAAC;QACV,OAAO,EAAE;YACP,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,CAAC;SACP;KACF,CAAC,CAAC;IAEH,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IAEnC,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;IAE7D,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,SAAS;YACjB,gCAAgC;YAChC,yBAAyB;YACzB,UAAU,EAAE,MAAM;YAClB,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,mBAAmB;aAC3B;SACF;KACF,CAAC,CAAC;IACK,IAAA,GAAG,GAAK,QAAQ,CAAC,CAAC,CAAC,IAAhB,CAAiB;IAC5B,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAoB,CAAC;IAClC,IAAA,UAAU,GAAK,KAAK,CAAC,QAAQ,EAAE,WAArB,CAAsB;IACxC,IAAM,UAAU,GAAe;QAC7B,OAAO,SAAA;QACP,MAAM,QAAA;QACN,OAAO,SAAA;QACP,IAAI,MAAA;QACJ,KAAK,OAAA;QACL,UAAU,YAAA;QACV,UAAU,YAAA;QACV,SAAS,WAAA;KACV,CAAC;IAEF,0BAA0B;IAC1B,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAEhC,OAAO;IACP,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAErB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,MAA6B,EAAE,OAAiB;IAChE,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAoB,OAAO,UAA3B,EAAE,OAAO,GAAW,OAAO,QAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAE7C,cAAc;IACd,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9C,IAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,UAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG,EAA1B,CAA0B,CAAC,CAAC;IACjG,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC;IACnC,IAAI,UAAU,EAAE;QACd,UAAU,GAAG,UAAU,CAAC,EAAE,EAAE,UAAU,EAAE;YACtC,OAAO,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;SAClF,CAAC,CAAC;KACJ;IAED,eAAe,CAAC,KAAK,EAAE,EAAE,SAAS,wBAAO,SAAS,KAAE,OAAO,EAAE,UAAU,GAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;IAE9G,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B;IACnD,0CAA0C;IAC1C,OAAO,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AAC7G,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { get, isNil } from '@antv/util';\nimport { animation, interaction, pattern, scale, theme } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow, renderStatistic } from '../../utils';\nimport { CustomInfo, LiquidOptions } from './types';\nimport { getLiquidData } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<LiquidOptions>): Params<LiquidOptions> {\n  const { chart, options } = params;\n  const { percent, liquidStyle, radius, outline, wave, shape, shapeStyle, animation } = options;\n\n  chart.scale({\n    percent: {\n      min: 0,\n      max: 1,\n    },\n  });\n\n  chart.data(getLiquidData(percent));\n\n  const color = options.color || chart.getTheme().defaultColor;\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: 'type',\n      yField: 'percent',\n      // radius 放到 columnWidthRatio 中。\n      // 保证横向的大小是根据  radius 生成的\n      widthRatio: radius,\n      interval: {\n        color,\n        style: liquidStyle,\n        shape: 'liquid-fill-gauge',\n      },\n    },\n  });\n  const { ext } = interval(p);\n  const geometry = ext.geometry as Geometry;\n  const { background } = chart.getTheme();\n  const customInfo: CustomInfo = {\n    percent,\n    radius,\n    outline,\n    wave,\n    shape,\n    shapeStyle,\n    background,\n    animation,\n  };\n\n  // 将 radius 传入到自定义 shape 中\n  geometry.customInfo(customInfo);\n\n  // 关闭组件\n  chart.legend(false);\n  chart.axis(false);\n  chart.tooltip(false);\n\n  return params;\n}\n\n/**\n * 统计指标文档\n * @param params\n */\nexport function statistic(params: Params<LiquidOptions>, updated?: boolean): Params<LiquidOptions> {\n  const { chart, options } = params;\n  const { statistic, percent, meta } = options;\n\n  // 先清空标注，再重新渲染\n  chart.getController('annotation').clear(true);\n\n  const metaFormatter = get(meta, ['percent', 'formatter']) || ((v) => `${(v * 100).toFixed(2)}%`);\n  let contentOpt = statistic.content;\n  if (contentOpt) {\n    contentOpt = deepAssign({}, contentOpt, {\n      content: !isNil(contentOpt.content) ? contentOpt.content : metaFormatter(percent),\n    });\n  }\n\n  renderStatistic(chart, { statistic: { ...statistic, content: contentOpt }, plotType: 'liquid' }, { percent });\n\n  if (updated) {\n    chart.render(true);\n  }\n\n  return params;\n}\n\n/**\n * 水波图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<LiquidOptions>) {\n  // flow 的方式处理所有的配置到 G2 API (主题前置，会影响绘制的取色)\n  return flow(theme, pattern('liquidStyle'), geometry, statistic, scale({}), animation, interaction)(params);\n}\n"]}