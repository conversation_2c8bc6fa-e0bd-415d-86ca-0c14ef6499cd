{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1749109381331}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "components", "ThemePicker", "data", "theme", "$store", "state", "settings", "sideTheme", "computed", "visible", "get", "showSettings", "fixedHeader", "set", "val", "dispatch", "key", "value", "topNav", "commit", "permission", "defaultRoutes", "tagsView", "sidebarLogo", "dynamicTitle", "methods", "themeChange", "handleTheme", "saveSetting", "$modal", "loading", "$cache", "local", "concat", "setTimeout", "closeLoading", "resetSetting", "remove"], "sources": ["src/layout/components/Settings/index.vue"], "sourcesContent": ["<template>\r\n  <el-drawer size=\"280px\" :visible=\"visible\" :with-header=\"false\" :append-to-body=\"true\" :show-close=\"false\">\r\n    <div class=\"drawer-container\">\r\n      <div>\r\n        <div class=\"setting-drawer-content\">\r\n          <div class=\"setting-drawer-title\">\r\n            <h3 class=\"drawer-title\">主题风格设置</h3>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox\">\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\r\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\r\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\r\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\r\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"drawer-item\">\r\n            <span>主题颜色</span>\r\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\" />\r\n          </div>\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <h3 class=\"drawer-title\">系统布局配置</h3>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 Tags-Views</span>\r\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>固定 Header</span>\r\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>显示 Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>动态标题</span>\r\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\r\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\r\n      </div>\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      theme: this.$store.state.settings.theme,\r\n      sideTheme: this.$store.state.settings.sideTheme\r\n    };\r\n  },\r\n  computed: {\r\n    visible: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    dynamicTitle: {\r\n      get() {\r\n        return this.$store.state.settings.dynamicTitle\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'dynamicTitle',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n      this.theme = val;\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'sideTheme',\r\n        value: val\r\n      })\r\n      this.sideTheme = val;\r\n    },\r\n    saveSetting() {\r\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\r\n      this.$cache.local.set(\r\n        \"layout-setting\",\r\n        `{\r\n            \"topNav\":${this.topNav},\r\n            \"tagsView\":${this.tagsView},\r\n            \"fixedHeader\":${this.fixedHeader},\r\n            \"sidebarLogo\":${this.sidebarLogo},\r\n            \"dynamicTitle\":${this.dynamicTitle},\r\n            \"sideTheme\":\"${this.sideTheme}\",\r\n            \"theme\":\"${this.theme}\"\r\n          }`\r\n      );\r\n      setTimeout(this.$modal.closeLoading(), 1000)\r\n    },\r\n    resetSetting() {\r\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\r\n      this.$cache.local.remove(\"layout-setting\")\r\n      setTimeout(\"window.location.reload()\", 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .setting-drawer-content {\r\n    .setting-drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .setting-drawer-block-checbox {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      margin-bottom: 20px;\r\n\r\n      .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .setting-drawer-block-checbox-selectIcon {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          padding-top: 15px;\r\n          padding-left: 24px;\r\n          color: #1890ff;\r\n          font-weight: 700;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .drawer-container {\r\n    padding: 20px;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    word-wrap: break-word;\r\n\r\n    .drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n\r\n    .drawer-item {\r\n      color: rgba(0, 0, 0, .65);\r\n      font-size: 14px;\r\n      padding: 12px 0;\r\n    }\r\n\r\n    .drawer-switch {\r\n      float: right\r\n    }\r\n  }\r\n</style>\r\n"], "mappings": ";;;;;;;;AA4EA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;MACAI,SAAA,OAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAK,YAAA;MACA;IACA;IACAC,WAAA;MACAF,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAM,WAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAV,MAAA,CAAAW,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAI,MAAA;MACAR,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAY,MAAA;MACA;MACAL,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAV,MAAA,CAAAW,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;QACA,KAAAA,GAAA;UACA,KAAAV,MAAA,CAAAW,QAAA;UACA,KAAAX,MAAA,CAAAe,MAAA,6BAAAf,MAAA,CAAAC,KAAA,CAAAe,UAAA,CAAAC,aAAA;QACA;MACA;IACA;IACAC,QAAA;MACAZ,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAgB,QAAA;MACA;MACAT,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAV,MAAA,CAAAW,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAS,WAAA;MACAb,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAiB,WAAA;MACA;MACAV,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAV,MAAA,CAAAW,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;IACAU,YAAA;MACAd,GAAA,WAAAA,IAAA;QACA,YAAAN,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAkB,YAAA;MACA;MACAX,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAV,MAAA,CAAAW,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAW,OAAA;IACAC,WAAA,WAAAA,YAAAZ,GAAA;MACA,KAAAV,MAAA,CAAAW,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;MACA,KAAAX,KAAA,GAAAW,GAAA;IACA;IACAa,WAAA,WAAAA,YAAAb,GAAA;MACA,KAAAV,MAAA,CAAAW,QAAA;QACAC,GAAA;QACAC,KAAA,EAAAH;MACA;MACA,KAAAP,SAAA,GAAAO,GAAA;IACA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAAC,MAAA,CAAAC,OAAA;MACA,KAAAC,MAAA,CAAAC,KAAA,CAAAnB,GAAA,CACA,+CAAAoB,MAAA,CAEA,KAAAf,MAAA,kCAAAe,MAAA,CACA,KAAAX,QAAA,qCAAAW,MAAA,CACA,KAAArB,WAAA,qCAAAqB,MAAA,CACA,KAAAV,WAAA,sCAAAU,MAAA,CACA,KAAAT,YAAA,qCAAAS,MAAA,CACA,KAAA1B,SAAA,mCAAA0B,MAAA,CACA,KAAA9B,KAAA,oBAEA;MACA+B,UAAA,MAAAL,MAAA,CAAAM,YAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAP,MAAA,CAAAC,OAAA;MACA,KAAAC,MAAA,CAAAC,KAAA,CAAAK,MAAA;MACAH,UAAA;IACA;EACA;AACA", "ignoreList": []}]}