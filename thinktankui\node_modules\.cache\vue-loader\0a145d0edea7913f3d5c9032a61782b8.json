{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\App.vue", "mtime": 1749109381292}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVGhlbWVQaWNrZXIgZnJvbSAiQC9jb21wb25lbnRzL1RoZW1lUGlja2VyIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQXBwIiwNCiAgY29tcG9uZW50czogeyBUaGVtZVBpY2tlciB9LA0KICBtZXRhSW5mbygpIHsNCiAgICByZXR1cm4gew0KICAgICAgdGl0bGU6IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLmR5bmFtaWNUaXRsZSAmJiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aXRsZSwNCiAgICAgIHRpdGxlVGVtcGxhdGU6IHRpdGxlID0+IHsNCiAgICAgICAgcmV0dXJuIHRpdGxlID8gYCR7dGl0bGV9IC0gJHtwcm9jZXNzLmVudi5WVUVfQVBQX1RJVExFfWAgOiBwcm9jZXNzLmVudi5WVUVfQVBQX1RJVExFDQogICAgICB9DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;AAQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n    <theme-picker />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from \"@/components/ThemePicker\";\r\n\r\nexport default {\r\n  name: \"App\",\r\n  components: { ThemePicker },\r\n  metaInfo() {\r\n    return {\r\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\r\n      titleTemplate: title => {\r\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n#app .theme-picker {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}