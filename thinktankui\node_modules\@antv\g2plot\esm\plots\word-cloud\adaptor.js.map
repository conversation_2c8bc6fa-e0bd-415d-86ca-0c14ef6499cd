{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/word-cloud/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC5F,OAAO,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAEjD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAC;AAEpD,OAAO,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAEpC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAgC;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,UAAU,GAAY,OAAO,WAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IACtC,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,UAAU,IAAI,sBAAsB;YACjD,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,oCAAQ,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC,UAAE,OAAO,SAAC;YAC3E,KAAK,EAAE;gBACL,KAAK,OAAA;gBACL,KAAK,EAAE,YAAY;aACpB;SACF;KACF,CAAC,CAAC;IAEK,IAAA,GAAG,GAAK,KAAK,CAAC,CAAC,CAAC,IAAb,CAAc;IACzB,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAE1B,KAAK,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAChC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAElB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAgC;IAC5C,OAAO,IAAI,CACT,KAAK,CAAC;QACJ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;QAClB,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;KACnB,CAAC,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAAgC;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAiB,OAAO,OAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAEvC,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM,IAAI,UAAU,EAAE;QACrB,KAAK,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;KAC9C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC;IACtD,0BAA0B;IAC1B,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACtF,CAAC", "sourcesContent": ["import { get, isFunction } from '@antv/util';\nimport { animation, interaction, scale, state, theme, tooltip } from '../../adaptor/common';\nimport { point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { WORD_CLOUD_COLOR_FIELD } from './constant';\nimport { WordCloudOptions } from './types';\nimport { transform } from './utils';\n\n/**\n * geometry 配置处理\n * @param params\n */\nfunction geometry(params: Params<WordCloudOptions>): Params<WordCloudOptions> {\n  const { chart, options } = params;\n  const { colorField, color } = options;\n  const data = transform(params);\n\n  chart.data(data);\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: 'x',\n      yField: 'y',\n      seriesField: colorField && WORD_CLOUD_COLOR_FIELD,\n      rawFields: isFunction(color) && [...get(options, 'rawFields', []), 'datum'],\n      point: {\n        color,\n        shape: 'word-cloud',\n      },\n    },\n  });\n\n  const { ext } = point(p);\n  ext.geometry.label(false);\n\n  chart.coordinate().reflect('y');\n  chart.axis(false);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<WordCloudOptions>): Params<WordCloudOptions> {\n  return flow(\n    scale({\n      x: { nice: false },\n      y: { nice: false },\n    })\n  )(params);\n}\n\n/**\n * 词云图 legend 配置\n * @param params\n */\nexport function legend(params: Params<WordCloudOptions>): Params<WordCloudOptions> {\n  const { chart, options } = params;\n  const { legend, colorField } = options;\n\n  if (legend === false) {\n    chart.legend(false);\n  } else if (colorField) {\n    chart.legend(WORD_CLOUD_COLOR_FIELD, legend);\n  }\n\n  return params;\n}\n\n/**\n * 词云图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<WordCloudOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  flow(geometry, meta, tooltip, legend, interaction, animation, theme, state)(params);\n}\n"]}