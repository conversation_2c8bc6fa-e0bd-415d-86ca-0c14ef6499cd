{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\index.vue", "mtime": 1749109381357}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuedraggable", "_interopRequireDefault", "require", "_jsBeautify", "_clipboard", "_render", "_RightPanel", "_config", "_index", "_html", "_js", "_css", "_drawingDefault", "_logo", "_CodeTypeDialog", "_DraggableItem", "oldActiveId", "tempActiveData", "_default", "exports", "default", "components", "draggable", "render", "RightPanel", "CodeTypeDialog", "DraggableItem", "data", "logo", "idGlobal", "formConf", "inputComponents", "selectComponents", "layoutComponents", "labelWidth", "drawingList", "<PERSON><PERSON><PERSON><PERSON>", "drawingData", "activeId", "formId", "drawerVisible", "formData", "dialogVisible", "generateConf", "showFileName", "activeData", "created", "document", "body", "ondrop", "event", "preventDefault", "stopPropagation", "watch", "activeDataLabel", "val", "oldVal", "placeholder", "undefined", "tag", "replace", "handler", "immediate", "mounted", "_this", "clipboard", "ClipboardJS", "text", "trigger", "codeStr", "generateCode", "$notify", "title", "message", "type", "on", "e", "$message", "error", "methods", "activeFormItem", "element", "onEnd", "obj", "a", "from", "to", "addComponent", "item", "clone", "cloneComponent", "push", "origin", "JSON", "parse", "stringify", "span", "<PERSON><PERSON><PERSON>", "Date", "layout", "vModel", "concat", "label", "componentName", "gutter", "AssembleFormData", "_objectSpread2", "fields", "generate", "func", "titleCase", "operationType", "execRun", "execDownload", "blob", "Blob", "$download", "saveAs", "fileName", "execCopy", "getElementById", "click", "empty", "_this2", "$confirm", "then", "drawingItemCopy", "parent", "createIdAndKey", "_this3", "Array", "isArray", "children", "map", "childItem", "drawingItemDelete", "index", "_this4", "splice", "$nextTick", "len", "length", "script", "vueScript", "makeUpJs", "html", "vueTemplate", "makeUpHtml", "css", "cssStyle", "makeUpCss", "beautifier", "beautifierConf", "download", "run", "copy", "tagChange", "newTag", "_this5", "tagIcon", "Object", "keys", "for<PERSON>ach", "key", "_typeof2", "updateDrawingList", "list", "_this6", "findIndex"], "sources": ["src/views/tool/build/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div class=\"left-board\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo\">\r\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\r\n        </div>\r\n      </div>\r\n      <el-scrollbar class=\"left-scrollbar\">\r\n        <div class=\"components-list\">\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />输入型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"inputComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" />选择型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\"\r\n            :list=\"selectComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :clone=\"cloneComponent\"\r\n            draggable=\".components-item\"\r\n            :sort=\"false\"\r\n            @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in selectComponents\"\r\n              :key=\"index\"\r\n              class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\" /> 布局型组件\r\n          </div>\r\n          <draggable\r\n            class=\"components-draggable\" :list=\"layoutComponents\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\" :clone=\"cloneComponent\"\r\n            draggable=\".components-item\" :sort=\"false\" @end=\"onEnd\"\r\n          >\r\n            <div\r\n              v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\"\r\n            >\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\" />\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <div class=\"center-board\">\r\n      <div class=\"action-bar\">\r\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\r\n          导出vue文件\r\n        </el-button>\r\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\r\n          复制代码\r\n        </el-button>\r\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\r\n          清空\r\n        </el-button>\r\n      </div>\r\n      <el-scrollbar class=\"center-scrollbar\">\r\n        <el-row class=\"center-board-row\" :gutter=\"formConf.gutter\">\r\n          <el-form\r\n            :size=\"formConf.size\"\r\n            :label-position=\"formConf.labelPosition\"\r\n            :disabled=\"formConf.disabled\"\r\n            :label-width=\"formConf.labelWidth + 'px'\"\r\n          >\r\n            <draggable class=\"drawing-board\" :list=\"drawingList\" :animation=\"340\" group=\"componentsGroup\">\r\n              <draggable-item\r\n                v-for=\"(element, index) in drawingList\"\r\n                :key=\"element.renderKey\"\r\n                :drawing-list=\"drawingList\"\r\n                :element=\"element\"\r\n                :index=\"index\"\r\n                :active-id=\"activeId\"\r\n                :form-conf=\"formConf\"\r\n                @activeItem=\"activeFormItem\"\r\n                @copyItem=\"drawingItemCopy\"\r\n                @deleteItem=\"drawingItemDelete\"\r\n              />\r\n            </draggable>\r\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\r\n              从左侧拖入或点选组件进行表单设计\r\n            </div>\r\n          </el-form>\r\n        </el-row>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <right-panel\r\n      :active-data=\"activeData\"\r\n      :form-conf=\"formConf\"\r\n      :show-field=\"!!drawingList.length\"\r\n      @tag-change=\"tagChange\"\r\n    />\r\n\r\n    <code-type-dialog\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"选择生成类型\"\r\n      :show-file-name=\"showFileName\"\r\n      @confirm=\"generate\"\r\n    />\r\n    <input id=\"copyNode\" type=\"hidden\">\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport draggable from 'vuedraggable'\r\nimport beautifier from 'js-beautify'\r\nimport ClipboardJS from 'clipboard'\r\nimport render from '@/utils/generator/render'\r\nimport RightPanel from './RightPanel'\r\nimport { inputComponents, selectComponents, layoutComponents, formConf } from '@/utils/generator/config'\r\nimport { beautifierConf, titleCase } from '@/utils/index'\r\nimport { makeUpHtml, vueTemplate, vueScript, cssStyle } from '@/utils/generator/html'\r\nimport { makeUpJs } from '@/utils/generator/js'\r\nimport { makeUpCss } from '@/utils/generator/css'\r\nimport drawingDefault from '@/utils/generator/drawingDefault'\r\nimport logo from '@/assets/logo/logo.png'\r\nimport CodeTypeDialog from './CodeTypeDialog'\r\nimport DraggableItem from './DraggableItem'\r\n\r\nlet oldActiveId\r\nlet tempActiveData\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    render,\r\n    RightPanel,\r\n    CodeTypeDialog,\r\n    DraggableItem\r\n  },\r\n  data() {\r\n    return {\r\n      logo,\r\n      idGlobal: 100,\r\n      formConf,\r\n      inputComponents,\r\n      selectComponents,\r\n      layoutComponents,\r\n      labelWidth: 100,\r\n      drawingList: drawingDefault,\r\n      drawingData: {},\r\n      activeId: drawingDefault[0].formId,\r\n      drawerVisible: false,\r\n      formData: {},\r\n      dialogVisible: false,\r\n      generateConf: null,\r\n      showFileName: false,\r\n      activeData: drawingDefault[0]\r\n    }\r\n  },\r\n  created() {\r\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\r\n    document.body.ondrop = event => {\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n  },\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'activeData.label': function (val, oldVal) {\r\n      if (\r\n        this.activeData.placeholder === undefined\r\n        || !this.activeData.tag\r\n        || oldActiveId !== this.activeId\r\n      ) {\r\n        return\r\n      }\r\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\r\n    },\r\n    activeId: {\r\n      handler(val) {\r\n        oldActiveId = val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    const clipboard = new ClipboardJS('#copyNode', {\r\n      text: trigger => {\r\n        const codeStr = this.generateCode()\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return codeStr\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  methods: {\r\n    activeFormItem(element) {\r\n      this.activeData = element\r\n      this.activeId = element.formId\r\n    },\r\n    onEnd(obj, a) {\r\n      if (obj.from !== obj.to) {\r\n        this.activeData = tempActiveData\r\n        this.activeId = this.idGlobal\r\n      }\r\n    },\r\n    addComponent(item) {\r\n      const clone = this.cloneComponent(item)\r\n      this.drawingList.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    cloneComponent(origin) {\r\n      const clone = JSON.parse(JSON.stringify(origin))\r\n      clone.formId = ++this.idGlobal\r\n      clone.span = formConf.span\r\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\r\n      if (!clone.layout) clone.layout = 'colFormItem'\r\n      if (clone.layout === 'colFormItem') {\r\n        clone.vModel = `field${this.idGlobal}`\r\n        clone.placeholder !== undefined && (clone.placeholder += clone.label)\r\n        tempActiveData = clone\r\n      } else if (clone.layout === 'rowFormItem') {\r\n        delete clone.label\r\n        clone.componentName = `row${this.idGlobal}`\r\n        clone.gutter = this.formConf.gutter\r\n        tempActiveData = clone\r\n      }\r\n      return tempActiveData\r\n    },\r\n    AssembleFormData() {\r\n      this.formData = {\r\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\r\n        ...this.formConf\r\n      }\r\n    },\r\n    generate(data) {\r\n      const func = this[`exec${titleCase(this.operationType)}`]\r\n      this.generateConf = data\r\n      func && func(data)\r\n    },\r\n    execRun(data) {\r\n      this.AssembleFormData()\r\n      this.drawerVisible = true\r\n    },\r\n    execDownload(data) {\r\n      const codeStr = this.generateCode()\r\n      const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\r\n      this.$download.saveAs(blob, data.fileName)\r\n    },\r\n    execCopy(data) {\r\n      document.getElementById('copyNode').click()\r\n    },\r\n    empty() {\r\n      this.$confirm('确定要清空所有组件吗？', '提示', { type: 'warning' }).then(\r\n        () => {\r\n          this.drawingList = []\r\n        }\r\n      )\r\n    },\r\n    drawingItemCopy(item, parent) {\r\n      let clone = JSON.parse(JSON.stringify(item))\r\n      clone = this.createIdAndKey(clone)\r\n      parent.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    createIdAndKey(item) {\r\n      item.formId = ++this.idGlobal\r\n      item.renderKey = +new Date()\r\n      if (item.layout === 'colFormItem') {\r\n        item.vModel = `field${this.idGlobal}`\r\n      } else if (item.layout === 'rowFormItem') {\r\n        item.componentName = `row${this.idGlobal}`\r\n      }\r\n      if (Array.isArray(item.children)) {\r\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\r\n      }\r\n      return item\r\n    },\r\n    drawingItemDelete(index, parent) {\r\n      parent.splice(index, 1)\r\n      this.$nextTick(() => {\r\n        const len = this.drawingList.length\r\n        if (len) {\r\n          this.activeFormItem(this.drawingList[len - 1])\r\n        }\r\n      })\r\n    },\r\n    generateCode() {\r\n      const { type } = this.generateConf\r\n      this.AssembleFormData()\r\n      const script = vueScript(makeUpJs(this.formData, type))\r\n      const html = vueTemplate(makeUpHtml(this.formData, type))\r\n      const css = cssStyle(makeUpCss(this.formData))\r\n      return beautifier.html(html + script + css, beautifierConf.html)\r\n    },\r\n    download() {\r\n      this.dialogVisible = true\r\n      this.showFileName = true\r\n      this.operationType = 'download'\r\n    },\r\n    run() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'run'\r\n    },\r\n    copy() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'copy'\r\n    },\r\n    tagChange(newTag) {\r\n      newTag = this.cloneComponent(newTag)\r\n      newTag.vModel = this.activeData.vModel\r\n      newTag.formId = this.activeId\r\n      newTag.span = this.activeData.span\r\n      delete this.activeData.tag\r\n      delete this.activeData.tagIcon\r\n      delete this.activeData.document\r\n      Object.keys(newTag).forEach(key => {\r\n        if (this.activeData[key] !== undefined\r\n          && typeof this.activeData[key] === typeof newTag[key]) {\r\n          newTag[key] = this.activeData[key]\r\n        }\r\n      })\r\n      this.activeData = newTag\r\n      this.updateDrawingList(newTag, this.drawingList)\r\n    },\r\n    updateDrawingList(newTag, list) {\r\n      const index = list.findIndex(item => item.formId === this.activeId)\r\n      if (index > -1) {\r\n        list.splice(index, 1, newTag)\r\n      } else {\r\n        list.forEach(item => {\r\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang='scss'>\r\n.editor-tabs{\r\n  background: #121315;\r\n  .el-tabs__header{\r\n    margin: 0;\r\n    border-bottom-color: #121315;\r\n    .el-tabs__nav{\r\n      border-color: #121315;\r\n    }\r\n  }\r\n  .el-tabs__item{\r\n    height: 32px;\r\n    line-height: 32px;\r\n    color: #888a8e;\r\n    border-left: 1px solid #121315 !important;\r\n    background: #363636;\r\n    margin-right: 5px;\r\n    user-select: none;\r\n  }\r\n  .el-tabs__item.is-active{\r\n    background: #1e1e1e;\r\n    border-bottom-color: #1e1e1e!important;\r\n    color: #fff;\r\n  }\r\n  .el-icon-edit{\r\n    color: #f1fa8c;\r\n  }\r\n  .el-icon-document{\r\n    color: #a95812;\r\n  }\r\n}\r\n\r\n// home\r\n.right-scrollbar {\r\n  .el-scrollbar__view {\r\n    padding: 12px 18px 15px 15px;\r\n  }\r\n}\r\n.left-scrollbar .el-scrollbar__wrap {\r\n  box-sizing: border-box;\r\n  overflow-x: hidden !important;\r\n  margin-bottom: 0 !important;\r\n}\r\n.center-tabs{\r\n  .el-tabs__header{\r\n    margin-bottom: 0!important;\r\n  }\r\n  .el-tabs__item{\r\n    width: 50%;\r\n    text-align: center;\r\n  }\r\n  .el-tabs__nav{\r\n    width: 100%;\r\n  }\r\n}\r\n.reg-item{\r\n  padding: 12px 6px;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  border-radius: 4px;\r\n  .close-btn{\r\n    position: absolute;\r\n    right: -6px;\r\n    top: -6px;\r\n    display: block;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 50%;\r\n    color: #fff;\r\n    text-align: center;\r\n    z-index: 1;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n    &:hover{\r\n      background: rgba(210, 23, 23, 0.5)\r\n    }\r\n  }\r\n  & + .reg-item{\r\n    margin-top: 18px;\r\n  }\r\n}\r\n.action-bar{\r\n  & .el-button+.el-button {\r\n    margin-left: 15px;\r\n  }\r\n  & i {\r\n    font-size: 20px;\r\n    vertical-align: middle;\r\n    position: relative;\r\n    top: -1px;\r\n  }\r\n}\r\n\r\n.custom-tree-node{\r\n  width: 100%;\r\n  font-size: 14px;\r\n  .node-operation{\r\n    float: right;\r\n  }\r\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"]{\r\n    margin-left: 6px;\r\n  }\r\n  .el-icon-plus{\r\n    color: #409EFF;\r\n  }\r\n  .el-icon-delete{\r\n    color: #157a0c;\r\n  }\r\n}\r\n\r\n.left-scrollbar .el-scrollbar__view{\r\n  overflow-x: hidden;\r\n}\r\n\r\n.el-rate{\r\n  display: inline-block;\r\n  vertical-align: text-top;\r\n}\r\n.el-upload__tip{\r\n  line-height: 1.2;\r\n}\r\n\r\n$selectedColor: #f6f7ff;\r\n$lighterBlue: #409EFF;\r\n\r\n.container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.components-list {\r\n  padding: 8px;\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n  .components-item {\r\n    display: inline-block;\r\n    width: 48%;\r\n    margin: 1%;\r\n    transition: transform 0ms !important;\r\n  }\r\n}\r\n.components-draggable{\r\n  padding-bottom: 20px;\r\n}\r\n.components-title{\r\n  font-size: 14px;\r\n  color: #222;\r\n  margin: 6px 2px;\r\n  .svg-icon{\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n.components-body {\r\n  padding: 8px 10px;\r\n  background: $selectedColor;\r\n  font-size: 12px;\r\n  cursor: move;\r\n  border: 1px dashed $selectedColor;\r\n  border-radius: 3px;\r\n  .svg-icon{\r\n    color: #777;\r\n    font-size: 15px;\r\n  }\r\n  &:hover {\r\n    border: 1px dashed #787be8;\r\n    color: #787be8;\r\n    .svg-icon {\r\n      color: #787be8;\r\n    }\r\n  }\r\n}\r\n\r\n.left-board {\r\n  width: 260px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100vh;\r\n}\r\n.left-scrollbar{\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n}\r\n.center-scrollbar {\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n  border-left: 1px solid #f1e8e8;\r\n  border-right: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.center-board {\r\n  height: 100vh;\r\n  width: auto;\r\n  margin: 0 350px 0 260px;\r\n  box-sizing: border-box;\r\n}\r\n.empty-info{\r\n  position: absolute;\r\n  top: 46%;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  color: #ccb1ea;\r\n  letter-spacing: 4px;\r\n}\r\n.action-bar{\r\n  position: relative;\r\n  height: 42px;\r\n  text-align: right;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;;\r\n  border: 1px solid #f1e8e8;\r\n  border-top: none;\r\n  border-left: none;\r\n  .delete-btn{\r\n    color: #F56C6C;\r\n  }\r\n}\r\n.logo-wrapper{\r\n  position: relative;\r\n  height: 42px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n.logo{\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 6px;\r\n  line-height: 30px;\r\n  color: #00afff;\r\n  font-weight: 600;\r\n  font-size: 17px;\r\n  white-space: nowrap;\r\n  > img{\r\n    width: 30px;\r\n    height: 30px;\r\n    vertical-align: top;\r\n  }\r\n  .github{\r\n    display: inline-block;\r\n    vertical-align: sub;\r\n    margin-left: 15px;\r\n    > img{\r\n      height: 22px;\r\n    }\r\n  }\r\n}\r\n\r\n.center-board-row {\r\n  padding: 12px 12px 15px 12px;\r\n  box-sizing: border-box;\r\n  & > .el-form {\r\n    // 69 = 12+15+42\r\n    height: calc(100vh - 69px);\r\n  }\r\n}\r\n.drawing-board {\r\n  height: 100%;\r\n  position: relative;\r\n  .components-body {\r\n    padding: 0;\r\n    margin: 0;\r\n    font-size: 0;\r\n  }\r\n  .sortable-ghost {\r\n    position: relative;\r\n    display: block;\r\n    overflow: hidden;\r\n    &::before {\r\n      content: \" \";\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      height: 3px;\r\n      background: rgb(89, 89, 223);\r\n      z-index: 2;\r\n    }\r\n  }\r\n  .components-item.sortable-ghost {\r\n    width: 100%;\r\n    height: 60px;\r\n    background-color: $selectedColor;\r\n  }\r\n  .active-from-item {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n    & > .component-name{\r\n      color: $lighterBlue;\r\n    }\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n.drawing-item{\r\n  position: relative;\r\n  cursor: move;\r\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child  {\r\n    border: 1px dashed #ccc;\r\n  }\r\n  .el-form-item{\r\n    padding: 12px 10px;\r\n  }\r\n}\r\n.drawing-row-item{\r\n  position: relative;\r\n  cursor: move;\r\n  box-sizing: border-box;\r\n  border: 1px dashed #ccc;\r\n  border-radius: 3px;\r\n  padding: 0 2px;\r\n  margin-bottom: 15px;\r\n  .drawing-row-item {\r\n    margin-bottom: 2px;\r\n  }\r\n  .el-col{\r\n    margin-top: 22px;\r\n  }\r\n  .el-form-item{\r\n    margin-bottom: 0;\r\n  }\r\n  .drag-wrapper{\r\n    min-height: 80px;\r\n  }\r\n  &.active-from-item{\r\n    border: 1px dashed $lighterBlue;\r\n  }\r\n  .component-name{\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    font-size: 12px;\r\n    color: #bbb;\r\n    display: inline-block;\r\n    padding: 0 6px;\r\n  }\r\n}\r\n.drawing-item, .drawing-row-item{\r\n  &:hover {\r\n    & > .el-form-item{\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n    & > .drawing-item-copy, & > .drawing-item-delete{\r\n      display: initial;\r\n    }\r\n  }\r\n  & > .drawing-item-copy, & > .drawing-item-delete{\r\n    display: none;\r\n    position: absolute;\r\n    top: -10px;\r\n    width: 22px;\r\n    height: 22px;\r\n    line-height: 22px;\r\n    text-align: center;\r\n    border-radius: 50%;\r\n    font-size: 12px;\r\n    border: 1px solid;\r\n    cursor: pointer;\r\n    z-index: 1;\r\n  }\r\n  & > .drawing-item-copy{\r\n    right: 56px;\r\n    border-color: $lighterBlue;\r\n    color: $lighterBlue;\r\n    background: #fff;\r\n    &:hover{\r\n      background: $lighterBlue;\r\n      color: #fff;\r\n    }\r\n  }\r\n  & > .drawing-item-delete{\r\n    right: 24px;\r\n    border-color: #F56C6C;\r\n    color: #F56C6C;\r\n    background: #fff;\r\n    &:hover{\r\n      background: #F56C6C;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA0IA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,GAAA,GAAAR,OAAA;AACA,IAAAS,IAAA,GAAAT,OAAA;AACA,IAAAU,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,eAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAc,WAAA;AACA,IAAAC,cAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,EAAAA,aAAA;MACAC,QAAA;MACAC,QAAA,EAAAA,gBAAA;MACAC,eAAA,EAAAA,uBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,UAAA;MACAC,WAAA,EAAAC,uBAAA;MACAC,WAAA;MACAC,QAAA,EAAAF,uBAAA,IAAAG,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,aAAA;MACAC,YAAA;MACAC,YAAA;MACAC,UAAA,EAAAT,uBAAA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA;IACAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,aAAAC,KAAA;MACAA,KAAA,CAAAC,cAAA;MACAD,KAAA,CAAAE,eAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,6BAAAC,gBAAAC,GAAA,EAAAC,MAAA;MACA,IACA,KAAAX,UAAA,CAAAY,WAAA,KAAAC,SAAA,IACA,MAAAb,UAAA,CAAAc,GAAA,IACA3C,WAAA,UAAAsB,QAAA,EACA;QACA;MACA;MACA,KAAAO,UAAA,CAAAY,WAAA,QAAAZ,UAAA,CAAAY,WAAA,CAAAG,OAAA,CAAAJ,MAAA,QAAAD,GAAA;IACA;IACAjB,QAAA;MACAuB,OAAA,WAAAA,QAAAN,GAAA;QACAvC,WAAA,GAAAuC,GAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,SAAA,OAAAC,kBAAA;MACAC,IAAA,WAAAA,KAAAC,OAAA;QACA,IAAAC,OAAA,GAAAL,KAAA,CAAAM,YAAA;QACAN,KAAA,CAAAO,OAAA;UACAC,KAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,OAAAL,OAAA;MACA;IACA;IACAJ,SAAA,CAAAU,EAAA,oBAAAC,CAAA;MACAZ,KAAA,CAAAa,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,OAAA;MACA,KAAApC,UAAA,GAAAoC,OAAA;MACA,KAAA3C,QAAA,GAAA2C,OAAA,CAAA1C,MAAA;IACA;IACA2C,KAAA,WAAAA,MAAAC,GAAA,EAAAC,CAAA;MACA,IAAAD,GAAA,CAAAE,IAAA,KAAAF,GAAA,CAAAG,EAAA;QACA,KAAAzC,UAAA,GAAA5B,cAAA;QACA,KAAAqB,QAAA,QAAAT,QAAA;MACA;IACA;IACA0D,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,KAAA,QAAAC,cAAA,CAAAF,IAAA;MACA,KAAArD,WAAA,CAAAwD,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAE,MAAA;MACA,IAAAH,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,MAAA;MACAH,KAAA,CAAAlD,MAAA,UAAAV,QAAA;MACA4D,KAAA,CAAAO,IAAA,GAAAlE,gBAAA,CAAAkE,IAAA;MACAP,KAAA,CAAAQ,SAAA,QAAAC,IAAA;MACA,KAAAT,KAAA,CAAAU,MAAA,EAAAV,KAAA,CAAAU,MAAA;MACA,IAAAV,KAAA,CAAAU,MAAA;QACAV,KAAA,CAAAW,MAAA,WAAAC,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAhC,WAAA,KAAAC,SAAA,KAAA+B,KAAA,CAAAhC,WAAA,IAAAgC,KAAA,CAAAa,KAAA;QACArF,cAAA,GAAAwE,KAAA;MACA,WAAAA,KAAA,CAAAU,MAAA;QACA,OAAAV,KAAA,CAAAa,KAAA;QACAb,KAAA,CAAAc,aAAA,SAAAF,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAe,MAAA,QAAA1E,QAAA,CAAA0E,MAAA;QACAvF,cAAA,GAAAwE,KAAA;MACA;MACA,OAAAxE,cAAA;IACA;IACAwF,gBAAA,WAAAA,iBAAA;MACA,KAAAhE,QAAA,OAAAiE,cAAA,CAAAtF,OAAA;QACAuF,MAAA,EAAAd,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5D,WAAA;MAAA,GACA,KAAAL,QAAA,CACA;IACA;IACA8E,QAAA,WAAAA,SAAAjF,IAAA;MACA,IAAAkF,IAAA,eAAAR,MAAA,KAAAS,gBAAA,OAAAC,aAAA;MACA,KAAApE,YAAA,GAAAhB,IAAA;MACAkF,IAAA,IAAAA,IAAA,CAAAlF,IAAA;IACA;IACAqF,OAAA,WAAAA,QAAArF,IAAA;MACA,KAAA8E,gBAAA;MACA,KAAAjE,aAAA;IACA;IACAyE,YAAA,WAAAA,aAAAtF,IAAA;MACA,IAAA0C,OAAA,QAAAC,YAAA;MACA,IAAA4C,IAAA,OAAAC,IAAA,EAAA9C,OAAA;QAAAK,IAAA;MAAA;MACA,KAAA0C,SAAA,CAAAC,MAAA,CAAAH,IAAA,EAAAvF,IAAA,CAAA2F,QAAA;IACA;IACAC,QAAA,WAAAA,SAAA5F,IAAA;MACAoB,QAAA,CAAAyE,cAAA,aAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAlD,IAAA;MAAA,GAAAmD,IAAA,CACA;QACAF,MAAA,CAAAxF,WAAA;MACA,CACA;IACA;IACA2F,eAAA,WAAAA,gBAAAtC,IAAA,EAAAuC,MAAA;MACA,IAAAtC,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAP,IAAA;MACAC,KAAA,QAAAuC,cAAA,CAAAvC,KAAA;MACAsC,MAAA,CAAApC,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAuC,cAAA,WAAAA,eAAAxC,IAAA;MAAA,IAAAyC,MAAA;MACAzC,IAAA,CAAAjD,MAAA,UAAAV,QAAA;MACA2D,IAAA,CAAAS,SAAA,QAAAC,IAAA;MACA,IAAAV,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAY,MAAA,WAAAC,MAAA,MAAAxE,QAAA;MACA,WAAA2D,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAe,aAAA,SAAAF,MAAA,MAAAxE,QAAA;MACA;MACA,IAAAqG,KAAA,CAAAC,OAAA,CAAA3C,IAAA,CAAA4C,QAAA;QACA5C,IAAA,CAAA4C,QAAA,GAAA5C,IAAA,CAAA4C,QAAA,CAAAC,GAAA,WAAAC,SAAA;UAAA,OAAAL,MAAA,CAAAD,cAAA,CAAAM,SAAA;QAAA;MACA;MACA,OAAA9C,IAAA;IACA;IACA+C,iBAAA,WAAAA,kBAAAC,KAAA,EAAAT,MAAA;MAAA,IAAAU,MAAA;MACAV,MAAA,CAAAW,MAAA,CAAAF,KAAA;MACA,KAAAG,SAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAtG,WAAA,CAAA0G,MAAA;QACA,IAAAD,GAAA;UACAH,MAAA,CAAAzD,cAAA,CAAAyD,MAAA,CAAAtG,WAAA,CAAAyG,GAAA;QACA;MACA;IACA;IACAtE,YAAA,WAAAA,aAAA;MACA,IAAAI,IAAA,QAAA/B,YAAA,CAAA+B,IAAA;MACA,KAAA+B,gBAAA;MACA,IAAAqC,MAAA,OAAAC,eAAA,MAAAC,YAAA,OAAAvG,QAAA,EAAAiC,IAAA;MACA,IAAAuE,IAAA,OAAAC,iBAAA,MAAAC,gBAAA,OAAA1G,QAAA,EAAAiC,IAAA;MACA,IAAA0E,GAAA,OAAAC,cAAA,MAAAC,cAAA,OAAA7G,QAAA;MACA,OAAA8G,mBAAA,CAAAN,IAAA,CAAAA,IAAA,GAAAH,MAAA,GAAAM,GAAA,EAAAI,qBAAA,CAAAP,IAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACA,KAAA/G,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA2C,GAAA,WAAAA,IAAA;MACA,KAAAhH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA4C,IAAA,WAAAA,KAAA;MACA,KAAAjH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAmE,aAAA;IACA;IACA6C,SAAA,WAAAA,UAAAC,MAAA;MAAA,IAAAC,MAAA;MACAD,MAAA,QAAAnE,cAAA,CAAAmE,MAAA;MACAA,MAAA,CAAAzD,MAAA,QAAAvD,UAAA,CAAAuD,MAAA;MACAyD,MAAA,CAAAtH,MAAA,QAAAD,QAAA;MACAuH,MAAA,CAAA7D,IAAA,QAAAnD,UAAA,CAAAmD,IAAA;MACA,YAAAnD,UAAA,CAAAc,GAAA;MACA,YAAAd,UAAA,CAAAkH,OAAA;MACA,YAAAlH,UAAA,CAAAE,QAAA;MACAiH,MAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAK,OAAA,WAAAC,GAAA;QACA,IAAAL,MAAA,CAAAjH,UAAA,CAAAsH,GAAA,MAAAzG,SAAA,IACA,IAAA0G,QAAA,CAAAhJ,OAAA,EAAA0I,MAAA,CAAAjH,UAAA,CAAAsH,GAAA,WAAAC,QAAA,CAAAhJ,OAAA,EAAAyI,MAAA,CAAAM,GAAA;UACAN,MAAA,CAAAM,GAAA,IAAAL,MAAA,CAAAjH,UAAA,CAAAsH,GAAA;QACA;MACA;MACA,KAAAtH,UAAA,GAAAgH,MAAA;MACA,KAAAQ,iBAAA,CAAAR,MAAA,OAAA1H,WAAA;IACA;IACAkI,iBAAA,WAAAA,kBAAAR,MAAA,EAAAS,IAAA;MAAA,IAAAC,MAAA;MACA,IAAA/B,KAAA,GAAA8B,IAAA,CAAAE,SAAA,WAAAhF,IAAA;QAAA,OAAAA,IAAA,CAAAjD,MAAA,KAAAgI,MAAA,CAAAjI,QAAA;MAAA;MACA,IAAAkG,KAAA;QACA8B,IAAA,CAAA5B,MAAA,CAAAF,KAAA,KAAAqB,MAAA;MACA;QACAS,IAAA,CAAAJ,OAAA,WAAA1E,IAAA;UACA,IAAA0C,KAAA,CAAAC,OAAA,CAAA3C,IAAA,CAAA4C,QAAA,GAAAmC,MAAA,CAAAF,iBAAA,CAAAR,MAAA,EAAArE,IAAA,CAAA4C,QAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}