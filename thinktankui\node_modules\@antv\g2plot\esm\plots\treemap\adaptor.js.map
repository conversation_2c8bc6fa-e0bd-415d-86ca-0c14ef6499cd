{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/treemap/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACjC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,IAAI,iBAAiB,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvH,OAAO,EAAE,OAAO,IAAI,WAAW,EAAE,MAAM,kCAAkC,CAAC;AAC1E,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAE7D,OAAO,EAAE,sBAAsB,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAEjF;;;GAGG;AACH,SAAS,cAAc,CAAC,MAA8B;IAC5C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,UAAU,GAAK,OAAO,WAAZ,CAAa;IAE/B,OAAO,UAAU,CACf;QACE,OAAO,EAAE;YACP,SAAS,EAAE,CAAC,OAAO,CAAC;YACpB,OAAO,EAAE;gBACP,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;gBAC7C,SAAS,EAAE,UAAC,IAAI;oBACd,OAAO;wBACL,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,KAAK,EAAE,IAAI,CAAC,KAAK;qBAClB,CAAC;gBACJ,CAAC;aACF;SACF;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA8B;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAwD,OAAO,MAA/D,EAAE,UAAU,GAA4C,OAAO,WAAnD,EAAE,SAAS,GAAiC,OAAO,UAAxC,EAAE,eAAe,GAAgB,OAAO,gBAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE7E,IAAM,IAAI,GAAG,aAAa,CAAC;QACzB,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,eAAe,EAAE,sBAAsB,CAAC,OAAO,CAAC;QAChD,eAAe,iBAAA;KAChB,CAAC,CAAC;IAEH,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,WAAW;IACX,WAAW,CACT,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,UAAU;YACvB,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE;gBACP,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;aACjB;SACF;KACF,CAAC,CACH,CAAC;IAEF,oCAAoC;IACpC,KAAK,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEhC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA8B;IAClC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAuB;IACzC,IAAA,SAAS,GAAwB,OAAO,UAA/B,EAAE,KAAsB,OAAO,aAAZ,EAAjB,YAAY,mBAAG,EAAE,KAAA,CAAa;IAEjD,IAAM,eAAe,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;IACxD,IAAI,eAAe,EAAE;QACnB,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE;YAC7B,YAAY,kCACP,YAAY;gBACf;oBACE,IAAI,EAAE,YAAY;oBAClB,6BAA6B;oBAC7B,GAAG,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,eAAA,EAAE;iBACnD;qBACF;SACF,CAAC,CAAC;KACJ;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAA8B;IAChD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,YAAY,GAAgB,OAAO,aAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE5C,iBAAiB,CAAC;QAChB,KAAK,OAAA;QACL,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC;KACrC,CAAC,CAAC;IAEH,eAAe;IACf,IAAM,mBAAmB,GAAG,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;IAEvE,IAAI,mBAAmB,EAAE;QACvB,yCAAyC;QACzC,IAAI,mBAAmB,CAAC,MAAM,KAAK,KAAK,EAAE;YACxC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,UAAC,EAAE;gBACpC,EAAE,CAAC,cAAc,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,yCAAyC;YACzC,KAAK,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SACrC;KACF;IAED,YAAY;IACZ,IAAM,eAAe,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;IACxD,IAAI,eAAe,EAAE;QACnB,qBAAqB;QACrB,KAAK,CAAC,aAAa,GAAG,sBAAsB,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;KAC/G;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA8B;IACpD,OAAO,IAAI,CACT,cAAc,EACd,KAAK,EACL,OAAO,CAAC,WAAW,CAAC,EACpB,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,SAAS,EACT,UAAU,EAAE,CACb,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { get } from '@antv/util';\nimport { animation, annotation, interaction as commonInteraction, legend, theme, tooltip } from '../../adaptor/common';\nimport { polygon as basePolygon } from '../../adaptor/geometries/polygon';\nimport { pattern } from '../../adaptor/pattern';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { getAdjustAppendPadding } from '../../utils/padding';\nimport { TreemapOptions } from './types';\nimport { enableDrillInteraction, findInteraction, transformData } from './utils';\n\n/**\n * 获取默认 option\n * @param params\n */\nfunction defaultOptions(params: Params<TreemapOptions>): Params<TreemapOptions> {\n  const { options } = params;\n  const { colorField } = options;\n\n  return deepAssign(\n    {\n      options: {\n        rawFields: ['value'],\n        tooltip: {\n          fields: ['name', 'value', colorField, 'path'],\n          formatter: (data) => {\n            return {\n              name: data.name,\n              value: data.value,\n            };\n          },\n        },\n      },\n    },\n    params\n  );\n}\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<TreemapOptions>): Params<TreemapOptions> {\n  const { chart, options } = params;\n  const { color, colorField, rectStyle, hierarchyConfig, rawFields } = options;\n\n  const data = transformData({\n    data: options.data,\n    colorField: options.colorField,\n    enableDrillDown: enableDrillInteraction(options),\n    hierarchyConfig,\n  });\n\n  chart.data(data);\n\n  // geometry\n  basePolygon(\n    deepAssign({}, params, {\n      options: {\n        xField: 'x',\n        yField: 'y',\n        seriesField: colorField,\n        rawFields: rawFields,\n        polygon: {\n          color,\n          style: rectStyle,\n        },\n      },\n    })\n  );\n\n  // 做一个反转，这样配合排序，可以将最大值放到左上角，最小值放到右下角\n  chart.coordinate().reflect('y');\n\n  return params;\n}\n\n/**\n * 坐标轴\n * @param params\n */\nfunction axis(params: Params<TreemapOptions>): Params<TreemapOptions> {\n  const { chart } = params;\n  chart.axis(false);\n  return params;\n}\n\nfunction adaptorInteraction(options: TreemapOptions): TreemapOptions {\n  const { drilldown, interactions = [] } = options;\n\n  const enableDrillDown = enableDrillInteraction(options);\n  if (enableDrillDown) {\n    return deepAssign({}, options, {\n      interactions: [\n        ...interactions,\n        {\n          type: 'drill-down',\n          // 🚓 这不是一个规范的 API，后续会变更。慎重参考\n          cfg: { drillDownConfig: drilldown, transformData },\n        },\n      ],\n    });\n  }\n  return options;\n}\n\n/**\n * Interaction 配置\n * @param params\n */\nexport function interaction(params: Params<TreemapOptions>): Params<TreemapOptions> {\n  const { chart, options } = params;\n  const { interactions, drilldown } = options;\n\n  commonInteraction({\n    chart,\n    options: adaptorInteraction(options),\n  });\n\n  // 适配 view-zoom\n  const viewZoomInteraction = findInteraction(interactions, 'view-zoom');\n\n  if (viewZoomInteraction) {\n    // 开启缩放 interaction 后，则阻止默认滚动事件，避免整个窗口的滚动\n    if (viewZoomInteraction.enable !== false) {\n      chart.getCanvas().on('mousewheel', (ev) => {\n        ev.preventDefault();\n      });\n    } else {\n      // 手动关闭后，清除。仅对声明 viewZoomInteraction 的清除。\n      chart.getCanvas().off('mousewheel');\n    }\n  }\n\n  // 适应下钻交互面包屑\n  const enableDrillDown = enableDrillInteraction(options);\n  if (enableDrillDown) {\n    // 为面包屑在底部留出 25px 的空间\n    chart.appendPadding = getAdjustAppendPadding(chart.appendPadding, get(drilldown, ['breadCrumb', 'position']));\n  }\n  return params;\n}\n\n/**\n * 矩形树图\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<TreemapOptions>) {\n  return flow(\n    defaultOptions,\n    theme,\n    pattern('rectStyle'),\n    geometry,\n    axis,\n    legend,\n    tooltip,\n    interaction,\n    animation,\n    annotation()\n  )(params);\n}\n"]}