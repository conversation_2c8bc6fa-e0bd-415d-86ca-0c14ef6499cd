{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/stock/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AACzF,OAAO,EAAE,MAAM,EAAE,MAAM,0BAA0B,CAAC;AAClD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAExE,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAEvC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA4B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEnB,IAAA,IAAI,GAAmD,OAAO,KAA1D,EAAE,UAAU,GAAuC,OAAO,WAA9C,EAAE,WAAW,GAA0B,OAAO,YAAjC,EAAE,OAAO,GAAiB,OAAO,QAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAEvE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAEvC,IAAI,cAAc,GAAG,OAAO,CAAC;IAC7B,IAAI,cAAc,KAAK,KAAK,EAAE;QAC5B,cAAc,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC;KACrE;IAED,MAAM,CACJ,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,EAAE;gBACN,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC;gBAChC,KAAK,EAAE,UAAU;aAClB;YACD,MAAM,EAAE,OAAO;YACf,WAAW,EAAE,WAAW;YACxB,SAAS,EAAE,MAAM;YACjB,OAAO,EAAE,cAAc;SACxB;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA4B;;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA2B,OAAO,KAAlC,EAAE,KAAK,GAAoB,OAAO,MAA3B,EAAE,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE/C,IAAM,QAAQ;QACZ,GAAC,MAAM,IAAG;YACR,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,CAAC;SACb;QACD,GAAC,WAAW,IAAG;YACb,MAAM,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;SAC/B;WACF,CAAC;IAEF,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI;QACtC,GAAC,MAAM,IAAG,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;QAC5C,GAAC,OAAO,IAAG,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;YAC7C,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA4B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAoB,OAAO,MAA3B,EAAE,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEzC,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC5B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;KAC5B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KACxB;SAAM;QACL,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA4B;IACzC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE3B,IAAI,MAAM,EAAE;QACV,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;KACnC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAClD,0BAA0B;IAC1B,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;AAC3G,CAAC", "sourcesContent": ["import { animation, annotation, interaction, slider, theme } from '../../adaptor/common';\nimport { schema } from '../../adaptor/geometries';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow, pick } from '../../utils';\nimport { TREND_DOWN, TREND_FIELD, TREND_UP, Y_FIELD } from './constant';\nimport { StockOptions } from './types';\nimport { getStockData } from './utils';\n\n/**\n * 图表配置处理\n * @param params\n */\nfunction geometry(params: Params<StockOptions>): Params<StockOptions> {\n  const { chart, options } = params;\n  const { yField } = options;\n\n  const { data, risingFill, fallingFill, tooltip, stockStyle } = options;\n\n  chart.data(getStockData(data, yField));\n\n  let tooltipOptions = tooltip;\n  if (tooltipOptions !== false) {\n    tooltipOptions = deepAssign({}, { fields: yField }, tooltipOptions);\n  }\n\n  schema(\n    deepAssign({}, params, {\n      options: {\n        schema: {\n          shape: 'candle',\n          color: [risingFill, fallingFill],\n          style: stockStyle,\n        },\n        yField: Y_FIELD,\n        seriesField: TREND_FIELD,\n        rawFields: yField,\n        tooltip: tooltipOptions,\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<StockOptions>): Params<StockOptions> {\n  const { chart, options } = params;\n  const { meta, xAxis, yAxis, xField } = options;\n\n  const baseMeta = {\n    [xField]: {\n      type: 'timeCat',\n      tickCount: 6,\n    },\n    [TREND_FIELD]: {\n      values: [TREND_UP, TREND_DOWN],\n    },\n  };\n\n  const scales = deepAssign(baseMeta, meta, {\n    [xField]: pick(xAxis, AXIS_META_CONFIG_KEYS),\n    [Y_FIELD]: pick(yAxis, AXIS_META_CONFIG_KEYS),\n  });\n\n  chart.scale(scales);\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nexport function axis(params: Params<StockOptions>): Params<StockOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(Y_FIELD, false);\n  } else {\n    chart.axis(Y_FIELD, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nexport function tooltip(params: Params<StockOptions>): Params<StockOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  if (tooltip !== false) {\n    chart.tooltip(tooltip);\n  } else {\n    chart.tooltip(false);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<StockOptions>): Params<StockOptions> {\n  const { chart, options } = params;\n  const { legend } = options;\n\n  if (legend) {\n    chart.legend(TREND_FIELD, legend);\n  } else if (legend === false) {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * K线图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<StockOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  flow(theme, geometry, meta, axis, tooltip, legend, interaction, animation, annotation(), slider)(params);\n}\n"]}