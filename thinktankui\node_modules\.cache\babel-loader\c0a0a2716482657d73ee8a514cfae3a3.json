{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\index.vue", "mtime": 1749109381358}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_gen", "require", "_importTable", "_interopRequireDefault", "_createTable", "_highlight", "hljs", "registerLanguage", "_default", "exports", "default", "name", "components", "importTable", "createTable", "data", "loading", "uniqueId", "ids", "tableNames", "single", "multiple", "showSearch", "total", "tableList", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "tableName", "undefined", "tableComment", "preview", "open", "title", "activeName", "created", "getList", "activated", "time", "$route", "query", "t", "Number", "methods", "_this", "listTable", "addDateRange", "then", "response", "rows", "handleQuery", "handleGenTable", "row", "_this2", "$modal", "msgError", "genType", "genCode", "msgSuccess", "gen<PERSON><PERSON>", "$download", "zip", "handleSynchDb", "_this3", "confirm", "synchDb", "catch", "openImportTable", "$refs", "import", "show", "openCreateTable", "create", "reset<PERSON><PERSON>y", "resetForm", "handlePreview", "_this4", "previewTable", "tableId", "highlightedCode", "code", "key", "vmName", "substring", "lastIndexOf", "indexOf", "language", "length", "result", "highlight", "value", "clipboardSuccess", "handleSelectionChange", "selection", "map", "item", "handleEditTable", "params", "$tab", "openPage", "handleDelete", "_this5", "tableIds", "delTable"], "sources": ["src/views/tool/gen/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"表名称\" prop=\"tableName\">\r\n        <el-input\r\n          v-model=\"queryParams.tableName\"\r\n          placeholder=\"请输入表名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n        <el-input\r\n          v-model=\"queryParams.tableComment\"\r\n          placeholder=\"请输入表描述\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleGenTable\"\r\n          v-hasPermi=\"['tool:gen:code']\"\r\n        >生成</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"openCreateTable\"\r\n          v-hasRole=\"['admin']\"\r\n        >创建</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-upload\"\r\n          size=\"mini\"\r\n          @click=\"openImportTable\"\r\n          v-hasPermi=\"['tool:gen:import']\"\r\n        >导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleEditTable\"\r\n          v-hasPermi=\"['tool:gen:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['tool:gen:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"表名称\"\r\n        align=\"center\"\r\n        prop=\"tableName\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"表描述\"\r\n        align=\"center\"\r\n        prop=\"tableComment\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"实体\"\r\n        align=\"center\"\r\n        prop=\"className\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\r\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handlePreview(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:preview']\"\r\n          >预览</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleEditTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >编辑</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:remove']\"\r\n          >删除</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleSynchDb(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >同步</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-download\"\r\n            @click=\"handleGenTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:code']\"\r\n          >生成代码</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n    <!-- 预览界面 -->\r\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body class=\"scrollbar\">\r\n      <el-tabs v-model=\"preview.activeName\">\r\n        <el-tab-pane\r\n          v-for=\"(value, key) in preview.data\"\r\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\r\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\r\n          :key=\"key\"\r\n        >\r\n          <el-link :underline=\"false\" icon=\"el-icon-document-copy\" v-clipboard:copy=\"value\" v-clipboard:success=\"clipboardSuccess\" style=\"float:right\">复制</el-link>\r\n          <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\r\n    <create-table ref=\"create\" @ok=\"handleQuery\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\r\nimport importTable from \"./importTable\";\r\nimport createTable from \"./createTable\";\r\nimport hljs from \"highlight.js/lib/highlight\";\r\nimport \"highlight.js/styles/github-gist.css\";\r\nhljs.registerLanguage(\"py\", require(\"highlight.js/lib/languages/python\"));\r\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\r\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\r\n\r\nexport default {\r\n  name: \"Gen\",\r\n  components: { importTable, createTable },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: \"\",\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: \"\",\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: \"代码预览\",\r\n        data: {},\r\n        activeName: \"do.py\"\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t;\r\n    if (time != null && time != this.uniqueId) {\r\n      this.uniqueId = time;\r\n      this.queryParams.pageNum = Number(this.$route.query.pageNum);\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.tableList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const tableNames = row.tableName || this.tableNames;\r\n      if (tableNames == \"\") {\r\n        this.$modal.msgError(\"请选择要生成的数据\");\r\n        return;\r\n      }\r\n      if(row.genType === \"1\") {\r\n        genCode(row.tableName).then(response => {\r\n          this.$modal.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\r\n        });\r\n      } else {\r\n        this.$download.zip(\"/tool/gen/batchGenCode?tables=\" + tableNames, \"vfadmin.zip\");\r\n      }\r\n    },\r\n    /** 同步数据库操作 */\r\n    handleSynchDb(row) {\r\n      const tableName = row.tableName;\r\n      this.$modal.confirm('确认要强制同步\"' + tableName + '\"表结构吗？').then(function() {\r\n        return synchDb(tableName);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"同步成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.import.show();\r\n    },\r\n    /** 打开创建表弹窗 */\r\n    openCreateTable() {\r\n      this.$refs.create.show();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data;\r\n        this.preview.open = true;\r\n        this.preview.activeName = \"do.py\";\r\n      });\r\n    },\r\n    /** 高亮显示 */\r\n    highlightedCode(code, key) {\r\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".jinja2\"));\r\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\r\n      const result = hljs.highlight(language, code || \"\", true);\r\n      return result.value || '&nbsp;';\r\n    },\r\n    /** 复制代码成功 */\r\n    clipboardSuccess() {\r\n      this.$modal.msgSuccess(\"复制成功\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId);\r\n      this.tableNames = selection.map(item => item.tableName);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0];\r\n      const tableName = row.tableName || this.tableNames[0];\r\n      const params = { pageNum: this.queryParams.pageNum };\r\n      this.$tab.openPage(\"修改[\" + tableName + \"]生成配置\", '/tool/gen-edit/index/' + tableId, params);\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids;\r\n      this.$modal.confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项？').then(function() {\r\n        return delTable(tableIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA8LA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACAK,kBAAA,CAAAC,gBAAA,OAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,SAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,QAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,eAAAN,OAAA;AACAK,kBAAA,CAAAC,gBAAA,QAAAN,OAAA;AAAA,IAAAO,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA,EAAAC,SAAA;QACAC,YAAA,EAAAD;MACA;MACA;MACAE,OAAA;QACAC,IAAA;QACAC,KAAA;QACAnB,IAAA;QACAoB,UAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,CAAA;IACA,IAAAH,IAAA,YAAAA,IAAA,SAAAtB,QAAA;MACA,KAAAA,QAAA,GAAAsB,IAAA;MACA,KAAAb,WAAA,CAAAC,OAAA,GAAAgB,MAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAd,OAAA;MACA,KAAAU,OAAA;IACA;EACA;EACAO,OAAA;IACA,YACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,KAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,cAAA,OAAAC,YAAA,MAAArB,WAAA,OAAAD,SAAA,GAAAuB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAArB,SAAA,GAAAyB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAtB,KAAA,GAAA0B,QAAA,CAAA1B,KAAA;QACAsB,KAAA,CAAA7B,OAAA;MACA,CACA;IACA;IACA,aACAmC,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACAe,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAnC,UAAA,GAAAkC,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAAA,UAAA;QACA,KAAAoC,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAH,GAAA,CAAAI,OAAA;QACA,IAAAC,YAAA,EAAAL,GAAA,CAAAxB,SAAA,EAAAmB,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAAC,MAAA,CAAAI,UAAA,iBAAAN,GAAA,CAAAO,OAAA;QACA;MACA;QACA,KAAAC,SAAA,CAAAC,GAAA,oCAAA3C,UAAA;MACA;IACA;IACA,cACA4C,aAAA,WAAAA,cAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAnC,SAAA,GAAAwB,GAAA,CAAAxB,SAAA;MACA,KAAA0B,MAAA,CAAAU,OAAA,cAAApC,SAAA,aAAAmB,IAAA;QACA,WAAAkB,YAAA,EAAArC,SAAA;MACA,GAAAmB,IAAA;QACAgB,MAAA,CAAAT,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,IAAA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAH,KAAA,CAAAI,MAAA,CAAAF,IAAA;IACA;IACA,aACAG,UAAA,WAAAA,WAAA;MACA,KAAAjD,SAAA;MACA,KAAAkD,SAAA;MACA,KAAAxB,WAAA;IACA;IACA,WACAyB,aAAA,WAAAA,cAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,iBAAA,EAAAzB,GAAA,CAAA0B,OAAA,EAAA/B,IAAA,WAAAC,QAAA;QACA4B,MAAA,CAAA7C,OAAA,CAAAjB,IAAA,GAAAkC,QAAA,CAAAlC,IAAA;QACA8D,MAAA,CAAA7C,OAAA,CAAAC,IAAA;QACA4C,MAAA,CAAA7C,OAAA,CAAAG,UAAA;MACA;IACA;IACA,WACA6C,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,SAAA,CAAAF,GAAA,CAAAG,WAAA,WAAAH,GAAA,CAAAI,OAAA;MACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAC,SAAA,CAAAD,MAAA,CAAAG,OAAA,WAAAH,MAAA,CAAAK,MAAA;MACA,IAAAC,MAAA,GAAAnF,kBAAA,CAAAoF,SAAA,CAAAH,QAAA,EAAAN,IAAA;MACA,OAAAQ,MAAA,CAAAE,KAAA;IACA;IACA,aACAC,gBAAA,WAAAA,iBAAA;MACA,KAAArC,MAAA,CAAAI,UAAA;IACA;IACA;IACAkC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5E,GAAA,GAAA4E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAjB,OAAA;MAAA;MACA,KAAA5D,UAAA,GAAA2E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAnE,SAAA;MAAA;MACA,KAAAT,MAAA,GAAA0E,SAAA,CAAAN,MAAA;MACA,KAAAnE,QAAA,IAAAyE,SAAA,CAAAN,MAAA;IACA;IACA,aACAS,eAAA,WAAAA,gBAAA5C,GAAA;MACA,IAAA0B,OAAA,GAAA1B,GAAA,CAAA0B,OAAA,SAAA7D,GAAA;MACA,IAAAW,SAAA,GAAAwB,GAAA,CAAAxB,SAAA,SAAAV,UAAA;MACA,IAAA+E,MAAA;QAAAvE,OAAA,OAAAD,WAAA,CAAAC;MAAA;MACA,KAAAwE,IAAA,CAAAC,QAAA,SAAAvE,SAAA,sCAAAkD,OAAA,EAAAmB,MAAA;IACA;IACA,aACAG,YAAA,WAAAA,aAAAhD,GAAA;MAAA,IAAAiD,MAAA;MACA,IAAAC,QAAA,GAAAlD,GAAA,CAAA0B,OAAA,SAAA7D,GAAA;MACA,KAAAqC,MAAA,CAAAU,OAAA,iBAAAsC,QAAA,aAAAvD,IAAA;QACA,WAAAwD,aAAA,EAAAD,QAAA;MACA,GAAAvD,IAAA;QACAsD,MAAA,CAAAjE,OAAA;QACAiE,MAAA,CAAA/C,MAAA,CAAAI,UAAA;MACA,GAAAQ,KAAA;IACA;EACA;AACA", "ignoreList": []}]}