{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\monitor\\cache.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\monitor\\cache.js", "mtime": 1749109381293}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getCache", "request", "url", "method", "listCacheName", "list<PERSON><PERSON><PERSON><PERSON>", "cacheName", "getCacheValue", "cache<PERSON>ey", "clearCacheName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCacheAll"], "sources": ["D:/thinktank/thinktankui/src/api/monitor/cache.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询缓存详细\r\nexport function getCache() {\r\n  return request({\r\n    url: '/monitor/cache',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询缓存名称列表\r\nexport function listCacheName() {\r\n  return request({\r\n    url: '/monitor/cache/getNames',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询缓存键名列表\r\nexport function listCacheKey(cacheName) {\r\n  return request({\r\n    url: '/monitor/cache/getKeys/' + cacheName,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询缓存内容\r\nexport function getCacheValue(cacheName, cacheKey) {\r\n  return request({\r\n    url: '/monitor/cache/getValue/' + cacheName + '/' + cacheKey,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 清理指定名称缓存\r\nexport function clearCacheName(cacheName) {\r\n  return request({\r\n    url: '/monitor/cache/clearCacheName/' + cacheName,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清理指定键名缓存\r\nexport function clearCacheKey(cacheKey) {\r\n  return request({\r\n    url: '/monitor/cache/clearCacheKey/' + cacheKey,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清理全部缓存\r\nexport function clearCacheAll() {\r\n  return request({\r\n    url: '/monitor/cache/clearCacheAll',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAAA,EAAG;EACzB,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,SAAS;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACD,SAAS,EAAEE,QAAQ,EAAE;EACjD,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,SAAS,GAAG,GAAG,GAAGE,QAAQ;IAC5DL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,cAAcA,CAACH,SAAS,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGI,SAAS;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,aAAaA,CAACF,QAAQ,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B,GAAGM,QAAQ;IAC/CL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}