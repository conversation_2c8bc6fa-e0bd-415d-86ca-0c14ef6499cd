{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/sunburst/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EACL,SAAS,EACT,UAAU,EACV,WAAW,IAAI,eAAe,EAC9B,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,GACN,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,OAAO,IAAI,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAGrE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAC7D,OAAO,EAAE,UAAU,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAExG,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA+B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAiF,OAAO,MAAxF,EAAE,KAA+E,OAAO,WAAlD,EAApC,UAAU,mBAAG,uBAAuB,KAAA,EAAE,aAAa,GAA4B,OAAO,cAAnC,EAAE,KAA0B,OAAO,UAAnB,EAAd,SAAS,mBAAG,EAAE,KAAA,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IACtG,IAAM,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IACpC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,+CAA+C;IAC/C,IAAI,KAAK,CAAC;IACV,IAAI,aAAa,EAAE;QACjB,KAAK,GAAG,UAAC,KAAY;YACnB,OAAO,UAAU,CACf,EAAE,EACF;gBACE,WAAW,EAAE,SAAA,IAAI,EAAI,KAAK,CAAC,KAAK,CAAA;aACjC,EACD,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CACjE,CAAC;QACJ,CAAC,CAAC;KACH;IAED,WAAW;IACX,cAAc,CACZ,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,WAAW,EAAE,UAAU;YACvB,SAAS,EAAE,IAAI,iCAAK,UAAU,SAAK,SAAS,QAAE;YAC9C,OAAO,EAAE;gBACP,KAAK,OAAA;gBACL,KAAK,OAAA;gBACL,KAAK,OAAA;aACN;SACF;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;IAC1C,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA+B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1B,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAEhD,uBAAuB;IACvB,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,KAAwC,KAAK,OAA5B,EAAjB,MAAM,mBAAG,CAAC,MAAM,CAAC,KAAA,EAAE,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA/C,sBAAuC,CAAF,CAAW;QACtD,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,QAAA;YACN,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA+B;IACzC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAAsB,OAAO,YAA7B,EAAE,MAAM,GAAc,OAAO,OAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEjD,IAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;QAC7B,IAAI,EAAE,OAAO;QACb,GAAG,EAAE;YACH,WAAW,aAAA;YACX,MAAM,QAAA;SACP;KACF,CAAC,CAAC;IACH,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KACxB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AACD;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;;IAC1C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,eAAe,GAAW,OAAO,gBAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAE1C,OAAO,IAAI,CACT,KAAK,CACH,EAAE;QAEA,GAAC,gBAAgB,IAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;YAE1E,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM;QACL,IAAI,cAAc,GAAG,OAAO,CAAC;QAC7B,4DAA4D;QAC5D,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YAC3B,cAAc,GAAG,UAAU,CACzB,EAAE,EACF;gBACE,WAAW,EAAE,UAAC,KAA0B;oBACtC,OAAA,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;wBACb,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;wBACjD,IAAM,aAAa,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,mBAAmB,EAAE,WAAW,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;wBAChF,IAAM,cAAc,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAC,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC,CAAC;wBAC9E,6BACK,IAAI,KACP,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EACnD,KAAK,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IACtC;oBACJ,CAAC,CAAC;gBATF,CASE;aACL,EACD,cAAc,CACf,CAAC;SACH;QACD,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;KAC/B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAwB;IAC1C,IAAA,SAAS,GAAwB,OAAO,UAA/B,EAAE,KAAsB,OAAO,aAAZ,EAAjB,YAAY,mBAAG,EAAE,KAAA,CAAa;IAEjD,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE;QACtB,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE;YAC7B,YAAY,kCACP,YAAY;gBACf;oBACE,IAAI,EAAE,YAAY;oBAClB,GAAG,EAAE,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,eAAA,EAAE;iBACnD;qBACF;SACF,CAAC,CAAC;KACJ;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAAC,MAA+B;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAE1B,IAAA,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE9B,eAAe,CAAC;QACd,KAAK,OAAA;QACL,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC;KACrC,CAAC,CAAC;IAEH,YAAY;IACZ,IAAI,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,OAAO,EAAE;QACtB,kBAAkB;QAClB,KAAK,CAAC,aAAa,GAAG,sBAAsB,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;KAC/G;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,OAAO,CAAC,eAAe,CAAC,EACxB,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,WAAW,EACX,SAAS,EACT,UAAU,EAAE,CACb,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { get, isFunction, uniq } from '@antv/util';\nimport {\n  animation,\n  annotation,\n  interaction as baseInteraction,\n  legend,\n  pattern,\n  scale,\n  theme,\n} from '../../adaptor/common';\nimport { polygon as polygonAdaptor } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { getAdjustAppendPadding } from '../../utils/padding';\nimport { RAW_FIELDS, SUNBURST_ANCESTOR_FIELD, SUNBURST_PATH_FIELD, SUNBURST_Y_FIELD } from './constant';\nimport { SunburstOptions } from './types';\nimport { transformData } from './utils';\n\n/**\n * geometry 配置处理\n * @param params\n */\nfunction geometry(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart, options } = params;\n  const { color, colorField = SUNBURST_ANCESTOR_FIELD, sunburstStyle, rawFields = [], shape } = options;\n  const data = transformData(options);\n  chart.data(data);\n\n  // 特殊处理下样式，如果没有设置 fillOpacity 的时候，默认根据层级进行填充透明度\n  let style;\n  if (sunburstStyle) {\n    style = (datum: Datum) => {\n      return deepAssign(\n        {},\n        {\n          fillOpacity: 0.85 ** datum.depth,\n        },\n        isFunction(sunburstStyle) ? sunburstStyle(datum) : sunburstStyle\n      );\n    };\n  }\n\n  // geometry\n  polygonAdaptor(\n    deepAssign({}, params, {\n      options: {\n        xField: 'x',\n        yField: 'y',\n        seriesField: colorField,\n        rawFields: uniq([...RAW_FIELDS, ...rawFields]),\n        polygon: {\n          color,\n          style,\n          shape,\n        },\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nexport function axis(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart } = params;\n  chart.axis(false);\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart, options } = params;\n  const { label } = options;\n\n  const geometry = findGeometry(chart, 'polygon');\n\n  // 默认不展示，undefined 也不展示\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { fields = ['name'], callback, ...cfg } = label;\n    geometry.label({\n      fields,\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * coord 配置\n * @param params\n */\nfunction coordinate(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart, options } = params;\n  const { innerRadius, radius, reflect } = options;\n\n  const coord = chart.coordinate({\n    type: 'polar',\n    cfg: {\n      innerRadius,\n      radius,\n    },\n  });\n  if (reflect) {\n    coord.reflect(reflect);\n  }\n\n  return params;\n}\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { options } = params;\n  const { hierarchyConfig, meta } = options;\n\n  return flow(\n    scale(\n      {},\n      {\n        [SUNBURST_Y_FIELD]: get(meta, get(hierarchyConfig, ['field'], 'value')),\n      }\n    )\n  )(params);\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nexport function tooltip(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  if (tooltip === false) {\n    chart.tooltip(false);\n  } else {\n    let tooltipOptions = tooltip;\n    // 设置了 fields，就不进行 customItems 了; 设置 formatter 时，需要搭配 fields\n    if (!get(tooltip, 'fields')) {\n      tooltipOptions = deepAssign(\n        {},\n        {\n          customItems: (items: Types.TooltipItem[]) =>\n            items.map((item) => {\n              const scales = get(chart.getOptions(), 'scales');\n              const pathFormatter = get(scales, [SUNBURST_PATH_FIELD, 'formatter'], (v) => v);\n              const valueFormatter = get(scales, [SUNBURST_Y_FIELD, 'formatter'], (v) => v);\n              return {\n                ...item,\n                name: pathFormatter(item.data[SUNBURST_PATH_FIELD]),\n                value: valueFormatter(item.data.value),\n              };\n            }),\n        },\n        tooltipOptions\n      );\n    }\n    chart.tooltip(tooltipOptions);\n  }\n\n  return params;\n}\n\nfunction adaptorInteraction(options: SunburstOptions): SunburstOptions {\n  const { drilldown, interactions = [] } = options;\n\n  if (drilldown?.enabled) {\n    return deepAssign({}, options, {\n      interactions: [\n        ...interactions,\n        {\n          type: 'drill-down',\n          cfg: { drillDownConfig: drilldown, transformData },\n        },\n      ],\n    });\n  }\n  return options;\n}\n\n/**\n * 交互配置\n * @param params\n * @returns\n */\nfunction interaction(params: Params<SunburstOptions>): Params<SunburstOptions> {\n  const { chart, options } = params;\n\n  const { drilldown } = options;\n\n  baseInteraction({\n    chart,\n    options: adaptorInteraction(options),\n  });\n\n  // 适应下钻交互面包屑\n  if (drilldown?.enabled) {\n    // 为面包屑留出 25px 的空间\n    chart.appendPadding = getAdjustAppendPadding(chart.appendPadding, get(drilldown, ['breadCrumb', 'position']));\n  }\n\n  return params;\n}\n\n/**\n * 旭日图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<SunburstOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    pattern('sunburstStyle'),\n    geometry,\n    axis,\n    meta,\n    legend,\n    coordinate,\n    tooltip,\n    label,\n    interaction,\n    animation,\n    annotation()\n  )(params);\n}\n"]}