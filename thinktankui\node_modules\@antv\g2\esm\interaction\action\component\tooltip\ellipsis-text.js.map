{"version": 3, "file": "ellipsis-text.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/component/tooltip/ellipsis-text.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAEpD,OAAO,MAAM,MAAM,YAAY,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD;;;GAGG;AACH;IAA0C,gCAAM;IAAhD;QAAA,qEAoGC;QAnGS,eAAS,GAAW,CAAC,CAAC;;IAmGhC,CAAC;IA/FQ,8BAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC;QAChB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,2BAAI,GAAX;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACzB,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAM,SAAS,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QAE9B,IAAI,SAAS,GAAG,aAAa,GAAG,EAAE,EAAE;YAClC,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC7B,IAAM,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;gBACvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;aAC1B;YACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;SACxB;IACH,CAAC;IAED;;;OAGG;IACI,2BAAI,GAAX;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAES,kCAAW,GAArB,UAAsB,MAAa;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACzB,IAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;QAEzB,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,OAAO;aAC9B;iBAAM;gBACL,2CAA2C;gBAC3C,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;gBAC1B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAM,MAAM,GAAG;oBACb,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;oBACrB,GAAG,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;iBACzD,CAAA;gBACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;aACnC;YACD,IAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACrC,aAAa;YACb,IAAI,CAAC,OAAO,CAAC,MAAM,YACjB,KAAK,EAAE,UAAU,IACd,MAAM,EACT,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SACrB;IACH,CAAC;IAES,kCAAW,GAArB;QACE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;IAEO,oCAAa,GAArB;;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,IAAM,MAAM,GAAG;YACb,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACrB,GAAG,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;SACzD,CAAC;QAEF,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAM,aAAa,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gBAAgB;QAC9F,IAAM,OAAO,GAAG,IAAI,WAAW,CAAC;YAC9B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU;YACnC,MAAM,QAAA;YACN,OAAO,EAAE,KAAK;YACd,UAAU,EAAE,IAAI;YAChB,SAAS,eACJ,OAAO,CAAC,EAAE,EAAE,aAAa;gBAC1B,qCAAqC;gBACrC,GAAC,iBAAiB,CAAC,eAAe,IAAG,EAAE,WAAW,EAAE,KAAK,EAAE;gBAC3D,GAAC,iBAAiB,CAAC,WAAW,IAAG,EAAE,YAAY,EAAE,WAAW,EAAE;oBAC9D,CACH;SACF,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IACH,mBAAC;AAAD,CAAC,AApGD,CAA0C,MAAM,GAoG/C", "sourcesContent": ["import { isEqual, get, deepMix } from '@antv/util';\nimport { TOOLTIP_CSS_CONST } from '@antv/component';\nimport { Point } from '../../../../interface';\nimport Action from '../../base';\nimport { HtmlTooltip } from '../../../../dependents';\n\n/**\n * 用于组件文本省略后需要展示完整信息的 Tooltip Action\n * @ignore\n */\nexport default class EllipsisText extends Action {\n  private timeStamp: number = 0;\n  private location: Point;\n  private tooltip;\n\n  public destroy() {\n    super.destroy();\n    this.tooltip && this.tooltip.destroy();\n  }\n\n  /**\n   * 显示 Tooltip\n   * @returns\n   */\n  public show() {\n    const context = this.context;\n    const ev = context.event;\n    const lastTimeStamp = this.timeStamp;\n    const timeStamp = +new Date();\n\n    if (timeStamp - lastTimeStamp > 16) {\n      const preLoc = this.location;\n      const curLoc = { x: ev.x, y: ev.y };\n      if (!preLoc || !isEqual(preLoc, curLoc)) {\n        this.showTooltip(curLoc);\n      }\n      this.timeStamp = timeStamp;\n      this.location = curLoc;\n    }\n  }\n\n  /**\n   * 隐藏 Tooltip。\n   * @returns\n   */\n  public hide() {\n    this.hideTooltip();\n    this.location = null;\n  }\n\n  protected showTooltip(curLoc: Point) {\n    const context = this.context;\n    const ev = context.event;\n    const target = ev.target;\n\n    if (target && target.get('tip')) {\n      if (!this.tooltip) {\n        this.renderTooltip(); // 延迟生成\n      } else {\n        // 更新时需要重新获取 region 赋值，避免画布缩放后 tooltip 位置不对\n        const view = context.view;\n        const canvas = view.canvas;\n        const region = {\n          start: { x: 0, y: 0 },\n          end: { x: canvas.get('width'), y: canvas.get('height') },\n        }\n        this.tooltip.set('region', region)\n      }\n      const tipContent = target.get('tip');\n      // 展示 tooltip\n      this.tooltip.update({\n        title: tipContent,\n        ...curLoc,\n      });\n      this.tooltip.show();\n    }\n  }\n\n  protected hideTooltip() {\n    this.tooltip && this.tooltip.hide();\n  }\n\n  private renderTooltip() {\n    const view = this.context.view;\n    const canvas = view.canvas;\n\n    const region = {\n      start: { x: 0, y: 0 },\n      end: { x: canvas.get('width'), y: canvas.get('height') },\n    };\n\n    const theme = view.getTheme();\n    const tooltipStyles = get(theme, ['components', 'tooltip', 'domStyles'], {}); // 获取 tooltip 样式\n    const tooltip = new HtmlTooltip({\n      parent: canvas.get('el').parentNode,\n      region,\n      visible: false,\n      crosshairs: null,\n      domStyles: {\n        ...deepMix({}, tooltipStyles, {\n          // 超长的时候，tooltip tip 最大宽度为 50%，然后可以换行\n          [TOOLTIP_CSS_CONST.CONTAINER_CLASS]: { 'max-width': '50%' },\n          [TOOLTIP_CSS_CONST.TITLE_CLASS]: { 'word-break': 'break-all' },\n        }),\n      },\n    });\n    tooltip.init();\n    tooltip.setCapture(false); // 不允许捕获事件\n    this.tooltip = tooltip;\n  }\n}\n"]}