{"version": 3, "file": "basic.js", "sourceRoot": "", "sources": ["../../../../src/plots/funnel/geometries/basic.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,QAAQ,IAAI,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAG5E,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAExF,OAAO,EAAE,sBAAsB,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAEjE;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA6B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAwC,OAAO,KAAtC,EAAT,IAAI,mBAAG,EAAE,KAAA,EAAE,MAAM,GAAuB,OAAO,OAA9B,EAAE,OAAO,GAAc,OAAO,QAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IACxD,IAAM,UAAU,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE;QAC3C,MAAM,QAAA;QACN,OAAO,SAAA;QACP,OAAO,SAAA;KACR,CAAC,CAAC;IAEH,QAAQ;IACR,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA0E,OAAO,OAAjF,EAAE,MAAM,GAAkE,OAAO,OAAzE,EAAE,KAAK,GAA2D,OAAO,MAAlE,EAAE,OAAO,GAAkD,OAAO,QAAzD,EAAE,KAAK,GAA2C,OAAO,MAAlD,EAAE,KAAyC,OAAO,MAAhC,EAAhB,KAAK,mBAAG,QAAQ,KAAA,EAAE,WAAW,GAAY,OAAO,YAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1F,IAAA,KAAwB,iBAAiB,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAAlE,MAAM,YAAA,EAAE,SAAS,eAAiD,CAAC;IAE3E,YAAY,CAAC;QACX,KAAK,OAAA;QACL,OAAO,EAAE;YACP,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,oBAAoB;YAC5B,UAAU,EAAE,MAAM;YAClB,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;YACtF,OAAO,EAAE;gBACP,KAAK,OAAA;gBACL,OAAO,EAAE,SAAS;gBAClB,KAAK,OAAA;gBACL,KAAK,EAAE,WAAW;aACnB;YACD,KAAK,OAAA;YACL,KAAK,OAAA;SACN;KACF,CAAC,CAAC;IAEH,IAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IACnD,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAExB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,MAA6B;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,YAAY,GAAK,OAAO,aAAZ,CAAa;IACjC,KAAK,CAAC,UAAU,CAAC;QACf,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KAChE,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,MAA6B;IACjD,IAAA,OAAO,GAAY,MAAM,QAAlB,EAAE,KAAK,GAAK,MAAM,MAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,sBAAsB;IACtB,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;IACnE,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IACvD,IAAM,CAAC,GAAG,GAAG,CAAC,SAAS,EAAE,UAAC,IAAI,IAAK,OAAA,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,EAArD,CAAqD,CAAC,CAAC;IAE1F,IAAM,iBAAiB,GAAG,UACxB,KAAY,EACZ,UAAkB,EAClB,IAAU,EACV,cAAmC;QAEnC,IAAM,OAAO,GAAG,OAAO,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC;QACtE,6BACK,cAAc,KACjB,KAAK,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,EACvD,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,UAAU,GAAG,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC,IAC5D;IACJ,CAAC,CAAC;IAEF,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC;IAElD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,MAA6B;IACvD,OAAO,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC;AACjE,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { get, isArray, map } from '@antv/util';\nimport { geometry as baseGeometry } from '../../../adaptor/geometries/base';\nimport { Params } from '../../../core/adaptor';\nimport { Data, Datum } from '../../../types/common';\nimport { findGeometry, flow } from '../../../utils';\nimport { getTooltipMapping } from '../../../utils/tooltip';\nimport { FUNNEL_CONVERSATION, FUNNEL_MAPPING_VALUE, FUNNEL_PERCENT } from '../constant';\nimport { FunnelOptions } from '../types';\nimport { conversionTagComponent, transformData } from './common';\n\n/**\n * 处理字段数据\n * @param params\n */\nfunction field(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { chart, options } = params;\n  const { data = [], yField, maxSize, minSize } = options;\n  const formatData = transformData(data, data, {\n    yField,\n    maxSize,\n    minSize,\n  });\n\n  // 绘制漏斗图\n  chart.data(formatData);\n  return params;\n}\n\n/**\n * geometry处理\n * @param params\n */\nfunction geometry(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { chart, options } = params;\n  const { xField, yField, color, tooltip, label, shape = 'funnel', funnelStyle, state } = options;\n\n  const { fields, formatter } = getTooltipMapping(tooltip, [xField, yField]);\n\n  baseGeometry({\n    chart,\n    options: {\n      type: 'interval',\n      xField: xField,\n      yField: FUNNEL_MAPPING_VALUE,\n      colorField: xField,\n      tooltipFields: isArray(fields) && fields.concat([FUNNEL_PERCENT, FUNNEL_CONVERSATION]),\n      mapping: {\n        shape,\n        tooltip: formatter,\n        color,\n        style: funnelStyle,\n      },\n      label,\n      state,\n    },\n  });\n\n  const geo = findGeometry(params.chart, 'interval');\n  geo.adjust('symmetric');\n\n  return params;\n}\n\n/**\n * 转置处理\n * @param params\n */\nfunction transpose(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { chart, options } = params;\n  const { isTransposed } = options;\n  chart.coordinate({\n    type: 'rect',\n    actions: !isTransposed ? [['transpose'], ['scale', 1, -1]] : [],\n  });\n  return params;\n}\n\n/**\n * 转化率组件\n * @param params\n */\nexport function conversionTag(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { options, chart } = params;\n  const { maxSize } = options;\n\n  // 获取形状位置，再转化为需要的转化率位置\n  const dataArray = get(chart, ['geometries', '0', 'dataArray'], []);\n  const size = get(chart, ['options', 'data', 'length']);\n  const x = map(dataArray, (item) => get(item, ['0', 'nextPoints', '0', 'x']) * size - 0.5);\n\n  const getLineCoordinate = (\n    datum: Datum,\n    datumIndex: number,\n    data: Data,\n    initLineOption: Record<string, any>\n  ): Types.LineOption => {\n    const percent = maxSize - (maxSize - datum[FUNNEL_MAPPING_VALUE]) / 2;\n    return {\n      ...initLineOption,\n      start: [x[datumIndex - 1] || datumIndex - 0.5, percent],\n      end: [x[datumIndex - 1] || datumIndex - 0.5, percent + 0.05],\n    };\n  };\n\n  conversionTagComponent(getLineCoordinate)(params);\n\n  return params;\n}\n\n/**\n * 基础漏斗\n * @param chart\n * @param options\n */\nexport function basicFunnel(params: Params<FunnelOptions>) {\n  return flow(field, geometry, transpose, conversionTag)(params);\n}\n"]}