{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\menu.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\menu.js", "mtime": 1749109381292}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmdldFJvdXRlcnMgPSB2b2lkIDA7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDojrflj5bot6/nlLEKdmFyIGdldFJvdXRlcnMgPSBleHBvcnRzLmdldFJvdXRlcnMgPSBmdW5jdGlvbiBnZXRSb3V0ZXJzKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2dldFJvdXRlcnMnLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9Ow=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getRouters", "exports", "request", "url", "method"], "sources": ["D:/thinktank/thinktankui/src/api/menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取路由\r\nexport const getRouters = () => {\r\n  return request({\r\n    url: '/getRouters',\r\n    method: 'get'\r\n  })\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,IAAMC,UAAU,GAAAC,OAAA,CAAAD,UAAA,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,OAAO,IAAAE,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}]}