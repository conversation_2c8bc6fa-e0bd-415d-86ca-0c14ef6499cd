{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\utils\\dict\\Dict.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\utils\\dict\\Dict.js", "mtime": 1749109381336}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vue", "_interopRequireDefault", "require", "_ruoyi", "_DictMeta", "_DictData", "DEFAULT_DICT_OPTIONS", "types", "Dict", "exports", "default", "_classCallCheck2", "owner", "label", "type", "_createClass2", "key", "value", "init", "options", "_this", "Array", "opts", "mergeRecursive", "undefined", "Error", "ps", "_dictMetas", "map", "t", "DictMeta", "parse", "for<PERSON>ach", "dictMeta", "<PERSON><PERSON>", "set", "lazy", "push", "loadDict", "Promise", "all", "reloadDict", "find", "e", "reject", "concat", "dict", "request", "then", "response", "_dict$type$type", "dicts", "responseConverter", "console", "error", "filter", "d", "DictData", "length", "splice", "apply", "Number", "MAX_SAFE_INTEGER", "_toConsumableArray2"], "sources": ["D:/thinktank/thinktankui/src/utils/dict/Dict.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport { mergeRecursive } from \"@/utils/ruoyi\";\r\nimport DictMeta from './DictMeta'\r\nimport DictData from './DictData'\r\n\r\nconst DEFAULT_DICT_OPTIONS = {\r\n  types: [],\r\n}\r\n\r\n/**\r\n * @classdesc 字典\r\n * @property {Object} label 标签对象，内部属性名为字典类型名称\r\n * @property {Object} dict 字段数组，内部属性名为字典类型名称\r\n * @property {Array.<DictMeta>} _dictMetas 字典元数据数组\r\n */\r\nexport default class Dict {\r\n  constructor() {\r\n    this.owner = null\r\n    this.label = {}\r\n    this.type = {}\r\n  }\r\n\r\n  init(options) {\r\n    if (options instanceof Array) {\r\n      options = { types: options }\r\n    }\r\n    const opts = mergeRecursive(DEFAULT_DICT_OPTIONS, options)\r\n    if (opts.types === undefined) {\r\n      throw new Error('need dict types')\r\n    }\r\n    const ps = []\r\n    this._dictMetas = opts.types.map(t => DictMeta.parse(t))\r\n    this._dictMetas.forEach(dictMeta => {\r\n      const type = dictMeta.type\r\n      Vue.set(this.label, type, {})\r\n      Vue.set(this.type, type, [])\r\n      if (dictMeta.lazy) {\r\n        return\r\n      }\r\n      ps.push(loadDict(this, dictMeta))\r\n    })\r\n    return Promise.all(ps)\r\n  }\r\n\r\n  /**\r\n   * 重新加载字典\r\n   * @param {String} type 字典类型\r\n   */\r\n  reloadDict(type) {\r\n    const dictMeta = this._dictMetas.find(e => e.type === type)\r\n    if (dictMeta === undefined) {\r\n      return Promise.reject(`the dict meta of ${type} was not found`)\r\n    }\r\n    return loadDict(this, dictMeta)\r\n  }\r\n}\r\n\r\n/**\r\n * 加载字典\r\n * @param {Dict} dict 字典\r\n * @param {DictMeta} dictMeta 字典元数据\r\n * @returns {Promise}\r\n */\r\nfunction loadDict(dict, dictMeta) {\r\n  return dictMeta.request(dictMeta)\r\n    .then(response => {\r\n      const type = dictMeta.type\r\n      let dicts = dictMeta.responseConverter(response, dictMeta)\r\n      if (!(dicts instanceof Array)) {\r\n        console.error('the return of responseConverter must be Array.<DictData>')\r\n        dicts = []\r\n      } else if (dicts.filter(d => d instanceof DictData).length !== dicts.length) {\r\n        console.error('the type of elements in dicts must be DictData')\r\n        dicts = []\r\n      }\r\n      dict.type[type].splice(0, Number.MAX_SAFE_INTEGER, ...dicts)\r\n      dicts.forEach(d => {\r\n        Vue.set(dict.label[type], d.value, d.label)\r\n      })\r\n      return dicts\r\n    })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEA,IAAMI,oBAAoB,GAAG;EAC3BC,KAAK,EAAE;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA,IAMqBC,IAAI,GAAAC,OAAA,CAAAC,OAAA;EACvB,SAAAF,KAAA,EAAc;IAAA,IAAAG,gBAAA,CAAAD,OAAA,QAAAF,IAAA;IACZ,IAAI,CAACI,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EAChB;EAAC,WAAAC,aAAA,CAAAL,OAAA,EAAAF,IAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAC,IAAIA,CAACC,OAAO,EAAE;MAAA,IAAAC,KAAA;MACZ,IAAID,OAAO,YAAYE,KAAK,EAAE;QAC5BF,OAAO,GAAG;UAAEZ,KAAK,EAAEY;QAAQ,CAAC;MAC9B;MACA,IAAMG,IAAI,GAAG,IAAAC,qBAAc,EAACjB,oBAAoB,EAAEa,OAAO,CAAC;MAC1D,IAAIG,IAAI,CAACf,KAAK,KAAKiB,SAAS,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MACA,IAAMC,EAAE,GAAG,EAAE;MACb,IAAI,CAACC,UAAU,GAAGL,IAAI,CAACf,KAAK,CAACqB,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIC,iBAAQ,CAACC,KAAK,CAACF,CAAC,CAAC;MAAA,EAAC;MACxD,IAAI,CAACF,UAAU,CAACK,OAAO,CAAC,UAAAC,QAAQ,EAAI;QAClC,IAAMnB,IAAI,GAAGmB,QAAQ,CAACnB,IAAI;QAC1BoB,YAAG,CAACC,GAAG,CAACf,KAAI,CAACP,KAAK,EAAEC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC7BoB,YAAG,CAACC,GAAG,CAACf,KAAI,CAACN,IAAI,EAAEA,IAAI,EAAE,EAAE,CAAC;QAC5B,IAAImB,QAAQ,CAACG,IAAI,EAAE;UACjB;QACF;QACAV,EAAE,CAACW,IAAI,CAACC,QAAQ,CAAClB,KAAI,EAAEa,QAAQ,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,OAAOM,OAAO,CAACC,GAAG,CAACd,EAAE,CAAC;IACxB;;IAEA;AACF;AACA;AACA;EAHE;IAAAV,GAAA;IAAAC,KAAA,EAIA,SAAAwB,UAAUA,CAAC3B,IAAI,EAAE;MACf,IAAMmB,QAAQ,GAAG,IAAI,CAACN,UAAU,CAACe,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,IAAI,KAAKA,IAAI;MAAA,EAAC;MAC3D,IAAImB,QAAQ,KAAKT,SAAS,EAAE;QAC1B,OAAOe,OAAO,CAACK,MAAM,qBAAAC,MAAA,CAAqB/B,IAAI,mBAAgB,CAAC;MACjE;MACA,OAAOwB,QAAQ,CAAC,IAAI,EAAEL,QAAQ,CAAC;IACjC;EAAC;AAAA;AAGH;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACQ,IAAI,EAAEb,QAAQ,EAAE;EAChC,OAAOA,QAAQ,CAACc,OAAO,CAACd,QAAQ,CAAC,CAC9Be,IAAI,CAAC,UAAAC,QAAQ,EAAI;IAAA,IAAAC,eAAA;IAChB,IAAMpC,IAAI,GAAGmB,QAAQ,CAACnB,IAAI;IAC1B,IAAIqC,KAAK,GAAGlB,QAAQ,CAACmB,iBAAiB,CAACH,QAAQ,EAAEhB,QAAQ,CAAC;IAC1D,IAAI,EAAEkB,KAAK,YAAY9B,KAAK,CAAC,EAAE;MAC7BgC,OAAO,CAACC,KAAK,CAAC,0DAA0D,CAAC;MACzEH,KAAK,GAAG,EAAE;IACZ,CAAC,MAAM,IAAIA,KAAK,CAACI,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,YAAYC,iBAAQ;IAAA,EAAC,CAACC,MAAM,KAAKP,KAAK,CAACO,MAAM,EAAE;MAC3EL,OAAO,CAACC,KAAK,CAAC,gDAAgD,CAAC;MAC/DH,KAAK,GAAG,EAAE;IACZ;IACA,CAAAD,eAAA,GAAAJ,IAAI,CAAChC,IAAI,CAACA,IAAI,CAAC,EAAC6C,MAAM,CAAAC,KAAA,CAAAV,eAAA,GAAC,CAAC,EAAEW,MAAM,CAACC,gBAAgB,EAAAjB,MAAA,KAAAkB,mBAAA,CAAArD,OAAA,EAAKyC,KAAK,GAAC;IAC5DA,KAAK,CAACnB,OAAO,CAAC,UAAAwB,CAAC,EAAI;MACjBtB,YAAG,CAACC,GAAG,CAACW,IAAI,CAACjC,KAAK,CAACC,IAAI,CAAC,EAAE0C,CAAC,CAACvC,KAAK,EAAEuC,CAAC,CAAC3C,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOsC,KAAK;EACd,CAAC,CAAC;AACN", "ignoreList": []}]}