{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue?vue&type=style&index=0&id=3dd5307a&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue", "mtime": 1749110937880}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCn0NCg0KLnBhZ2UtY29udGFpbmVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgaGVpZ2h0OiAxMDAlOw0KfQ0KDQovKiDlt6bkvqflr7zoiKrmoI/moLflvI8gKi8NCi5sZWZ0LXNpZGViYXIgew0KICB3aWR0aDogMjAwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICNlNmU2ZTY7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGZsZXgtc2hyaW5rOiAwOw0KICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzOw0KfQ0KDQovKiDmipjlj6DnirbmgIHnmoTkvqfovrnmoI8gKi8NCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIHsNCiAgd2lkdGg6IDY0cHg7DQp9DQoNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5zaWRlYmFyLXNlYXJjaCwNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5lbC1tZW51LWl0ZW0gc3BhbiwNCi5sZWZ0LXNpZGViYXIuY29sbGFwc2VkIC5lbC1zdWJtZW51X190aXRsZSBzcGFuIHsNCiAgZGlzcGxheTogbm9uZTsNCn0NCg0KLmxlZnQtc2lkZWJhci5jb2xsYXBzZWQgLm5ldy1zY2hlbWUtYnRuIHsNCiAgcGFkZGluZzogOHB4IDA7DQogIGZvbnQtc2l6ZTogMDsNCn0NCg0KLmxlZnQtc2lkZWJhci5jb2xsYXBzZWQgLm5ldy1zY2hlbWUtYnRuIGkgew0KICBmb250LXNpemU6IDE2cHg7DQogIG1hcmdpbjogMDsNCn0NCg0KLnNpZGViYXItaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5uZXctc2NoZW1lLWJ0biB7DQogIGZsZXg6IDE7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgcGFkZGluZzogOHB4IDEwcHg7DQp9DQoNCi5zaWRlYmFyLWJ0biB7DQogIHdpZHRoOiAzMHB4Ow0KICBoZWlnaHQ6IDMwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBtYXJnaW4tbGVmdDogNXB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouc2lkZWJhci1zZWFyY2ggew0KICBwYWRkaW5nOiAxMHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLnNpZGViYXItbWVudSB7DQogIGZsZXg6IDE7DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQoNCi5zaWRlYmFyLW1lbnUtbGlzdCB7DQogIGJvcmRlci1yaWdodDogbm9uZTsNCn0NCg0KLmFjdGl2ZS1tZW51LWl0ZW0gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWNmNWZmICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjNDA5RUZGICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOiPnOWNleWbvuagh+agt+W8jyAqLw0KOjp2LWRlZXAgLmVsLW1lbnUtaXRlbSBpLA0KOjp2LWRlZXAgLmVsLXN1Ym1lbnVfX3RpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICB3aWR0aDogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQo6OnYtZGVlcCAuZWwtbWVudS1pdGVtIGkgew0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KOjp2LWRlZXAgLmVsLXN1Ym1lbnVfX3RpdGxlIGkgew0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KOjp2LWRlZXAgLmVsLW1lbnUtaXRlbS5pcy1hY3RpdmUgaSwNCjo6di1kZWVwIC5hY3RpdmUtbWVudS1pdGVtIGkgew0KICBjb2xvcjogIzQwOUVGRjsNCn0NCg0KLyog5Y+z5L6n5YaF5a655Yy65qC35byPICovDQoucmlnaHQtY29udGVudCB7DQogIGZsZXg6IDE7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi50b3AtbmF2IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAwIDIwcHg7DQogIGhlaWdodDogNTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQp9DQoNCi5uYXYtaXRlbXMgew0KICBkaXNwbGF5OiBmbGV4Ow0KfQ0KDQoubmF2LWl0ZW0gew0KICBwYWRkaW5nOiAwIDE1cHg7DQogIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLm5hdi1pdGVtLmFjdGl2ZSB7DQogIGNvbG9yOiAjNDA5RUZGOw0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLm5hdi1pdGVtLmFjdGl2ZTo6YWZ0ZXIgew0KICBjb250ZW50OiAnJzsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICBib3R0b206IDA7DQogIGxlZnQ6IDA7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzQwOUVGRjsNCn0NCg0KLnVzZXItaW5mbyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi51c2VyLWluZm8gc3BhbiB7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi5tYWluLWNvbnRlbnQgew0KICBmbGV4OiAxOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQoudGl0bGUtYXJlYSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQoudGl0bGUgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnRpdGxlIGgyIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE4cHg7DQogIG1hcmdpbi1yaWdodDogMjBweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLnRpdGxlIGgyIGkgew0KICBtYXJnaW4tbGVmdDogNXB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBjdXJzb3I6IHBvaW50ZXI7DQp9DQoNCi50YWJzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KfQ0KDQoudGFicyAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICBwYWRkaW5nLWxlZnQ6IDA7DQp9DQoNCi5hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmFjdGlvbnMgLmVsLWJ1dHRvbiB7DQogIG1hcmdpbi1sZWZ0OiAxNXB4Ow0KfQ0KDQoudGFibGUtYXJlYSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLnRhYmxlLXRvb2xiYXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5sZWZ0LXRvb2xzLCAucmlnaHQtdG9vbHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoubGVmdC10b29scyA+ICogew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQp9DQoNCi5yaWdodC10b29scyA+ICogew0KICBtYXJnaW4tbGVmdDogMTVweDsNCn0NCg0KLmRhdGUtcmFuZ2Ugew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouZWwtZHJvcGRvd24tbGluayB7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi5lbC10YWJsZSB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi8qIOimhueblkVsZW1lbnQgVUnnmoTkuIDkupvpu5jorqTmoLflvI8gKi8NCjo6di1kZWVwIC5lbC1tZW51LWl0ZW0sIDo6di1kZWVwIC5lbC1zdWJtZW51X190aXRsZSB7DQogIGhlaWdodDogNDBweDsNCiAgbGluZS1oZWlnaHQ6IDQwcHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KOjp2LWRlZXAgLmVsLXN1Ym1lbnUgLmVsLW1lbnUtaXRlbSB7DQogIGhlaWdodDogMzZweDsNCiAgbGluZS1oZWlnaHQ6IDM2cHg7DQogIHBhZGRpbmc6IDAgMjBweCAwIDQwcHg7DQp9DQoNCi8qIOmihOitpuiuvue9ruaKveWxieagt+W8jyAqLw0KLndhcm5pbmctZHJhd2VyIHsNCiAgLmVsLWRyYXdlcl9faGVhZGVyIHsNCiAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2VlZTsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgfQ0KDQogIC5lbC1kcmF3ZXJfX2JvZHkgew0KICAgIHBhZGRpbmc6IDA7DQogIH0NCn0NCg0KLndhcm5pbmctZHJhd2VyLWNvbnRlbnQgew0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDIwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZy1ib3R0b206IDcwcHg7IC8qIOS4uuW6lemDqOaMiemSrueVmeWHuuepuumXtCAqLw0KfQ0KDQoud2FybmluZy1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7DQogIHBhZGRpbmctYm90dG9tOiAxNXB4Ow0KfQ0KDQoud2FybmluZy1zZWN0aW9uIGgzIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzMzMzsNCn0NCg0KLndhcm5pbmctb3B0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLndhcm5pbmctb3B0aW9ucyAuZWwtY2hlY2tib3ggew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCg0KLndhcm5pbmctb3B0aW9ucyAuZWwtcmFkaW8gew0KICBtYXJnaW4tcmlnaHQ6IDE1cHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCg0KLnJlZ2lvbi1zZWN0aW9uIHsNCiAgcGFkZGluZzogNXB4IDA7DQp9DQoNCi5yZWdpb24taW5wdXQgew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoucmVnaW9uLXRhZ3Mgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi5yZWdpb24tdGFncyAuZWwtdGFnIHsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouY2F0ZWdvcnktc2VjdGlvbiB7DQogIGN1cnNvcjogcG9pbnRlcjsNCn0NCg0KLmNhdGVnb3J5LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmNhdGVnb3J5LWNvdW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgY29sb3I6ICM5OTk7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCg0KLmNhdGVnb3J5LWNvdW50IGkgew0KICBtYXJnaW4tbGVmdDogNXB4Ow0KfQ0KDQouZHJhd2VyLWZvb3RlciB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgYm90dG9tOiAwOw0KICBsZWZ0OiAwOw0KICByaWdodDogMDsNCiAgcGFkZGluZzogMTVweCAyMHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2VlZTsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQogIGJveC1zaGFkb3c6IDAgLTJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjA1KTsNCn0NCg0KLyog5YWz6ZSu6K+N6K6+572u5oq95bGJ5qC35byPICovDQoua2V5d29yZC1kcmF3ZXIgew0KICAuZWwtZHJhd2VyX19oZWFkZXIgew0KICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgcGFkZGluZzogMTVweCAyMHB4Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICB9DQoNCiAgLmVsLWRyYXdlcl9fYm9keSB7DQogICAgcGFkZGluZzogMDsNCiAgfQ0KfQ0KDQoua2V5d29yZC1kcmF3ZXItY29udGVudCB7DQogIGhlaWdodDogMTAwJTsNCiAgcGFkZGluZzogMjBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBwYWRkaW5nLWJvdHRvbTogNzBweDsgLyog5Li65bqV6YOo5oyJ6ZKu55WZ5Ye656m66Ze0ICovDQp9DQoNCi5rZXl3b3JkLXNlY3Rpb24gew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoua2V5d29yZC1zZWN0aW9uIGgzIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzMzMzsNCn0NCg0KLyog6Ieq5Yqo6aKE6K2m6K6+572u5oq95bGJ5qC35byPICovDQouYXV0by13YXJuaW5nLWRyYXdlciB7DQogIC5lbC1kcmF3ZXJfX2hlYWRlciB7DQogICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICBwYWRkaW5nOiAxNXB4IDIwcHg7DQogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlZWU7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogIH0NCg0KICAuZWwtZHJhd2VyX19ib2R5IHsNCiAgICBwYWRkaW5nOiAwOw0KICB9DQp9DQoNCi5hdXRvLXdhcm5pbmctZHJhd2VyLWNvbnRlbnQgew0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDIwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgcGFkZGluZy1ib3R0b206IDcwcHg7IC8qIOS4uuW6lemDqOaMiemSrueVmeWHuuepuumXtCAqLw0KfQ0KDQouYXV0by13YXJuaW5nLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzMzMzsNCn0NCg0KLmF1dG8td2FybmluZy1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZy1ib3R0b206IDE1cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KfQ0KDQouc2VjdGlvbi1sYWJlbCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgY29sb3I6ICM2NjY7DQp9DQoNCi50aW1lLXJhbmdlLXNlbGVjdG9yIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KfQ0KDQoudGltZS1zZXBhcmF0b3Igew0KICBtYXJnaW46IDAgNXB4Ow0KfQ0KDQoudGltZS1yYW5nZS1ub3RlIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzk5OTsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KfQ0KDQoucGxhdGZvcm0tY2hlY2tib3hlcyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLnBsYXRmb3JtLWNoZWNrYm94ZXMgLmVsLWNoZWNrYm94IHsNCiAgbWFyZ2luLXJpZ2h0OiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQoud2FybmluZy10eXBlLXNlbGVjdG9yLA0KLnByb2Nlc3MtbWV0aG9kLXNlbGVjdG9yLA0KLnByaW9yaXR5LXNlbGVjdG9yLA0KLmhhbmRsZS1tZXRob2Qtc2VsZWN0b3Igew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi53YXJuaW5nLXR5cGUtc2VsZWN0b3IgLmVsLXJhZGlvLA0KLnByb2Nlc3MtbWV0aG9kLXNlbGVjdG9yIC5lbC1yYWRpbywNCi5wcmlvcml0eS1zZWxlY3RvciAuZWwtcmFkaW8sDQouaGFuZGxlLW1ldGhvZC1zZWxlY3RvciAuZWwtcmFkaW8gew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5wcm9jZXNzLXN3aXRjaCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgcGFkZGluZzogNXB4IDA7DQp9DQoNCi5zd2l0Y2gtbGFiZWwgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjNjY2Ow0KfQ0KDQoubm90ZS10ZXh0IHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzk5OTsNCn0NCg0KLm5vdGlmeS1tZXRob2QtY2hlY2tib3hlcyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg0KLm5vdGlmeS1tZXRob2QtY2hlY2tib3hlcyAuZWwtY2hlY2tib3ggew0KICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "index.vue", "sourceRoot": "src/views/warning-center", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <!-- 主体内容 -->\r\n        <div class=\"main-content\">\r\n          <!-- 标题和操作区 -->\r\n          <div class=\"title-area\">\r\n            <div class=\"title\">\r\n              <div style=\"width: 100%; text-align: left;\">\r\n                <h2><i class=\"el-icon-warning-outline\" style=\"color: #E6A23C; margin-right: 8px;\"></i>方太<i class=\"el-icon-edit-outline\"></i></h2>\r\n                <div class=\"tabs\" style=\"text-align: left; margin-top: 10px; margin-left: 0;\">\r\n                  <el-button type=\"text\" icon=\"el-icon-user\">接收人设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-bell\" @click=\"openWarningDialog\">预警设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-data-analysis\" @click=\"openKeywordDialog\">关键词设置</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"actions\">\r\n              <el-switch v-model=\"autoRefresh\" active-text=\"预警开关\"></el-switch>\r\n              <div>\r\n                <el-button type=\"primary\" size=\"small\">人工预警</el-button>\r\n                <el-button type=\"primary\" size=\"small\" @click=\"openAutoWarningDialog\">自动预警</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 表格区域 -->\r\n          <div class=\"table-area\">\r\n            <div class=\"table-toolbar\">\r\n              <div class=\"left-tools\">\r\n                <el-checkbox></el-checkbox>\r\n                <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n              </div>\r\n              <div class=\"right-tools\">\r\n                <span>共计{{total}}条</span>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出下载</el-button>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    字段<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <div class=\"date-range\">\r\n                  <span>2023/04/23 08:00:00 - 2023/04/25 01:00:00</span>\r\n                </div>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    全部<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <el-input\r\n                  placeholder=\"搜索\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  v-model=\"searchText\"\r\n                  style=\"width: 200px;\"\r\n                  clearable\r\n                ></el-input>\r\n              </div>\r\n            </div>\r\n\r\n            <el-table\r\n              :data=\"tableData\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"title\"\r\n                label=\"标题/摘要\"\r\n                show-overflow-tooltip>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"source\"\r\n                label=\"来源类型\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" type=\"danger\">{{ scope.row.source }}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"platform\"\r\n                label=\"平台类型\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"time\"\r\n                label=\"发布时间\"\r\n                width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-view\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-edit\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-delete\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-share\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"currentPage\"\r\n              :limit.sync=\"pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"warningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeWarningDialog\"\r\n      custom-class=\"warning-drawer\">\r\n      <div class=\"warning-drawer-content\">\r\n        <!-- 平台类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.platformType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.platformType.options\"\r\n              :key=\"'platform-' + index\"\r\n              v-model=\"option.checked\"\r\n              @change=\"option.value === 'all' && handleAllCheckbox(warningSettings.platformType)\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容属性 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.contentProperty.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.contentProperty.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.contentProperty.options\"\r\n                :key=\"'content-property-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.infoType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.infoType.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.infoType.options\"\r\n                :key=\"'info-type-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配对象 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchObject.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-model=\"warningSettings.matchObject.allChecked\"\r\n              @change=\"handleMatchObjectAll\">\r\n              全部\r\n            </el-checkbox>\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.matchObject.options\"\r\n              :key=\"'match-object-' + index\"\r\n              v-model=\"option.checked\"\r\n              :disabled=\"warningSettings.matchObject.allChecked\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配方式 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchMethod.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.matchMethod.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.matchMethod.options\"\r\n                :key=\"'match-method-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 发布地区 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.publishRegion.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加发布地区\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"publishRegionInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.publishRegion.regions.length > 0\">\r\n              <el-tag\r\n                v-for=\"(region, index) in warningSettings.publishRegion.regions\"\r\n                :key=\"'region-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removePublishRegion(region.name)\">\r\n                {{ region.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- IP属地 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.ipArea.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加IP属地\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"ipAreaInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.ipArea.areas.length > 0\">\r\n              <el-tag\r\n                v-for=\"(area, index) in warningSettings.ipArea.areas\"\r\n                :key=\"'ip-area-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removeIpArea(area.name)\">\r\n                {{ area.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 媒体类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openMediaCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.mediaCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.mediaCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文章类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openArticleCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.articleCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.articleCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 关键词设置抽屉 -->\r\n    <el-drawer\r\n      title=\"文本词设置\"\r\n      :visible.sync=\"keywordDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeKeywordDialog\"\r\n      custom-class=\"keyword-drawer\">\r\n      <div class=\"keyword-drawer-content\">\r\n        <!-- 允许词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>允许词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"允许词：对文本进行筛选，命中文本的内容会被允许通过\"\r\n            v-model=\"keywordSettings.allowWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 拒绝词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>拒绝词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过\"\r\n            v-model=\"keywordSettings.rejectWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeKeywordDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveKeywordSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 自动预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"autoWarningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeAutoWarningDialog\"\r\n      custom-class=\"auto-warning-drawer\">\r\n      <div class=\"auto-warning-drawer-content\">\r\n        <h3 class=\"auto-warning-title\">自动预警设置</h3>\r\n\r\n        <!-- 预警时间 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警时间</div>\r\n          <div class=\"time-range-selector\">\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startHour\" placeholder=\"小时\" size=\"small\">\r\n              <el-option\r\n                v-for=\"h in 24\"\r\n                :key=\"'start-hour-' + h\"\r\n                :label=\"(h - 1).toString().padStart(2, '0')\"\r\n                :value=\"(h - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n            <span class=\"time-separator\">:</span>\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startMinute\" placeholder=\"分钟\" size=\"small\">\r\n              <el-option\r\n                v-for=\"m in 60\"\r\n                :key=\"'start-minute-' + m\"\r\n                :label=\"(m - 1).toString().padStart(2, '0')\"\r\n                :value=\"(m - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"time-range-note\">\r\n            <span class=\"note-text\">预警时间段开始时间到结束时间</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警平台 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警平台</div>\r\n          <div class=\"platform-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.weibo\">微博</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.wechat\">微信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.website\">网站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.douyin\">抖音</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.redbook\">小红书</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.bilibili\">B站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.zhihu\">知乎</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警类型 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警类型</div>\r\n          <div class=\"warning-type-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.warningType\" label=\"negative\">负面</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"process-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"all\">全部预警</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"onlyAlert\">仅告警 <span class=\"note-text\">(只对符合条件的)</span></el-radio>\r\n          </div>\r\n          <div class=\"process-switch\">\r\n            <span class=\"switch-label\">只对重要账号 <span class=\"note-text\">(对方粉丝大于10万或认证账号)</span></span>\r\n            <el-switch v-model=\"importantAccountOnly\"></el-switch>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 优先级别 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">优先级别</div>\r\n          <div class=\"priority-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"normal\">正常</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"urgent\">紧急</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"handle-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"auto\">自动</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"manual\">人工</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 告知方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">告知方式</div>\r\n          <div class=\"notify-method-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.sms\">短信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.email\">邮件</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.wechatNotify\">微信通知</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeAutoWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveAutoWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 导入分页组件（如果需要）\r\n// import Pagination from \"@/components/Pagination\";\r\n\r\nexport default {\r\n  name: 'InfoSummary',\r\n  // 注册组件（如果需要）\r\n  // components: {\r\n  //   Pagination\r\n  // },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      autoRefresh: true,\r\n      searchText: '',\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 156,\r\n      // 预警设置弹窗相关数据\r\n      publishRegionInput: '',\r\n      ipAreaInput: '',\r\n      warningDialogVisible: false,\r\n      // 关键词设置抽屉相关数据\r\n      keywordDialogVisible: false,\r\n      keywordSettings: {\r\n        allowWords: '允许词：对文本进行筛选，命中文本的内容会被允许通过',\r\n        rejectWords: '拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过'\r\n      },\r\n      // 自动预警设置抽屉相关数据\r\n      autoWarningDialogVisible: false,\r\n      importantAccountOnly: true,\r\n      autoWarningSettings: {\r\n        timeRange: {\r\n          startHour: '06',\r\n          startMinute: '00',\r\n          endHour: '18',\r\n          endMinute: '00'\r\n        },\r\n        platforms: {\r\n          weibo: true,\r\n          wechat: true,\r\n          website: true,\r\n          douyin: true,\r\n          redbook: true,\r\n          bilibili: false,\r\n          zhihu: false\r\n        },\r\n        warningType: 'negative', // negative, positive, all\r\n        processMethod: 'all', // all, onlyAlert\r\n        priority: 'normal', // normal, urgent\r\n        handleMethod: 'auto', // auto, manual\r\n        notifyMethods: {\r\n          sms: true,\r\n          email: false,\r\n          wechatNotify: true\r\n        }\r\n      },\r\n      warningSettings: {\r\n        platformType: {\r\n          title: '平台类型',\r\n          options: [\r\n            { label: '全部', value: 'all', checked: true },\r\n            { label: '网页', value: 'webpage', checked: true },\r\n            { label: '微信', value: 'wechat', checked: true },\r\n            { label: '微博', value: 'weibo', checked: true },\r\n            { label: '头条号', value: 'toutiao', checked: true },\r\n            { label: 'APP', value: 'app', checked: true },\r\n            { label: '视频', value: 'video', checked: true },\r\n            { label: '论坛', value: 'forum', checked: true },\r\n            { label: '报刊', value: 'newspaper', checked: true },\r\n            { label: '问答', value: 'qa', checked: true }\r\n          ]\r\n        },\r\n        contentProperty: {\r\n          title: '内容属性',\r\n          value: 'all', // all, yes, no\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '是', value: 'yes' },\r\n            { label: '不是', value: 'no' }\r\n          ]\r\n        },\r\n        infoType: {\r\n          title: '信息类型',\r\n          value: 'noncomment', // all, noncomment, comment\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '非评论', value: 'noncomment' },\r\n            { label: '评论', value: 'comment' }\r\n          ]\r\n        },\r\n        matchObject: {\r\n          title: '匹配对象',\r\n          allChecked: true,\r\n          options: [\r\n            { label: '标题匹配', value: 'title', checked: false },\r\n            { label: '正文匹配', value: 'content', checked: false },\r\n            { label: '音频/图片匹配', value: 'media', checked: false },\r\n            { label: '原文匹配', value: 'original', checked: false }\r\n          ]\r\n        },\r\n        matchMethod: {\r\n          title: '匹配方式',\r\n          value: 'exact', // exact, fuzzy\r\n          options: [\r\n            { label: '精准', value: 'exact' },\r\n            { label: '模糊', value: 'fuzzy' }\r\n          ]\r\n        },\r\n        publishRegion: {\r\n          title: '发布地区',\r\n          regions: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        ipArea: {\r\n          title: 'IP属地',\r\n          areas: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        mediaCategory: {\r\n          title: '媒体类别',\r\n          count: 0\r\n        },\r\n        articleCategory: {\r\n          title: '文章类别',\r\n          count: 0\r\n        }\r\n      },\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [], icon: 'el-icon-view' },\r\n        { name: '品牌', count: 1, children: [], icon: 'el-icon-star-on' },\r\n        { name: '方太', count: 0, isItem: true, icon: 'el-icon-office-building' },\r\n        { name: '人物', count: 0, children: [], icon: 'el-icon-user' },\r\n        { name: '机构', count: 0, children: [], icon: 'el-icon-office-building' },\r\n        { name: '产品', count: 0, children: [], icon: 'el-icon-goods' },\r\n        { name: '事件', count: 0, children: [], icon: 'el-icon-bell' },\r\n        { name: '话题', count: 0, children: [], icon: 'el-icon-chat-dot-square' }\r\n      ],\r\n      tableData: [\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:01:00'\r\n        },\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:07:46'\r\n        },\r\n        {\r\n          title: '在集成灶领域中的5%，持续超过了7个品牌相加，方太集成灶',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 18:22:45'\r\n        },\r\n        {\r\n          title: '以空气质量提出史上最严苛标准，方太发布\"致净厨房\"理念',\r\n          source: '负面',\r\n          platform: '头条号',\r\n          time: '2023-04-24 17:49:45'\r\n        },\r\n        {\r\n          title: '厨电行业\"十年一遇\"的创新产品，方太发布全球首款\"蒸烤一体机\"',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 17:12:10'\r\n        },\r\n        {\r\n          title: '方太成立20周年之际，推出第二代\"智净洗碗机\"，全球首次三代同台亮相',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 15:15:16'\r\n        },\r\n        {\r\n          title: '厨房电器十年来变革与创新，方新一代集成灶发布，升级蒸、烤一体功能',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:29:09'\r\n        },\r\n        {\r\n          title: '【方太】全球首款\"蒸烤一体机\"发布，方太再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:19:21'\r\n        },\r\n        {\r\n          title: '方太厨房电器发布全球首款\"蒸烤一体机\"，方太厨电再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:48:04'\r\n        },\r\n        {\r\n          title: '【方太】家用厨电市场增长放缓，方太发力高端市场，AI/IOT技术成新增长点',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:34:54'\r\n        }\r\n      ],\r\n      multipleSelection: []\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSelectionChange(val) {\r\n      this.multipleSelection = val;\r\n    },\r\n    getList() {\r\n      // 这里可以添加获取数据的逻辑\r\n      console.log('获取数据，页码：', this.currentPage, '每页条数：', this.pageSize);\r\n      // 模拟API调用\r\n      // listData(this.queryParams).then(response => {\r\n      //   this.tableData = response.rows;\r\n      //   this.total = response.total;\r\n      // });\r\n    },\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.getList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 打开预警设置弹窗\r\n    openWarningDialog() {\r\n      this.warningDialogVisible = true;\r\n    },\r\n    // 关闭预警设置弹窗\r\n    closeWarningDialog() {\r\n      this.warningDialogVisible = false;\r\n    },\r\n    // 打开关键词设置抽屉\r\n    openKeywordDialog() {\r\n      this.keywordDialogVisible = true;\r\n    },\r\n    // 关闭关键词设置抽屉\r\n    closeKeywordDialog() {\r\n      this.keywordDialogVisible = false;\r\n    },\r\n    // 保存预警设置\r\n    saveWarningSettings() {\r\n      // 这里可以添加保存预警设置的逻辑\r\n      console.log('保存预警设置:', this.warningSettings);\r\n      this.$message({\r\n        message: '预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeWarningDialog();\r\n    },\r\n\r\n    // 处理全部复选框\r\n    handleAllCheckbox(section) {\r\n      const allOption = section.options.find(opt => opt.value === 'all');\r\n      if (allOption && allOption.checked) {\r\n        // 如果全部被选中，则选中所有选项\r\n        section.options.forEach(opt => {\r\n          opt.checked = true;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 处理匹配对象全部复选框\r\n    handleMatchObjectAll(checked) {\r\n      this.warningSettings.matchObject.allChecked = checked;\r\n      if (checked) {\r\n        // 如果全部被选中，则取消选中其他选项\r\n        this.warningSettings.matchObject.options.forEach(opt => {\r\n          opt.checked = false;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 添加发布地区\r\n    addPublishRegion(region) {\r\n      if (region && !this.warningSettings.publishRegion.regions.some(r => r.name === region)) {\r\n        this.warningSettings.publishRegion.regions.push({ name: region, value: region });\r\n      }\r\n    },\r\n\r\n    // 删除发布地区\r\n    removePublishRegion(region) {\r\n      const index = this.warningSettings.publishRegion.regions.findIndex(r => r.name === region);\r\n      if (index !== -1) {\r\n        this.warningSettings.publishRegion.regions.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 添加IP属地\r\n    addIpArea(area) {\r\n      if (area && !this.warningSettings.ipArea.areas.some(a => a.name === area)) {\r\n        this.warningSettings.ipArea.areas.push({ name: area, value: area });\r\n      }\r\n    },\r\n\r\n    // 删除IP属地\r\n    removeIpArea(area) {\r\n      const index = this.warningSettings.ipArea.areas.findIndex(a => a.name === area);\r\n      if (index !== -1) {\r\n        this.warningSettings.ipArea.areas.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 打开媒体类别对话框\r\n    openMediaCategoryDialog() {\r\n      this.$message({\r\n        message: '媒体类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 打开文章类别对话框\r\n    openArticleCategoryDialog() {\r\n      this.$message({\r\n        message: '文章类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 保存关键词设置\r\n    saveKeywordSettings() {\r\n      // 这里可以添加保存关键词设置的逻辑\r\n      console.log('保存关键词设置:', this.keywordSettings);\r\n      this.$message({\r\n        message: '关键词设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeKeywordDialog();\r\n    },\r\n\r\n    // 打开自动预警设置抽屉\r\n    openAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = true;\r\n    },\r\n\r\n    // 关闭自动预警设置抽屉\r\n    closeAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = false;\r\n    },\r\n\r\n    // 保存自动预警设置\r\n    saveAutoWarningSettings() {\r\n      // 这里可以添加保存自动预警设置的逻辑\r\n      console.log('保存自动预警设置:', this.autoWarningSettings);\r\n      this.$message({\r\n        message: '自动预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeAutoWarningDialog();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 菜单图标样式 */\r\n::v-deep .el-menu-item i,\r\n::v-deep .el-submenu__title i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  width: 16px;\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-menu-item i {\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-submenu__title i {\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-menu-item.is-active i,\r\n::v-deep .active-menu-item i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-nav {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  height: 50px;\r\n  border-bottom: 1px solid #eee;\r\n  background-color: #fff;\r\n}\r\n\r\n.nav-items {\r\n  display: flex;\r\n}\r\n\r\n.nav-item {\r\n  padding: 0 15px;\r\n  line-height: 50px;\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.nav-item.active {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.nav-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: #409EFF;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info span {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  overflow-y: auto;\r\n}\r\n\r\n.title-area {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  margin-right: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.title h2 i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n  color: #909399;\r\n  cursor: pointer;\r\n}\r\n\r\n.tabs {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.tabs .el-button {\r\n  margin-right: 15px;\r\n  padding-left: 0;\r\n}\r\n\r\n.actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.actions .el-button {\r\n  margin-left: 15px;\r\n}\r\n\r\n.table-area {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.left-tools, .right-tools {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.left-tools > * {\r\n  margin-right: 10px;\r\n}\r\n\r\n.right-tools > * {\r\n  margin-left: 15px;\r\n}\r\n\r\n.date-range {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #606266;\r\n}\r\n\r\n.el-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 预警设置抽屉样式 */\r\n.warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.warning-section {\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.warning-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.warning-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-options .el-checkbox {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.warning-options .el-radio {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.region-section {\r\n  padding: 5px 0;\r\n}\r\n\r\n.region-input {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.region-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.region-tags .el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-section {\r\n  cursor: pointer;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.category-count {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #999;\r\n  font-size: 13px;\r\n}\r\n\r\n.category-count i {\r\n  margin-left: 5px;\r\n}\r\n\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 15px 20px;\r\n  background-color: #fff;\r\n  border-top: 1px solid #eee;\r\n  text-align: right;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 关键词设置抽屉样式 */\r\n.keyword-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.keyword-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.keyword-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.keyword-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 自动预警设置抽屉样式 */\r\n.auto-warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.auto-warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.auto-warning-title {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.auto-warning-section {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.section-label {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  color: #666;\r\n}\r\n\r\n.time-range-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.time-separator {\r\n  margin: 0 5px;\r\n}\r\n\r\n.time-range-note {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 5px;\r\n}\r\n\r\n.platform-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.platform-checkboxes .el-checkbox {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.warning-type-selector,\r\n.process-method-selector,\r\n.priority-selector,\r\n.handle-method-selector {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-type-selector .el-radio,\r\n.process-method-selector .el-radio,\r\n.priority-selector .el-radio,\r\n.handle-method-selector .el-radio {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.process-switch {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 10px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.switch-label {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.note-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.notify-method-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.notify-method-checkboxes .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}