{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\formula.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\formula.js", "mtime": 1749109533008}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyIpOwp2YXIgX2NsYXNzQ2FsbENoZWNrMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NsYXNzQ2FsbENoZWNrLmpzIikpOwp2YXIgX2NyZWF0ZUNsYXNzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUNsYXNzLmpzIikpOwp2YXIgX2NhbGxTdXBlcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9jYWxsU3VwZXIuanMiKSk7CnZhciBfc3VwZXJQcm9wR2V0MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovdGhpbmt0YW5rL3RoaW5rdGFua3VpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3N1cGVyUHJvcEdldC5qcyIpKTsKdmFyIF9pbmhlcml0czIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbmhlcml0cy5qcyIpKTsKdmFyIF9kZWZpbmVQcm9wZXJ0eTIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L3RoaW5rdGFuay90aGlua3Rhbmt1aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKdmFyIF9lbWJlZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vYmxvdHMvZW1iZWQuanMiKSk7CnZhciBGb3JtdWxhID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChfRW1iZWQpIHsKICBmdW5jdGlvbiBGb3JtdWxhKCkgewogICAgKDAsIF9jbGFzc0NhbGxDaGVjazIuZGVmYXVsdCkodGhpcywgRm9ybXVsYSk7CiAgICByZXR1cm4gKDAsIF9jYWxsU3VwZXIyLmRlZmF1bHQpKHRoaXMsIEZvcm11bGEsIGFyZ3VtZW50cyk7CiAgfQogICgwLCBfaW5oZXJpdHMyLmRlZmF1bHQpKEZvcm11bGEsIF9FbWJlZCk7CiAgcmV0dXJuICgwLCBfY3JlYXRlQ2xhc3MyLmRlZmF1bHQpKEZvcm11bGEsIFt7CiAgICBrZXk6ICJodG1sIiwKICAgIHZhbHVlOiBmdW5jdGlvbiBodG1sKCkgewogICAgICB2YXIgX3RoaXMkdmFsdWUgPSB0aGlzLnZhbHVlKCksCiAgICAgICAgZm9ybXVsYSA9IF90aGlzJHZhbHVlLmZvcm11bGE7CiAgICAgIHJldHVybiAiPHNwYW4+Ii5jb25jYXQoZm9ybXVsYSwgIjwvc3Bhbj4iKTsKICAgIH0KICB9XSwgW3sKICAgIGtleTogImNyZWF0ZSIsCiAgICB2YWx1ZTogZnVuY3Rpb24gY3JlYXRlKHZhbHVlKSB7CiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IKICAgICAgaWYgKHdpbmRvdy5rYXRleCA9PSBudWxsKSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGb3JtdWxhIG1vZHVsZSByZXF1aXJlcyBLYVRlWC4nKTsKICAgICAgfQogICAgICB2YXIgbm9kZSA9ICgwLCBfc3VwZXJQcm9wR2V0Mi5kZWZhdWx0KShGb3JtdWxhLCAiY3JlYXRlIiwgdGhpcywgMikoW3ZhbHVlXSk7CiAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7CiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcgogICAgICAgIHdpbmRvdy5rYXRleC5yZW5kZXIodmFsdWUsIG5vZGUsIHsKICAgICAgICAgIHRocm93T25FcnJvcjogZmFsc2UsCiAgICAgICAgICBlcnJvckNvbG9yOiAnI2YwMCcKICAgICAgICB9KTsKICAgICAgICBub2RlLnNldEF0dHJpYnV0ZSgnZGF0YS12YWx1ZScsIHZhbHVlKTsKICAgICAgfQogICAgICByZXR1cm4gbm9kZTsKICAgIH0KICB9LCB7CiAgICBrZXk6ICJ2YWx1ZSIsCiAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUoZG9tTm9kZSkgewogICAgICByZXR1cm4gZG9tTm9kZS5nZXRBdHRyaWJ1dGUoJ2RhdGEtdmFsdWUnKTsKICAgIH0KICB9XSk7Cn0oX2VtYmVkLmRlZmF1bHQpOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShGb3JtdWxhLCAiYmxvdE5hbWUiLCAnZm9ybXVsYScpOwooMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShGb3JtdWxhLCAiY2xhc3NOYW1lIiwgJ3FsLWZvcm11bGEnKTsKKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoRm9ybXVsYSwgInRhZ05hbWUiLCAnU1BBTicpOwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBGb3JtdWxhOw=="}, {"version": 3, "names": ["_embed", "_interopRequireDefault", "require", "Formula", "_Embed", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "html", "_this$value", "formula", "concat", "create", "window", "katex", "Error", "node", "_superPropGet2", "render", "throwOnError", "errorColor", "setAttribute", "domNode", "getAttribute", "Embed", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/formula.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\n\nclass Formula extends Embed {\n  static blotName = 'formula';\n  static className = 'ql-formula';\n  static tagName = 'SPAN';\n\n  static create(value: string) {\n    // @ts-expect-error\n    if (window.katex == null) {\n      throw new Error('Formula module requires KaTeX.');\n    }\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      // @ts-expect-error\n      window.katex.render(value, node, {\n        throwOnError: false,\n        errorColor: '#f00',\n      });\n      node.setAttribute('data-value', value);\n    }\n    return node;\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('data-value');\n  }\n\n  html() {\n    const { formula } = this.value();\n    return `<span>${formula}</span>`;\n  }\n}\n\nexport default Formula;\n"], "mappings": ";;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,IAE/BC,OAAO,0BAAAC,MAAA;EAAA,SAAAD,QAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,OAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,OAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,OAAA,EAAAC,MAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,OAAA;IAAAQ,GAAA;IAAAC,KAAA,EA0BX,SAAAC,IAAIA,CAAA,EAAG;MACL,IAAAC,WAAA,GAAoB,IAAI,CAACF,KAAK,CAAC,CAAC;QAAxBG,OAAA,GAAAD,WAAA,CAAAC,OAAA;MACR,gBAAAC,MAAA,CAAgBD,OAAQ;IAC1B;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAxBA,SAAOK,MAAMA,CAACL,KAAa,EAAE;MAC3B;MACA,IAAIM,MAAM,CAACC,KAAK,IAAI,IAAI,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;MACnD;MACA,IAAMC,IAAI,OAAAC,cAAA,CAAAhB,OAAA,EAAAH,OAAA,sBAAgBS,KAAK,EAAY;MAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B;QACAM,MAAM,CAACC,KAAK,CAACI,MAAM,CAACX,KAAK,EAAES,IAAI,EAAE;UAC/BG,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE;QACd,CAAC,CAAC;QACFJ,IAAI,CAACK,YAAY,CAAC,YAAY,EAAEd,KAAK,CAAC;MACxC;MACA,OAAOS,IAAI;IACb;EAAA;IAAAV,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACe,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACC,YAAY,CAAC,YAAY,CAAC;IAC3C;EAAA;AAAA,EAxBoBC,cAAK;AAAA,IAAAC,gBAAA,CAAAxB,OAAA,EAArBH,OAAO,cACO,SAAS;AAAA,IAAA2B,gBAAA,CAAAxB,OAAA,EADvBH,OAAO,eAEQ,YAAY;AAAA,IAAA2B,gBAAA,CAAAxB,OAAA,EAF3BH,OAAO,aAGM,MAAM;AAAA,IAAA4B,QAAA,GAAAC,OAAA,CAAA1B,OAAA,GA6BVH,OAAO", "ignoreList": []}]}