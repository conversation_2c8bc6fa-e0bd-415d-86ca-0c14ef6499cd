{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\ThemePicker\\index.vue", "mtime": 1749109381328}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/ThemePicker", "sourcesContent": ["<template>\r\n  <el-color-picker\r\n    v-model=\"theme\"\r\n    :predefine=\"['#409EFF', '#1890ff', '#304156','#212121','#11a983', '#13c2c2', '#6959CD', '#f5222d', ]\"\r\n    class=\"theme-picker\"\r\n    popper-class=\"theme-picker-dropdown\"\r\n  />\r\n</template>\r\n\r\n<script>\r\nconst ORIGINAL_THEME = '#409EFF' // default color\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      chalk: '', // content of theme-chalk css\r\n      theme: ''\r\n    }\r\n  },\r\n  computed: {\r\n    defaultTheme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    defaultTheme: {\r\n      handler: function(val, oldVal) {\r\n        this.theme = val\r\n      },\r\n      immediate: true\r\n    },\r\n    async theme(val) {\r\n      await this.setTheme(val)\r\n    }\r\n  },\r\n  created() {\r\n    if(this.defaultTheme !== ORIGINAL_THEME) {\r\n      this.setTheme(this.defaultTheme)\r\n    }\r\n  },\r\n  methods: {\r\n    async setTheme(val) {\r\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\r\n      if (typeof val !== 'string') return\r\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\r\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\r\n\r\n      const getHandler = (variable, id) => {\r\n        return () => {\r\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\r\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\r\n\r\n          let styleTag = document.getElementById(id)\r\n          if (!styleTag) {\r\n            styleTag = document.createElement('style')\r\n            styleTag.setAttribute('id', id)\r\n            document.head.appendChild(styleTag)\r\n          }\r\n          styleTag.innerText = newStyle\r\n        }\r\n      }\r\n\r\n      if (!this.chalk) {\r\n        const url = `/styles/theme-chalk/index.css`\r\n        await this.getCSSString(url, 'chalk')\r\n      }\r\n\r\n      const chalkHandler = getHandler('chalk', 'chalk-style')\r\n      chalkHandler()\r\n\r\n      const styles = [].slice.call(document.querySelectorAll('style'))\r\n        .filter(style => {\r\n          const text = style.innerText\r\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\r\n        })\r\n      styles.forEach(style => {\r\n        const { innerText } = style\r\n        if (typeof innerText !== 'string') return\r\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\r\n      })\r\n\r\n      this.$emit('change', val)\r\n    },\r\n\r\n    updateStyle(style, oldCluster, newCluster) {\r\n      let newStyle = style\r\n      oldCluster.forEach((color, index) => {\r\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\r\n      })\r\n      return newStyle\r\n    },\r\n\r\n    getCSSString(url, variable) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.onreadystatechange = () => {\r\n          if (xhr.readyState === 4 && xhr.status === 200) {\r\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\r\n            resolve()\r\n          }\r\n        }\r\n        xhr.open('GET', url)\r\n        xhr.send()\r\n      })\r\n    },\r\n\r\n    getThemeCluster(theme) {\r\n      const tintColor = (color, tint) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        if (tint === 0) { // when primary color is in its rgb space\r\n          return [red, green, blue].join(',')\r\n        } else {\r\n          red += Math.round(tint * (255 - red))\r\n          green += Math.round(tint * (255 - green))\r\n          blue += Math.round(tint * (255 - blue))\r\n\r\n          red = red.toString(16)\r\n          green = green.toString(16)\r\n          blue = blue.toString(16)\r\n\r\n          return `#${red}${green}${blue}`\r\n        }\r\n      }\r\n\r\n      const shadeColor = (color, shade) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        red = Math.round((1 - shade) * red)\r\n        green = Math.round((1 - shade) * green)\r\n        blue = Math.round((1 - shade) * blue)\r\n\r\n        red = red.toString(16)\r\n        green = green.toString(16)\r\n        blue = blue.toString(16)\r\n\r\n        return `#${red}${green}${blue}`\r\n      }\r\n\r\n      const clusters = [theme]\r\n      for (let i = 0; i <= 9; i++) {\r\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\r\n      }\r\n      clusters.push(shadeColor(theme, 0.1))\r\n      return clusters\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n.theme-message,\r\n.theme-picker-dropdown {\r\n  z-index: 99999 !important;\r\n}\r\n\r\n.theme-picker .el-color-picker__trigger {\r\n  height: 26px !important;\r\n  width: 26px !important;\r\n  padding: 2px;\r\n}\r\n\r\n.theme-picker-dropdown .el-color-dropdown__link-btn {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}