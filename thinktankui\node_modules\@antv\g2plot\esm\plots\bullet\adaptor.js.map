{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/bullet/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AAC7C,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACrF,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAG3D,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAE/D,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAAgF,OAAO,YAAvF,EAAE,WAAW,GAAmE,OAAO,YAA1E,EAAE,UAAU,GAAuD,OAAO,WAA9D,EAAE,YAAY,GAAyC,OAAO,aAAhD,EAAE,MAAM,GAAiC,OAAO,OAAxC,EAAE,KAAK,GAA0B,OAAO,MAAjC,EAAE,MAAM,GAAkB,OAAO,OAAzB,EAAE,IAAI,GAAY,OAAO,KAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAC3G,OAAO;IACD,IAAA,KAAmB,aAAa,CAAC,OAAO,CAAC,EAAvC,GAAG,SAAA,EAAE,GAAG,SAAA,EAAE,EAAE,QAA2B,CAAC;IAChD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEf,gBAAgB;IAChB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,UAAU;YAClB,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC;YAC1B,QAAQ,EAAE;gBACR,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC;gBAC1B,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;gBAChC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;aACzB;SACF;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,mBAAmB;IACnB,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAEnC,kBAAkB;IAClB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;YAC5B,QAAQ,EAAE;gBACR,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;gBAC5B,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC;gBAClC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;aAC3B;SACF;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEZ,iBAAiB;IACjB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,WAAW;YACnB,WAAW,EAAE,MAAM;YACnB,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;YAC3B,KAAK,EAAE;gBACL,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;gBAC3B,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC;gBACjC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBACnC,CAAC,CAAC,UAAC,IAAW,IAAK,OAAA,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAA7B,CAA6B;oBAChD,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC;gBAC3B,KAAK,EAAE,MAAM,KAAK,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;aACnD;SACF;KACF,CAAC,CAAC;IACH,KAAK,CAAC,CAAC,CAAC,CAAC;IAET,eAAe;IACf,IAAI,MAAM,KAAK,YAAY,EAAE;QAC3B,KAAK,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC;KAChC;IAED,6BAAY,MAAM,KAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,EAAE,IAAG;AACpD,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA6B;;IACxC,IAAA,OAAO,GAAU,MAAM,QAAhB,EAAE,GAAG,GAAK,MAAM,IAAX,CAAY;IACxB,IAAA,KAAK,GAA2D,OAAO,MAAlE,EAAE,KAAK,GAAoD,OAAO,MAA3D,EAAE,WAAW,GAAuC,OAAO,YAA9C,EAAE,UAAU,GAA2B,OAAO,WAAlC,EAAE,YAAY,GAAa,OAAO,aAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEhF,IAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC;IACzB,OAAO,IAAI,CACT,KAAK;QAED,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,YAAY,IAAG,KAAK;;QAIrB,GAAC,YAAY,IAAG,EAAE,GAAG,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAE,GAAG,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;QACpE,GAAC,WAAW,IAAG;YACb,IAAI,EAAE,UAAG,YAAY,CAAE;SACxB;QACD,GAAC,UAAU,IAAG;YACZ,IAAI,EAAE,UAAG,YAAY,CAAE;SACxB;YAEJ,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA6B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA2D,OAAO,MAAlE,EAAE,KAAK,GAAoD,OAAO,MAA3D,EAAE,MAAM,GAA4C,OAAO,OAAnD,EAAE,YAAY,GAA8B,OAAO,aAArC,EAAE,UAAU,GAAkB,OAAO,WAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAEhF,KAAK,CAAC,IAAI,CAAC,UAAG,UAAU,CAAE,EAAE,KAAK,CAAC,CAAC;IACnC,KAAK,CAAC,IAAI,CAAC,UAAG,WAAW,CAAE,EAAE,KAAK,CAAC,CAAC;IAEpC,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,UAAG,MAAM,CAAE,EAAE,KAAK,CAAC,CAAC;KAChC;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,UAAG,MAAM,CAAE,EAAE,KAAK,CAAC,CAAC;KAChC;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,UAAG,YAAY,CAAE,EAAE,KAAK,CAAC,CAAC;KACtC;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,UAAG,YAAY,CAAE,EAAE,KAAK,CAAC,CAAC;KACtC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA6B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IAC3B,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IACzC,4BAA4B;IAC5B,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAErB,4CAA4C;IAC5C,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5B,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5B,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE5B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA6B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4C,OAAO,MAAnD,EAAE,YAAY,GAA8B,OAAO,aAArC,EAAE,WAAW,GAAiB,OAAO,YAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAC3D,IAAA,KAAmD,KAAK,CAAC,UAAU,EAAlE,aAAa,QAAA,EAAE,eAAe,QAAA,EAAE,cAAc,QAAoB,CAAC;IAE1E,IAAI,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;QACvB,aAAa,CAAC,KAAK,CAAC,UAAG,UAAU,CAAE,aACjC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,IAChC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAC9B,CAAC;KACJ;SAAM;QACL,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC5B;IACD,IAAI,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;QACzB,eAAe,CAAC,KAAK,CAAC,UAAG,YAAY,CAAE,aACrC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,IAChC,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,EAChC,CAAC;KACJ;SAAM;QACL,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC9B;IACD,IAAI,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;QACxB,cAAc,CAAC,KAAK,CAAC,UAAG,WAAW,CAAE,aACnC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,IAChC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,EAC/B,CAAC;KACJ;SAAM;QACL,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC7B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B;IACnD,0BAA0B;IAC1B,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AAC5F,CAAC", "sourcesContent": ["import { get, isFunction } from '@antv/util';\nimport { animation, interaction, scale, theme, tooltip } from '../../adaptor/common';\nimport { interval, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport { deepAssign, flow, transformLabel } from '../../utils';\nimport { BulletOptions } from './types';\nimport { transformData } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<BulletOptions>): Params<BulletOptions> {\n  const { chart, options } = params;\n  const { bulletStyle, targetField, rangeField, measureField, xField, color, layout, size, label } = options;\n  // 处理数据\n  const { min, max, ds } = transformData(options);\n  chart.data(ds);\n\n  // rangeGeometry\n  const r = deepAssign({}, params, {\n    options: {\n      xField: xField,\n      yField: rangeField,\n      seriesField: 'rKey',\n      isStack: true,\n      label: get(label, 'range'),\n      interval: {\n        color: get(color, 'range'),\n        style: get(bulletStyle, 'range'),\n        size: get(size, 'range'),\n      },\n    },\n  });\n  interval(r);\n  // 范围值的 tooltip 隐藏掉\n  chart.geometries[0].tooltip(false);\n\n  // measureGeometry\n  const m = deepAssign({}, params, {\n    options: {\n      xField: xField,\n      yField: measureField,\n      seriesField: 'mKey',\n      isStack: true,\n      label: get(label, 'measure'),\n      interval: {\n        color: get(color, 'measure'),\n        style: get(bulletStyle, 'measure'),\n        size: get(size, 'measure'),\n      },\n    },\n  });\n  interval(m);\n\n  // targetGeometry\n  const t = deepAssign({}, params, {\n    options: {\n      xField: xField,\n      yField: targetField,\n      seriesField: 'tKey',\n      label: get(label, 'target'),\n      point: {\n        color: get(color, 'target'),\n        style: get(bulletStyle, 'target'),\n        size: isFunction(get(size, 'target'))\n          ? (data: Datum) => get(size, 'target')(data) / 2\n          : get(size, 'target') / 2,\n        shape: layout === 'horizontal' ? 'line' : 'hyphen',\n      },\n    },\n  });\n  point(t);\n\n  // 水平的时候，要转换坐标轴\n  if (layout === 'horizontal') {\n    chart.coordinate().transpose();\n  }\n\n  return { ...params, ext: { data: { min, max } } };\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<BulletOptions>): Params<BulletOptions> {\n  const { options, ext } = params;\n  const { xAxis, yAxis, targetField, rangeField, measureField, xField } = options;\n\n  const extData = ext.data;\n  return flow(\n    scale(\n      {\n        [xField]: xAxis,\n        [measureField]: yAxis,\n      },\n      // 额外的 meta\n      {\n        [measureField]: { min: extData?.min, max: extData?.max, sync: true },\n        [targetField]: {\n          sync: `${measureField}`,\n        },\n        [rangeField]: {\n          sync: `${measureField}`,\n        },\n      }\n    )\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<BulletOptions>): Params<BulletOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, measureField, rangeField, targetField } = options;\n\n  chart.axis(`${rangeField}`, false);\n  chart.axis(`${targetField}`, false);\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(`${xField}`, false);\n  } else {\n    chart.axis(`${xField}`, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(`${measureField}`, false);\n  } else {\n    chart.axis(`${measureField}`, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nfunction legend(params: Params<BulletOptions>): Params<BulletOptions> {\n  const { chart, options } = params;\n  const { legend } = options;\n  chart.removeInteraction('legend-filter');\n  // @TODO 后续看是否内部自定义一个 legend\n  chart.legend(legend);\n\n  // 默认关闭掉所在 color 字段的 legend, 从而不影响自定义的legend\n  chart.legend('rKey', false);\n  chart.legend('mKey', false);\n  chart.legend('tKey', false);\n\n  return params;\n}\n\n/**\n * label 配置\n * @param params\n */\nfunction label(params: Params<BulletOptions>): Params<BulletOptions> {\n  const { chart, options } = params;\n  const { label, measureField, targetField, rangeField } = options;\n  const [rangeGeometry, measureGeometry, targetGeometry] = chart.geometries;\n\n  if (get(label, 'range')) {\n    rangeGeometry.label(`${rangeField}`, {\n      layout: [{ type: 'limit-in-plot' }],\n      ...transformLabel(label.range),\n    });\n  } else {\n    rangeGeometry.label(false);\n  }\n  if (get(label, 'measure')) {\n    measureGeometry.label(`${measureField}`, {\n      layout: [{ type: 'limit-in-plot' }],\n      ...transformLabel(label.measure),\n    });\n  } else {\n    measureGeometry.label(false);\n  }\n  if (get(label, 'target')) {\n    targetGeometry.label(`${targetField}`, {\n      layout: [{ type: 'limit-in-plot' }],\n      ...transformLabel(label.target),\n    });\n  } else {\n    targetGeometry.label(false);\n  }\n\n  return params;\n}\n\n/**\n * 子弹图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<BulletOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  flow(geometry, meta, axis, legend, theme, label, tooltip, interaction, animation)(params);\n}\n"]}