{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/radial-bar/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACzG,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAEjG,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAEtD;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAgC;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAU,KAAK,GAAoE,OAAO,SAA3E,EAAE,KAAK,GAA6D,OAAO,MAApE,EAAE,OAAO,GAAoD,OAAO,QAA3D,EAAE,UAAU,GAAwC,OAAO,WAA/C,EAAE,IAAI,GAAkC,OAAO,KAAzC,EAAE,MAAM,GAA0B,OAAO,OAAjC,EAAE,MAAM,GAAkB,OAAO,OAAzB,EAAE,IAAI,GAAY,OAAO,KAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEnG,WAAW;IACX,IAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrD,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAExB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,OAAO,SAAA;YACP,WAAW,EAAE,UAAU;YACvB,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,OAAA;gBACL,KAAK,EAAE,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;aACxD;YACD,+BAA+B;YAC/B,cAAc,EAAE,OAAO,CAAC,WAAW;YACnC,cAAc,EAAE,OAAO,CAAC,WAAW;YACnC,gBAAgB,EAAE,OAAO,CAAC,aAAa;SACxC;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,KAAK,CAAC;YACJ,KAAK,OAAA;YACL,OAAO,EAAE,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,OAAA,EAAE,EAAE;SACxF,CAAC,CAAC;KACJ;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAAgC;;IAC3C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,MAAM,GAA2D,OAAO,OAAlE,EAAE,MAAM,GAAmD,OAAO,OAA1D,EAAE,IAAI,GAA6C,OAAO,KAApD,EAAE,OAAO,GAAoC,OAAO,QAA3C,EAAE,OAAO,GAA2B,OAAO,QAAlC,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAEjF,IAAM,UAAU,GAAG,OAAO,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnG,IAAM,WAAW,GAAG,kBAAkB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC3D,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG;YACR,GAAG,EAAE,CAAC;YACN,GAAG,EAAE,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC;SAChD;YACD,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAAgC;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAwC,OAAO,OAA/C,EAAE,WAAW,GAA2B,OAAO,YAAlC,EAAE,UAAU,GAAe,OAAO,WAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAE9D,KAAK;SACF,UAAU,CAAC;QACV,IAAI,EAAE,OAAO;QACb,GAAG,EAAE;YACH,MAAM,QAAA;YACN,WAAW,aAAA;YACX,UAAU,YAAA;YACV,QAAQ,UAAA;SACT;KACF,CAAC;SACD,SAAS,EAAE,CAAC;IACf,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAAgC;IAC3C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAY,OAAO,OAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAClC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC1B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAAgC;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEzD,8BAA8B;IAC9B,IAAI,CAAC,KAAK,EAAE;QACV,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC/B;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,gBAAgB,CAAC,KAAK,CAAC;YACrB,MAAM,EAAE,CAAC,MAAM,CAAC;YAChB,QAAQ,UAAA;YACR,GAAG,wBACE,cAAc,CAAC,GAAG,CAAC,KACtB,IAAI,EAAE,OAAO,GACd;SACF,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC;IACtD,OAAO,IAAI,CACT,OAAO,CAAC,UAAU,CAAC,EACnB,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,WAAW,EACX,SAAS,EACT,KAAK,EACL,OAAO,EACP,MAAM,EACN,UAAU,EAAE,EACZ,KAAK,CACN,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { animation, annotation, interaction, legend, scale, theme, tooltip } from '../../adaptor/common';\nimport { interval, point } from '../../adaptor/geometries';\nimport { pattern } from '../../adaptor/pattern';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, processIllegalData, transformLabel } from '../../utils';\nimport { RadialBarOptions } from './types';\nimport { getScaleMax, getStackedData } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<RadialBarOptions>): Params<RadialBarOptions> {\n  const { chart, options } = params;\n  const { barStyle: style, color, tooltip, colorField, type, xField, yField, data, shape } = options;\n\n  // 处理不合法的数据\n  const processData = processIllegalData(data, yField);\n  chart.data(processData);\n\n  const p = deepAssign({}, params, {\n    options: {\n      tooltip,\n      seriesField: colorField,\n      interval: {\n        style,\n        color,\n        shape: shape || (type === 'line' ? 'line' : 'intervel'),\n      },\n      // 柱子的一些样式设置：柱子最小宽度、柱子最大宽度、柱子背景\n      minColumnWidth: options.minBarWidth,\n      maxColumnWidth: options.maxBarWidth,\n      columnBackground: options.barBackground,\n    },\n  });\n  interval(p);\n  if (type === 'line') {\n    point({\n      chart,\n      options: { xField, yField, seriesField: colorField, point: { shape: 'circle', color } },\n    });\n  }\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<RadialBarOptions>): Params<RadialBarOptions> {\n  const { options } = params;\n  const { yField, xField, data, isStack, isGroup, colorField, maxAngle } = options;\n\n  const actualData = isStack && !isGroup && colorField ? getStackedData(data, xField, yField) : data;\n  const processData = processIllegalData(actualData, yField);\n  return flow(\n    scale({\n      [yField]: {\n        min: 0,\n        max: getScaleMax(maxAngle, yField, processData),\n      },\n    })\n  )(params);\n}\n\n/**\n * coordinate 配置\n * @param params\n */\nfunction coordinate(params: Params<RadialBarOptions>): Params<RadialBarOptions> {\n  const { chart, options } = params;\n  const { radius, innerRadius, startAngle, endAngle } = options;\n\n  chart\n    .coordinate({\n      type: 'polar',\n      cfg: {\n        radius,\n        innerRadius,\n        startAngle,\n        endAngle,\n      },\n    })\n    .transpose();\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nexport function axis(params: Params<RadialBarOptions>): Params<RadialBarOptions> {\n  const { chart, options } = params;\n  const { xField, xAxis } = options;\n  chart.axis(xField, xAxis);\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<RadialBarOptions>): Params<RadialBarOptions> {\n  const { chart, options } = params;\n  const { label, yField } = options;\n\n  const intervalGeometry = findGeometry(chart, 'interval');\n\n  // label 为 false, 空 则不显示 label\n  if (!label) {\n    intervalGeometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    intervalGeometry.label({\n      fields: [yField],\n      callback,\n      cfg: {\n        ...transformLabel(cfg),\n        type: 'polar',\n      },\n    });\n  }\n\n  return params;\n}\n\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<RadialBarOptions>) {\n  return flow(\n    pattern('barStyle'),\n    geometry,\n    meta,\n    axis,\n    coordinate,\n    interaction,\n    animation,\n    theme,\n    tooltip,\n    legend,\n    annotation(),\n    label\n  )(params);\n}\n"]}