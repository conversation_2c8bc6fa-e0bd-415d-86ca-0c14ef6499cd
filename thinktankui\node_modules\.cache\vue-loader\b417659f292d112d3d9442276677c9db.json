{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue?vue&type=template&id=3dd5307a&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue", "mtime": 1749110937880}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1749109532675}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}