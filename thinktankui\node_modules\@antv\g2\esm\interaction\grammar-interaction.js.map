{"version": 3, "file": "grammar-interaction.js", "sourceRoot": "", "sources": ["../../src/interaction/grammar-interaction.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAGrF,OAAO,EAAE,YAAY,EAAE,oBAAoB,EAAE,MAAM,mBAAmB,CAAC;AACvE,OAAO,kBAAkB,MAAM,WAAW,CAAC;AAC3C,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC,iBAAiB;AACjB,MAAM,UAAU,WAAW,CAAC,SAAiB,EAAE,OAA4B,EAAE,GAAS;IACpF,IAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjC,IAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,mCAAmC;IACnC,IAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAClF,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,IAAI,KAAK,CAAC,mCAA4B,UAAU,CAAE,CAAC,CAAC;KAC3D;IACD,IAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAO;QACL,MAAM,QAAA;QACN,UAAU,YAAA;QACV,GAAG,KAAA;KACJ,CAAC;AACJ,CAAC;AAED,YAAY;AACZ,SAAS,aAAa,CAAC,YAA0B;IACvC,IAAA,MAAM,GAAsB,YAAY,OAAlC,EAAE,UAAU,GAAU,YAAY,WAAtB,EAAE,GAAG,GAAK,YAAY,IAAjB,CAAkB;IACjD,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,MAAM,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,iBAAU,MAAM,CAAC,IAAI,4CAAkC,UAAU,CAAE,CAAC,CAAC;KACtF;AACH,CAAC;AAED,IAAM,UAAU,GAAG;IACjB,KAAK,EAAE,OAAO;IACd,WAAW,EAAE,YAAY;IACzB,GAAG,EAAE,KAAK;IACV,QAAQ,EAAE,UAAU;IACpB,UAAU,EAAE,YAAY;CACzB,CAAC;AAyHF;;GAEG;AACH;IAAgD,sCAAW;IAczD,4BAAY,IAAU,EAAE,KAAuB;QAA/C,YACE,kBAAM,IAAI,EAAE,KAAK,CAAC,SAEnB;QAPO,oBAAc,GAAgB,EAAE,CAAC;QACzC,mBAAmB;QACX,gBAAU,GAAgB,EAAE,CAAC;QAInC,KAAI,CAAC,KAAK,GAAG,KAAK,CAAC;;IACrB,CAAC;IAED;;OAEG;IACI,iCAAI,GAAX;QACE,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,iBAAM,IAAI,WAAE,CAAC;IACf,CAAC;IAED;;OAEG;IACI,oCAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC,CAAC,QAAQ;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACO,uCAAU,GAApB;QAAA,iBAUC;QATC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,OAAO,EAAE,QAAQ;YACjC,IAAI,CAAC,OAAO,EAAE,UAAC,IAAI;gBACjB,IAAM,QAAQ,GAAG,KAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACxD,IAAI,QAAQ,EAAE;oBACZ,yCAAyC;oBACzC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;iBACxC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,wCAAW,GAArB;QAAA,iBASC;QARC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,OAAO,EAAE,QAAQ;YACjC,IAAI,CAAC,OAAO,EAAE,UAAC,IAAI;gBACjB,IAAM,QAAQ,GAAG,KAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACxD,IAAI,QAAQ,EAAE;oBACZ,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;iBACvC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB;IACb,wCAAW,GAAnB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,eAAe;QACf,IAAI,CAAC,KAAK,EAAE,UAAC,QAA2B;YACtC,IAAI,CAAC,QAAQ,EAAE,UAAC,IAAqB;gBACnC,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC3B,gCAAgC;oBAChC,IAAI,CAAC,YAAY,GAAG;wBAClB,MAAM,EAAE,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC;wBAClD,UAAU,EAAE,SAAS;qBACtB,CAAC;iBACH;qBAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAChC,SAAS;oBACT,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjE;qBAAM,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC/B,QAAQ;oBACR,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC9B,IAAM,QAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACzD,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;oBACvB,IAAI,CAAC,SAAS,EAAE,UAAC,SAAS,EAAE,GAAG;wBAC5B,IAAI,CAAC,YAA+B,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,QAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3F,CAAC,CAAC,CAAC;iBACJ;gBACD,4CAA4C;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,eAAe;IACP,wCAAW,GAAnB,UAAoB,QAAgB;QAClC,IAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,cAAc;QACd,IAAI,eAAe,KAAK,QAAQ,EAAE;YAChC,OAAO,IAAI,CAAC;SACb;QAED,IAAI,QAAQ,KAAK,UAAU,CAAC,WAAW,EAAE;YACvC,cAAc;YACd,OAAO,IAAI,CAAC;SACb;QAED,IAAI,QAAQ,KAAK,UAAU,CAAC,UAAU,EAAE;YACtC,+BAA+B;YAC/B,OAAO,eAAe,KAAK,UAAU,CAAC,KAAK,CAAC;SAC7C;QAED,IAAI,QAAQ,KAAK,UAAU,CAAC,KAAK,EAAE;YACjC,4CAA4C;YAC5C,OAAO,eAAe,KAAK,UAAU,CAAC,UAAU,CAAC;SAClD;QAED,IAAI,QAAQ,KAAK,UAAU,CAAC,GAAG,EAAE;YAC/B,OAAO,eAAe,KAAK,UAAU,CAAC,UAAU,IAAI,eAAe,KAAK,UAAU,CAAC,KAAK,CAAC;SAC1F;QAED,IAAI,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE;YACpC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACzB,2BAA2B;gBAC3B,OAAO,eAAe,KAAK,UAAU,CAAC,GAAG,CAAC;aAC3C;iBAAM,IAAI,eAAe,KAAK,UAAU,CAAC,KAAK,EAAE;gBAC/C,sBAAsB;gBACtB,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gBAAgB;IACR,2CAAc,GAAtB,UAAuB,QAAgB,EAAE,IAAqB;QAC5D,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;YAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACxC,kCAAkC;YAClC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YACD,2BAA2B;YAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aACpC;YACD,OAAO,IAAI,CAAC,CAAC,sBAAsB;SACpC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,sCAAS,GAAjB,UAAkB,QAAgB;QAChC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC,eAAe;IACvC,CAAC;IAED,kBAAkB;IACV,yCAAY,GAApB,UAAqB,QAAgB,EAAE,IAAI;QACzC,uCAAuC;QACvC,IAAI,QAAQ,KAAK,UAAU,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE;YAC5E,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACxC,yBAAyB;QACzB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAC9B,CAAC;IACD,eAAe;IACP,mCAAM,GAAd,UAAe,QAAQ,EAAE,IAAI;QAC3B,OAAO,QAAQ,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;IAC/C,CAAC;IAED,uCAAuC;IAC/B,8CAAiB,GAAzB,UAA0B,QAAgB,EAAE,IAAqB;QAAjE,iBAgDC;QA/CC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,IAAI,CAAC,MAAM,IAAI,YAAY,EAAE;YAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBACxB,4BAA4B;gBAC5B,IAAM,cAAc,GAAG,UAAC,KAAK;oBAC3B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,eAAe;oBACtC,IAAI,KAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE;wBACvC,eAAe;wBACf,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;4BACzB,IAAI,CAAC,YAAY,EAAE,UAAC,GAAiB;gCACnC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,qCAAqC;gCAC5D,aAAa,CAAC,GAAG,CAAC,CAAC;4BACrB,CAAC,CAAC,CAAC;yBACJ;6BAAM;4BACL,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,4BAA4B;4BACnD,aAAa,CAAC,YAAY,CAAC,CAAC;yBAC7B;wBACD,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;wBAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;4BACjB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,4BAA4B;4BACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;yBACxB;qBACF;yBAAM;wBACL,qBAAqB;wBACrB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;qBACtB;gBACH,CAAC,CAAC;gBACF,iBAAiB;gBACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,cAAc,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;iBAC7F;qBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACxB,cAAc;oBACd,cAAc,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;wBACjE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;wBAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;qBACjC,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO;oBACP,cAAc,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;iBACtC;aACF;YACD,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,sCAAS,GAAjB,UAAkB,SAAS,EAAE,QAAQ;QACnC,IAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC3B,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC/C;aAAM,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;YACpC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACjD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACnC;IACH,CAAC;IAEO,qCAAQ,GAAhB,UAAiB,SAAS,EAAE,QAAQ;QAClC,IAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC3B,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAClD;aAAM,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;YACpC,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACpD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACpC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AA5PD,CAAgD,WAAW,GA4P1D", "sourcesContent": ["import { each, isArray, isFunction, isString, debounce, throttle } from '@antv/util';\nimport { View } from '../chart';\nimport { ActionCallback, IAction, IInteractionContext, LooseObject } from '../interface';\nimport { createAction, createCallbackAction } from './action/register';\nimport InteractionContext from './context';\nimport Interaction from './interaction';\n\n// 将字符串转换成 action\nexport function parseAction(actionStr: string, context: IInteractionContext, arg?: any): ActionObject {\n  const arr = actionStr.split(':');\n  const actionName = arr[0];\n  // 如果已经初始化过 action ，则直接引用之前的 action\n  const action = context.getAction(actionName) || createAction(actionName, context);\n  if (!action) {\n    throw new Error(`There is no action named ${actionName}`);\n  }\n  const methodName = arr[1];\n  return {\n    action,\n    methodName,\n    arg,\n  };\n}\n\n// 执行 Action\nfunction executeAction(actionObject: ActionObject) {\n  const { action, methodName, arg } = actionObject;\n  if (action[methodName]) {\n    action[methodName](arg);\n  } else {\n    throw new Error(`Action(${action.name}) doesn't have a method called ${methodName}`);\n  }\n}\n\nconst STEP_NAMES = {\n  START: 'start',\n  SHOW_ENABLE: 'showEnable',\n  END: 'end',\n  ROLLBACK: 'rollback',\n  PROCESSING: 'processing',\n};\n\n/** 交互环节的定义 */\nexport interface InteractionStep {\n  /**\n   * 触发事件，支持 view，chart 的各种事件，也支持 document、window 的事件\n   */\n  trigger: string;\n  /**\n   * 是否可以触发 action\n   * @param context - 交互的上下文\n   */\n  isEnable?: (context: IInteractionContext) => boolean;\n  /**\n   * 反馈，支持三种方式：\n   * - action:method : action 的名字和方法的组合\n   * - [’action1:method1‘, ’action2:method‘]\n   * - ActionCallback: 回调函数\n   */\n  action: string | string[] | ActionCallback;\n  /**\n   * 反馈，具体 action method 的参数：\n   * - 当传递多个 action 时，args 必须是一个数组\n   */\n  arg?: any | any[];\n  /**\n   * 回调函数，action 执行后执行\n   */\n  callback?: (context: IInteractionContext) => void;\n  /**\n   * @private\n   * 不需要用户传入，通过上面的属性计算出来的属性\n   */\n  actionObject?: ActionObject | ActionObject[];\n  /**\n   * 在一个环节内是否只允许执行一次\n   */\n  once?: boolean;\n  /**\n   * 是否增加节流\n   */\n  throttle?: ThrottleOption;\n  /**\n   * 是否延迟\n   */\n  debounce?: DebounceOption;\n}\n\n// action 执行时支持 debounce 和 throttle，可以参考：https://css-tricks.com/debouncing-throttling-explained-examples/\n/**\n * debounce 的配置\n */\nexport interface DebounceOption {\n  /**\n   * 等待时间\n   */\n  wait: number;\n  /**\n   * 是否马上执行\n   */\n  immediate?: boolean;\n}\n\n/**\n * throttle 的配置\n */\nexport interface ThrottleOption {\n  /**\n   * 等待时间\n   */\n  wait: number;\n  /**\n   * 马上就执行\n   */\n  leading?: boolean;\n  /**\n   * 执行完毕后再执行一次\n   */\n  trailing?: boolean;\n}\n\n/** 缓存 action 对象，仅用于当前文件 */\ninterface ActionObject {\n  /**\n   * 缓存的 action\n   */\n  action: IAction;\n  /**\n   * action 的方法\n   */\n  methodName: string;\n  /**\n   * 用户传递的 action 方法的参数\n   */\n  arg?: any;\n}\n\n/** 交互的所有环节 */\nexport interface InteractionSteps {\n  /**\n   * 显示交互可以进行\n   */\n  showEnable?: InteractionStep[];\n  /**\n   * 交互开始\n   */\n  start?: InteractionStep[];\n  /**\n   * 交互持续\n   */\n  processing?: InteractionStep[];\n  /**\n   * 交互结束\n   */\n  end?: InteractionStep[];\n  /**\n   * 交互回滚\n   */\n  rollback?: InteractionStep[];\n}\n\n/**\n * 支持语法的交互类\n */\nexport default class GrammarInteraction extends Interaction {\n  // 存储的交互环节\n  private steps: InteractionSteps;\n  /** 当前执行到的阶段 */\n  public currentStepName: string;\n  /**\n   * 当前交互的上下文\n   */\n  public context: IInteractionContext;\n\n  private callbackCaches: LooseObject = {};\n  // 某个触发和反馈在本环节是否执行或\n  private emitCaches: LooseObject = {};\n\n  constructor(view: View, steps: InteractionSteps) {\n    super(view, steps);\n    this.steps = steps;\n  }\n\n  /**\n   * 初始化\n   */\n  public init() {\n    this.initContext();\n    super.init();\n  }\n\n  /**\n   * 清理资源\n   */\n  public destroy() {\n    super.destroy(); // 先清理事件\n    this.steps = null;\n    if (this.context) {\n      this.context.destroy();\n      this.context = null;\n    }\n\n    this.callbackCaches = null;\n    this.view = null;\n  }\n\n  /**\n   * 绑定事件\n   */\n  protected initEvents() {\n    each(this.steps, (stepArr, stepName) => {\n      each(stepArr, (step) => {\n        const callback = this.getActionCallback(stepName, step);\n        if (callback) {\n          // 如果存在 callback，才绑定，有时候会出现无 callback 的情况\n          this.bindEvent(step.trigger, callback);\n        }\n      });\n    });\n  }\n\n  /**\n   * 清理绑定的事件\n   */\n  protected clearEvents() {\n    each(this.steps, (stepArr, stepName) => {\n      each(stepArr, (step) => {\n        const callback = this.getActionCallback(stepName, step);\n        if (callback) {\n          this.offEvent(step.trigger, callback);\n        }\n      });\n    });\n  }\n\n  // 初始化上下文，并初始化 action\n  private initContext() {\n    const view = this.view;\n    const context = new InteractionContext(view);\n    this.context = context;\n    const steps = this.steps;\n    // 生成具体的 Action\n    each(steps, (subSteps: InteractionStep[]) => {\n      each(subSteps, (step: InteractionStep) => {\n        if (isFunction(step.action)) {\n          // 如果传入回调函数，则直接生成 CallbackAction\n          step.actionObject = {\n            action: createCallbackAction(step.action, context),\n            methodName: 'execute',\n          };\n        } else if (isString(step.action)) {\n          // 如果是字符串\n          step.actionObject = parseAction(step.action, context, step.arg);\n        } else if (isArray(step.action)) {\n          // 如果是数组\n          const actionArr = step.action;\n          const argArr = isArray(step.arg) ? step.arg : [step.arg];\n          step.actionObject = [];\n          each(actionArr, (actionStr, idx) => {\n            (step.actionObject as ActionObject[]).push(parseAction(actionStr, context, argArr[idx]));\n          });\n        }\n        // 如果 action 既不是字符串，也不是函数，则不会生成 actionObject\n      });\n    });\n  }\n\n  // 是否允许指定阶段名称执行\n  private isAllowStep(stepName: string): boolean {\n    const currentStepName = this.currentStepName;\n    const steps = this.steps;\n    // 相同的阶段允许同时执行\n    if (currentStepName === stepName) {\n      return true;\n    }\n\n    if (stepName === STEP_NAMES.SHOW_ENABLE) {\n      // 示能在整个过程中都可用\n      return true;\n    }\n\n    if (stepName === STEP_NAMES.PROCESSING) {\n      // 只有当前是 start 时，才允许 processing\n      return currentStepName === STEP_NAMES.START;\n    }\n\n    if (stepName === STEP_NAMES.START) {\n      // 如果当前是 processing，则无法 start，必须等待 end 后才能执行\n      return currentStepName !== STEP_NAMES.PROCESSING;\n    }\n\n    if (stepName === STEP_NAMES.END) {\n      return currentStepName === STEP_NAMES.PROCESSING || currentStepName === STEP_NAMES.START;\n    }\n\n    if (stepName === STEP_NAMES.ROLLBACK) {\n      if (steps[STEP_NAMES.END]) {\n        // 如果定义了 end, 只有 end 时才允许回滚\n        return currentStepName === STEP_NAMES.END;\n      } else if (currentStepName === STEP_NAMES.START) {\n        // 如果未定义 end, 则判断是否是开始\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // 具体的指定阶段是否允许执行\n  private isAllowExecute(stepName: string, step: InteractionStep): boolean {\n    if (this.isAllowStep(stepName)) {\n      const key = this.getKey(stepName, step);\n      // 如果是在本环节内仅允许触发一次，同时已经触发过，则不允许再触发\n      if (step.once && this.emitCaches[key]) {\n        return false;\n      }\n      // 如果是允许的阶段，则验证 isEnable 方法\n      if (step.isEnable) {\n        return step.isEnable(this.context);\n      }\n      return true; // 如果没有 isEnable 则允许执行\n    }\n    return false;\n  }\n\n  private enterStep(stepName: string) {\n    this.currentStepName = stepName;\n    this.emitCaches = {}; // 清除所有本环节触发的缓存\n  }\n\n  // 执行完某个触发和反馈（子环节）\n  private afterExecute(stepName: string, step) {\n    // show enable 不计入正常的流程，其他情况则设置当前的 step\n    if (stepName !== STEP_NAMES.SHOW_ENABLE && this.currentStepName !== stepName) {\n      this.enterStep(stepName);\n    }\n    const key = this.getKey(stepName, step);\n    // 一旦执行，则缓存标记为，一直保持到跳出改环节\n    this.emitCaches[key] = true;\n  }\n  // 获取某个环节的唯一的键值\n  private getKey(stepName, step) {\n    return stepName + step.trigger + step.action;\n  }\n\n  // 获取 step 的回调函数，如果已经生成，则直接返回，如果未生成，则创建\n  private getActionCallback(stepName: string, step: InteractionStep): (e: object) => void {\n    const context = this.context;\n    const callbackCaches = this.callbackCaches;\n    const actionObject = step.actionObject;\n    if (step.action && actionObject) {\n      const key = this.getKey(stepName, step);\n      if (!callbackCaches[key]) {\n        // 动态生成执行的方法，执行对应 action 的名称\n        const actionCallback = (event) => {\n          context.event = event; // 保证检测时的 event\n          if (this.isAllowExecute(stepName, step)) {\n            // 如果是数组时，则依次执行\n            if (isArray(actionObject)) {\n              each(actionObject, (obj: ActionObject) => {\n                context.event = event; // 可能触发新的事件，保证执行前的 context.event 是正确的\n                executeAction(obj);\n              });\n            } else {\n              context.event = event; // 保证执行前的 context.event 是正确的\n              executeAction(actionObject);\n            }\n            this.afterExecute(stepName, step);\n            if (step.callback) {\n              context.event = event; // 保证执行前的 context.event 是正确的\n              step.callback(context);\n            }\n          } else {\n            // 如果未通过验证，则事件不要绑定在上面\n            context.event = null;\n          }\n        };\n        // 如果设置了 debounce\n        if (step.debounce) {\n          callbackCaches[key] = debounce(actionCallback, step.debounce.wait, step.debounce.immediate);\n        } else if (step.throttle) {\n          // 设置 throttle\n          callbackCaches[key] = throttle(actionCallback, step.throttle.wait, {\n            leading: step.throttle.leading,\n            trailing: step.throttle.trailing,\n          });\n        } else {\n          // 直接设置\n          callbackCaches[key] = actionCallback;\n        }\n      }\n      return callbackCaches[key];\n    }\n    return null;\n  }\n\n  private bindEvent(eventName, callback) {\n    const nameArr = eventName.split(':');\n    if (nameArr[0] === 'window') {\n      window.addEventListener(nameArr[1], callback);\n    } else if (nameArr[0] === 'document') {\n      document.addEventListener(nameArr[1], callback);\n    } else {\n      this.view.on(eventName, callback);\n    }\n  }\n\n  private offEvent(eventName, callback) {\n    const nameArr = eventName.split(':');\n    if (nameArr[0] === 'window') {\n      window.removeEventListener(nameArr[1], callback);\n    } else if (nameArr[0] === 'document') {\n      document.removeEventListener(nameArr[1], callback);\n    } else {\n      this.view.off(eventName, callback);\n    }\n  }\n}\n"]}