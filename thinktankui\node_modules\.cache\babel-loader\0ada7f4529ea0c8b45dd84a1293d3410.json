{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\monitor\\operlog.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\monitor\\operlog.js", "mtime": 1749109381294}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmNsZWFuT3BlcmxvZyA9IGNsZWFuT3BlcmxvZzsKZXhwb3J0cy5kZWxPcGVybG9nID0gZGVsT3BlcmxvZzsKZXhwb3J0cy5saXN0ID0gbGlzdDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouaTjeS9nOaXpeW/l+WIl+ihqApmdW5jdGlvbiBsaXN0KHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbW9uaXRvci9vcGVybG9nL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5Yig6Zmk5pON5L2c5pel5b+XCmZ1bmN0aW9uIGRlbE9wZXJsb2cob3BlcklkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbW9uaXRvci9vcGVybG9nLycgKyBvcGVySWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOa4heepuuaTjeS9nOaXpeW/lwpmdW5jdGlvbiBjbGVhbk9wZXJsb2coKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbW9uaXRvci9vcGVybG9nL2NsZWFuJywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "query", "request", "url", "method", "params", "delOperlog", "operId", "cleanOperlog"], "sources": ["D:/thinktank/thinktankui/src/api/monitor/operlog.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询操作日志列表\r\nexport function list(query) {\r\n  return request({\r\n    url: '/monitor/operlog/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除操作日志\r\nexport function delOperlog(operId) {\r\n  return request({\r\n    url: '/monitor/operlog/' + operId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清空操作日志\r\nexport function cleanOperlog() {\r\n  return request({\r\n    url: '/monitor/operlog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}