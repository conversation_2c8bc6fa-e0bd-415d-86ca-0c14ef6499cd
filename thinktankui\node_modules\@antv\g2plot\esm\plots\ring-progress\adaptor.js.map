{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/ring-progress/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAE3E,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAChE,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAG/C;;;GAGG;AACH,SAAS,UAAU,CAAC,MAAmC;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAAa,OAAO,YAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAExC,aAAa;IACb,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;QACxB,WAAW,aAAA;QACX,MAAM,QAAA;KACP,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,MAAmC,EAAE,OAAiB;IACtE,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAA+B,OAAO,YAAtC,EAAE,SAAS,GAAoB,OAAO,UAA3B,EAAE,OAAO,GAAW,OAAO,QAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAE1D,cAAc;IACd,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9C,eAAe;IACf,IAAI,WAAW,IAAI,SAAS,EAAE;QAC5B,IAAM,aAAa,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,UAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG,EAA1B,CAA0B,CAAC,CAAC;QACjG,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC;QACnC,IAAI,UAAU,EAAE;YACd,UAAU,GAAG,UAAU,CAAC,EAAE,EAAE,UAAU,EAAE;gBACtC,OAAO,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;aAClF,CAAC,CAAC;SACJ;QACD,eAAe,CACb,KAAK,EACL,EAAE,SAAS,wBAAO,SAAS,KAAE,OAAO,EAAE,UAAU,GAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,EAC/E,EAAE,OAAO,SAAA,EAAE,CACZ,CAAC;KACH;IAED,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAmC;IACzD,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AAClG,CAAC", "sourcesContent": ["import { get, isNil } from '@antv/util';\nimport { animation, annotation, scale, theme } from '../../adaptor/common';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow, renderStatistic } from '../../utils';\nimport { geometry } from '../progress/adaptor';\nimport { RingProgressOptions } from './types';\n\n/**\n * coordinate 配置\n * @param params\n */\nfunction coordinate(params: Params<RingProgressOptions>): Params<RingProgressOptions> {\n  const { chart, options } = params;\n  const { innerRadius, radius } = options;\n\n  // coordinate\n  chart.coordinate('theta', {\n    innerRadius,\n    radius,\n  });\n\n  return params;\n}\n\n/**\n * statistic 配置\n * @param params\n */\nexport function statistic(params: Params<RingProgressOptions>, updated?: boolean): Params<RingProgressOptions> {\n  const { chart, options } = params;\n  const { innerRadius, statistic, percent, meta } = options;\n\n  // 先清空标注，再重新渲染\n  chart.getController('annotation').clear(true);\n\n  /** 中心文本 指标卡 */\n  if (innerRadius && statistic) {\n    const metaFormatter = get(meta, ['percent', 'formatter']) || ((v) => `${(v * 100).toFixed(2)}%`);\n    let contentOpt = statistic.content;\n    if (contentOpt) {\n      contentOpt = deepAssign({}, contentOpt, {\n        content: !isNil(contentOpt.content) ? contentOpt.content : metaFormatter(percent),\n      });\n    }\n    renderStatistic(\n      chart,\n      { statistic: { ...statistic, content: contentOpt }, plotType: 'ring-progress' },\n      { percent }\n    );\n  }\n\n  if (updated) {\n    chart.render(true);\n  }\n\n  return params;\n}\n\n/**\n * 环形进度图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<RingProgressOptions>) {\n  return flow(geometry, scale({}), coordinate, statistic, animation, theme, annotation())(params);\n}\n"]}