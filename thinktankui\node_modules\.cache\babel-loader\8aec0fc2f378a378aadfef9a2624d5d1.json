{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\store\\modules\\app.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\store\\modules\\app.js", "mtime": 1749109381335}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_js<PERSON><PERSON>ie", "_interopRequireDefault", "require", "state", "sidebar", "opened", "Cookies", "get", "withoutAnimation", "hide", "device", "size", "mutations", "TOGGLE_SIDEBAR", "set", "CLOSE_SIDEBAR", "TOGGLE_DEVICE", "SET_SIZE", "SET_SIDEBAR_HIDE", "status", "actions", "toggleSideBar", "_ref", "commit", "closeSideBar", "_ref2", "_ref3", "toggleDevice", "_ref4", "setSize", "_ref5", "toggleSideBarHide", "_ref6", "_default", "exports", "default", "namespaced"], "sources": ["D:/thinktank/thinktankui/src/store/modules/app.js"], "sourcesContent": ["import Cookies from 'js-cookie'\r\n\r\nconst state = {\r\n  sidebar: {\r\n    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,\r\n    withoutAnimation: false,\r\n    hide: false\r\n  },\r\n  device: 'desktop',\r\n  size: Cookies.get('size') || 'medium'\r\n}\r\n\r\nconst mutations = {\r\n  TOGGLE_SIDEBAR: state => {\r\n    if (state.sidebar.hide) {\r\n      return false;\r\n    }\r\n    state.sidebar.opened = !state.sidebar.opened\r\n    state.sidebar.withoutAnimation = false\r\n    if (state.sidebar.opened) {\r\n      Cookies.set('sidebarStatus', 1)\r\n    } else {\r\n      Cookies.set('sidebarStatus', 0)\r\n    }\r\n  },\r\n  CLOSE_SIDEBAR: (state, withoutAnimation) => {\r\n    Cookies.set('sidebarStatus', 0)\r\n    state.sidebar.opened = false\r\n    state.sidebar.withoutAnimation = withoutAnimation\r\n  },\r\n  TOGGLE_DEVICE: (state, device) => {\r\n    state.device = device\r\n  },\r\n  SET_SIZE: (state, size) => {\r\n    state.size = size\r\n    Cookies.set('size', size)\r\n  },\r\n  SET_SIDEBAR_HIDE: (state, status) => {\r\n    state.sidebar.hide = status\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  toggleSideBar({ commit }) {\r\n    commit('TOGGLE_SIDEBAR')\r\n  },\r\n  closeSideBar({ commit }, { withoutAnimation }) {\r\n    commit('CLOSE_SIDEBAR', withoutAnimation)\r\n  },\r\n  toggleDevice({ commit }, device) {\r\n    commit('TOGGLE_DEVICE', device)\r\n  },\r\n  setSize({ commit }, size) {\r\n    commit('SET_SIZE', size)\r\n  },\r\n  toggleSideBarHide({ commit }, status) {\r\n    commit('SET_SIDEBAR_HIDE', status)\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAMC,KAAK,GAAG;EACZC,OAAO,EAAE;IACPC,MAAM,EAAEC,iBAAO,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAACD,iBAAO,CAACC,GAAG,CAAC,eAAe,CAAC,GAAG,IAAI;IAC7EC,gBAAgB,EAAE,KAAK;IACvBC,IAAI,EAAE;EACR,CAAC;EACDC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAEL,iBAAO,CAACC,GAAG,CAAC,MAAM,CAAC,IAAI;AAC/B,CAAC;AAED,IAAMK,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAEV,KAAK,EAAI;IACvB,IAAIA,KAAK,CAACC,OAAO,CAACK,IAAI,EAAE;MACtB,OAAO,KAAK;IACd;IACAN,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAACF,KAAK,CAACC,OAAO,CAACC,MAAM;IAC5CF,KAAK,CAACC,OAAO,CAACI,gBAAgB,GAAG,KAAK;IACtC,IAAIL,KAAK,CAACC,OAAO,CAACC,MAAM,EAAE;MACxBC,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACLR,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IACjC;EACF,CAAC;EACDC,aAAa,EAAE,SAAfA,aAAaA,CAAGZ,KAAK,EAAEK,gBAAgB,EAAK;IAC1CF,iBAAO,CAACQ,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;IAC/BX,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,KAAK;IAC5BF,KAAK,CAACC,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;EACnD,CAAC;EACDQ,aAAa,EAAE,SAAfA,aAAaA,CAAGb,KAAK,EAAEO,MAAM,EAAK;IAChCP,KAAK,CAACO,MAAM,GAAGA,MAAM;EACvB,CAAC;EACDO,QAAQ,EAAE,SAAVA,QAAQA,CAAGd,KAAK,EAAEQ,IAAI,EAAK;IACzBR,KAAK,CAACQ,IAAI,GAAGA,IAAI;IACjBL,iBAAO,CAACQ,GAAG,CAAC,MAAM,EAAEH,IAAI,CAAC;EAC3B,CAAC;EACDO,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAGf,KAAK,EAAEgB,MAAM,EAAK;IACnChB,KAAK,CAACC,OAAO,CAACK,IAAI,GAAGU,MAAM;EAC7B;AACF,CAAC;AAED,IAAMC,OAAO,GAAG;EACdC,aAAa,WAAbA,aAAaA,CAAAC,IAAA,EAAa;IAAA,IAAVC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACpBA,MAAM,CAAC,gBAAgB,CAAC;EAC1B,CAAC;EACDC,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAAC,KAAA,EAAmC;IAAA,IAAhCH,MAAM,GAAAE,KAAA,CAANF,MAAM;IAAA,IAAMf,gBAAgB,GAAAkB,KAAA,CAAhBlB,gBAAgB;IACzCe,MAAM,CAAC,eAAe,EAAEf,gBAAgB,CAAC;EAC3C,CAAC;EACDmB,YAAY,WAAZA,YAAYA,CAAAC,KAAA,EAAalB,MAAM,EAAE;IAAA,IAAlBa,MAAM,GAAAK,KAAA,CAANL,MAAM;IACnBA,MAAM,CAAC,eAAe,EAAEb,MAAM,CAAC;EACjC,CAAC;EACDmB,OAAO,WAAPA,OAAOA,CAAAC,KAAA,EAAanB,IAAI,EAAE;IAAA,IAAhBY,MAAM,GAAAO,KAAA,CAANP,MAAM;IACdA,MAAM,CAAC,UAAU,EAAEZ,IAAI,CAAC;EAC1B,CAAC;EACDoB,iBAAiB,WAAjBA,iBAAiBA,CAAAC,KAAA,EAAab,MAAM,EAAE;IAAA,IAAlBI,MAAM,GAAAS,KAAA,CAANT,MAAM;IACxBA,MAAM,CAAC,kBAAkB,EAAEJ,MAAM,CAAC;EACpC;AACF,CAAC;AAAA,IAAAc,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACbC,UAAU,EAAE,IAAI;EAChBjC,KAAK,EAALA,KAAK;EACLS,SAAS,EAATA,SAAS;EACTQ,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}