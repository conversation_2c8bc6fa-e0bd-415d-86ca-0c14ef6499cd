{"version": 3, "file": "filter.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/data/filter.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAElC,OAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,OAAO,EAAE,mBAAmB,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEjF;;;GAGG;AACH;IAAyB,8BAAM;IAA/B;;IA+CA,CAAC;IA9CS,+BAAU,GAAlB,UAAmB,IAAU,EAAE,KAAK,EAAE,MAAM;QAA5C,iBAUC;QATC,oBAAoB;QACpB,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SAC5B;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,OAAO;gBACvB,KAAI,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IACD;;OAEG;IACI,2BAAM,GAAb;QACE,IAAM,cAAc,GAAG,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzD,IAAI,cAAc,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACvB,IAAA,SAAS,GAAK,cAAc,UAAnB,CAAoB;YACrC,IAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrC,aAAa;YACb,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE;gBAC1B,IAAI,KAAK,EAAE;oBACT,IAAM,cAAc,GAAG,SAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAC9D,IAAM,OAAK,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAC3C,IAAM,OAAK,GAAa,cAAc,CAAC,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,IAAI,EAAT,CAAS,CAAC,CAAC;oBAChE,IAAI,OAAK,CAAC,MAAM,EAAE;wBAChB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,UAAC,KAAK;4BACjC,IAAM,IAAI,GAAG,OAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;4BAClC,OAAO,CAAC,OAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC/B,CAAC,CAAC,CAAC;qBACJ;yBAAM;wBACL,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;qBACpC;oBACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBACnB;aACF;iBAAM,IAAI,QAAQ,CAAC,cAAc,CAAC,EAAE;gBACnC,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC7B,IAAA,KAAA,OAAa,KAAK,IAAA,EAAjB,KAAG,QAAA,EAAE,KAAG,QAAS,CAAC;gBACzB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,UAAC,KAAK;oBACjC,OAAO,KAAK,IAAI,KAAG,IAAI,KAAK,IAAI,KAAG,CAAC;gBACtC,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACnB;SACF;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AA/CD,CAAyB,MAAM,GA+C9B;AAED,eAAe,UAAU,CAAC", "sourcesContent": ["import { each } from '@antv/util';\nimport { View } from 'src/chart';\nimport Action from '../base';\nimport { getDelegationObject, getScaleByField, isList, isSlider } from '../util';\n\n/**\n * 数据过滤。\n * @ignore\n */\nclass DataFilter extends Action {\n  private filterView(view: View, field, filter) {\n    // 只有存在这个 scale 时才生效\n    if (view.getScaleByField(field)) {\n      view.filter(field, filter);\n    }\n    if (view.views && view.views.length) {\n      each(view.views, (subView) => {\n        this.filterView(subView, field, filter);\n      });\n    }\n  }\n  /**\n   * 过滤数据\n   */\n  public filter() {\n    const delegateObject = getDelegationObject(this.context);\n    if (delegateObject) {\n      const view = this.context.view;\n      const { component } = delegateObject;\n      const field = component.get('field');\n      // 列表类的组件能够触发\n      if (isList(delegateObject)) {\n        if (field) {\n          const unCheckedItems = component.getItemsByState('unchecked');\n          const scale = getScaleByField(view, field);\n          const names: string[] = unCheckedItems.map((item) => item.name);\n          if (names.length) {\n            this.filterView(view, field, (value) => {\n              const text = scale.getText(value);\n              return !names.includes(text);\n            });\n          } else {\n            this.filterView(view, field, null);\n          }\n          view.render(true);\n        }\n      } else if (isSlider(delegateObject)) {\n        const range = component.getValue();\n        const [min, max] = range;\n        this.filterView(view, field, (value) => {\n          return value >= min && value <= max;\n        });\n        view.render(true);\n      }\n    }\n  }\n}\n\nexport default DataFilter;\n"]}