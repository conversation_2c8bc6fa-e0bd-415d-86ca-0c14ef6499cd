{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/progress/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC3E,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAE5C,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,MAA+B;IAC9C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAA0C,OAAO,QAAjD,EAAE,aAAa,GAA2B,OAAO,cAAlC,EAAE,KAAK,GAAoB,OAAO,MAA3B,EAAE,aAAa,GAAK,OAAO,cAAZ,CAAa;IAEjE,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;IAErC,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,SAAS;YACjB,WAAW,EAAE,MAAM;YACnB,UAAU,EAAE,aAAa;YACzB,QAAQ,EAAE;gBACR,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;aAC3D;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,IAAI;aACjB;SACF;KACF,CAAC,CAAC;IAEH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEZ,OAAO;IACP,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACrB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA+B;IACzC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,CAAC;IAErC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,aAAa;IACb,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACvF,CAAC", "sourcesContent": ["import { isString } from '@antv/util';\nimport { animation, annotation, scale, theme } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { DEFAULT_COLOR } from './constants';\nimport { ProgressOptions } from './types';\nimport { getProgressData } from './utils';\n\n/**\n * 字段\n * @param params\n */\nexport function geometry(params: Params<ProgressOptions>): Params<ProgressOptions> {\n  const { chart, options } = params;\n  const { percent, progressStyle, color, barWidthRatio } = options;\n\n  chart.data(getProgressData(percent));\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: 'current',\n      yField: 'percent',\n      seriesField: 'type',\n      widthRatio: barWidthRatio,\n      interval: {\n        style: progressStyle,\n        color: isString(color) ? [color, DEFAULT_COLOR[1]] : color,\n      },\n      args: {\n        zIndexReversed: true,\n        sortZIndex: true,\n      },\n    },\n  });\n\n  interval(p);\n\n  // 关闭组件\n  chart.tooltip(false);\n  chart.axis(false);\n  chart.legend(false);\n\n  return params;\n}\n\n/**\n * other 配置\n * @param params\n */\nfunction coordinate(params: Params<ProgressOptions>): Params<ProgressOptions> {\n  const { chart } = params;\n\n  chart.coordinate('rect').transpose();\n\n  return params;\n}\n\n/**\n * 进度图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<ProgressOptions>) {\n  // @ts-ignore\n  return flow(geometry, scale({}), coordinate, animation, theme, annotation())(params);\n}\n"]}