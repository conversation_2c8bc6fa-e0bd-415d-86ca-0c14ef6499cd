{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\ui\\color-picker.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\ui\\color-picker.js", "mtime": 1749109532545}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_picker", "_interopRequireDefault", "require", "ColorPicker", "_Picker", "select", "label", "_this", "_classCallCheck2", "default", "_callSuper2", "innerHTML", "container", "classList", "add", "Array", "from", "querySelectorAll", "slice", "for<PERSON>ach", "item", "_inherits2", "_createClass2", "key", "value", "buildItem", "option", "_superPropGet2", "style", "backgroundColor", "getAttribute", "selectItem", "trigger", "colorLabel", "querySelector", "tagName", "stroke", "fill", "Picker", "_default", "exports"], "sources": ["../../src/ui/color-picker.ts"], "sourcesContent": ["import Picker from './picker.js';\n\nclass ColorPicker extends Picker {\n  constructor(select: HTMLSelectElement, label: string) {\n    super(select);\n    this.label.innerHTML = label;\n    this.container.classList.add('ql-color-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item'))\n      .slice(0, 7)\n      .forEach((item) => {\n        item.classList.add('ql-primary');\n      });\n  }\n\n  buildItem(option: HTMLOptionElement) {\n    const item = super.buildItem(option);\n    item.style.backgroundColor = option.getAttribute('value') || '';\n    return item;\n  }\n\n  selectItem(item: HTMLElement | null, trigger?: boolean) {\n    super.selectItem(item, trigger);\n    const colorLabel = this.label.querySelector<HTMLElement>('.ql-color-label');\n    const value = item ? item.getAttribute('data-value') || '' : '';\n    if (colorLabel) {\n      if (colorLabel.tagName === 'line') {\n        colorLabel.style.stroke = value;\n      } else {\n        colorLabel.style.fill = value;\n      }\n    }\n  }\n}\n\nexport default ColorPicker;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAgC,IAE1BC,WAAW,0BAAAC,OAAA;EACf,SAAAD,YAAYE,MAAyB,EAAEC,KAAa,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAN,WAAA;IACpDI,KAAA,OAAAG,WAAA,CAAAD,OAAA,QAAAN,WAAA,GAAME,MAAM;IACZE,KAAA,CAAKD,KAAK,CAACK,SAAS,GAAGL,KAAK;IAC5BC,KAAA,CAAKK,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC/CC,KAAK,CAACC,IAAI,CAACT,KAAA,CAAKK,SAAS,CAACK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAC3DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXC,OAAO,CAAE,UAAAC,IAAI,EAAK;MACjBA,IAAI,CAACP,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAClC,CAAC,CAAC;IAAA,OAAAP,KAAA;EACN;EAAA,IAAAc,UAAA,CAAAZ,OAAA,EAAAN,WAAA,EAAAC,OAAA;EAAA,WAAAkB,aAAA,CAAAb,OAAA,EAAAN,WAAA;IAAAoB,GAAA;IAAAC,KAAA,EAEA,SAAAC,SAASA,CAACC,MAAyB,EAAE;MACnC,IAAMN,IAAI,OAAAO,cAAA,CAAAlB,OAAA,EAAAN,WAAA,yBAAmBuB,MAAM,EAAC;MACpCN,IAAI,CAACQ,KAAK,CAACC,eAAe,GAAGH,MAAM,CAACI,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MAC/D,OAAOV,IAAI;IACb;EAAA;IAAAG,GAAA;IAAAC,KAAA,EAEA,SAAAO,UAAUA,CAACX,IAAwB,EAAEY,OAAiB,EAAE;MACtD,IAAAL,cAAA,CAAAlB,OAAA,EAAAN,WAAA,0BAAiBiB,IAAI,EAAEY,OAAO;MAC9B,IAAMC,UAAU,GAAG,IAAI,CAAC3B,KAAK,CAAC4B,aAAa,CAAc,iBAAiB,CAAC;MAC3E,IAAMV,KAAK,GAAGJ,IAAI,GAAGA,IAAI,CAACU,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;MAC/D,IAAIG,UAAU,EAAE;QACd,IAAIA,UAAU,CAACE,OAAO,KAAK,MAAM,EAAE;UACjCF,UAAU,CAACL,KAAK,CAACQ,MAAM,GAAGZ,KAAK;QACjC,CAAC,MAAM;UACLS,UAAU,CAACL,KAAK,CAACS,IAAI,GAAGb,KAAK;QAC/B;MACF;IACF;EAAA;AAAA,EA7BwBc,eAAM;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAA/B,OAAA,GAgCjBN,WAAW", "ignoreList": []}]}