{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\opinion-overview\\index.vue", "mtime": 1749109381349}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ09waW5pb25PdmVydmlldycsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFjdGl2ZU1lbnVJdGVtOiAn5pa55aSqJywNCiAgICAgIHNlYXJjaFRleHQ6ICcnLA0KICAgICAgYWN0aXZlVGFiOiAnb3Bpbmlvbi1tb25pdG9yJywgLy8g6buY6K6k5r+A5rS76IiG5oOF55uR5rWL5qCH562+DQogICAgICBvcmlnaW5hbFRvcE5hdjogdW5kZWZpbmVkLCAvLyDlrZjlgqjljp/lp4vnmoR0b3BOYXbnirbmgIENCiAgICAgIC8vIOWbvuihqOWunuS+iw0KICAgICAgbWFwQ2hhcnQ6IG51bGwsDQogICAgICBwbGF0Zm9ybUNoYXJ0OiBudWxsLA0KICAgICAgc2VudGltZW50Q2hhcnQ6IG51bGwsDQogICAgICB0cmVuZENoYXJ0OiBudWxsLCAvLyDotovlir/lm77ooajlrp7kvosNCiAgICAgIC8vIOeDremXqOaWh+eroOaVsOaNrg0KICAgICAgaG90QXJ0aWNsZXM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGljb246ICfnuqInLA0KICAgICAgICAgIHR5cGU6ICduZWdhdGl2ZScsDQogICAgICAgICAgdGl0bGU6ICfmn5Dlk4HniYzkuqflk4HotKjph4/pl67popjlvJXlj5HmtojotLnogIXkuI3mu6EnLA0KICAgICAgICAgIHNvdXJjZTogJ+aWsOa1qui0oue7jycsDQogICAgICAgICAgYXV0aG9yOiAn6LSi57uP6K6w6ICFJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWNvbjogJ+m7hCcsDQogICAgICAgICAgdHlwZTogJ25ldXRyYWwnLA0KICAgICAgICAgIHRpdGxlOiAn5biC5Zy65YiG5p6Q77ya5a6255S16KGM5Lia5Y+R5bGV6LaL5Yq/JywNCiAgICAgICAgICBzb3VyY2U6ICfkuK3lm73nu4/mtY7nvZEnLA0KICAgICAgICAgIGF1dGhvcjogJ+W4guWcuuWIhuaekOW4iCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGljb246ICfnu78nLA0KICAgICAgICAgIHR5cGU6ICdwb3NpdGl2ZScsDQogICAgICAgICAgdGl0bGU6ICfliJvmlrDmioDmnK/mjqjliqjooYzkuJrlj5HlsZUnLA0KICAgICAgICAgIHNvdXJjZTogJ+enkeaKgOaXpeaKpScsDQogICAgICAgICAgYXV0aG9yOiAn56eR5oqA6K6w6ICFJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWNvbjogJ+e6oicsDQogICAgICAgICAgdHlwZTogJ25lZ2F0aXZlJywNCiAgICAgICAgICB0aXRsZTogJ+a2iOi0ueiAheaKleivieWkhOeQhuS4jeW9k+W8leWPkeWFs+azqCcsDQogICAgICAgICAgc291cmNlOiAn5raI6LS56ICF5oqlJywNCiAgICAgICAgICBhdXRob3I6ICfmtojotLnnu7TmnYMnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBpY29uOiAn57u/JywNCiAgICAgICAgICB0eXBlOiAncG9zaXRpdmUnLA0KICAgICAgICAgIHRpdGxlOiAn5LyB5Lia56S+5Lya6LSj5Lu76I635b6X6K6k5Y+vJywNCiAgICAgICAgICBzb3VyY2U6ICfkurrmsJHml6XmiqUnLA0KICAgICAgICAgIGF1dGhvcjogJ+ekvuS8muiusOiAhScNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOacgOaWsOWFrOWRiuaVsOaNrg0KICAgICAgYW5ub3VuY2VtZW50czogWw0KICAgICAgICB7DQogICAgICAgICAgbGV2ZWw6ICdoaWdoJywNCiAgICAgICAgICB0aXRsZTogJ+iIhuaDheebkea1i+ezu+e7n+WNh+e6p+mAmuefpScsDQogICAgICAgICAgdGltZTogJzIwMjMtMDQtMjAgMTA6MzA6MDAnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsZXZlbDogJ21lZGl1bScsDQogICAgICAgICAgdGl0bGU6ICfkupTkuIDlgYfmnJ/nm5HmtYvlronmjpInLA0KICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTE5IDE2OjQ1OjAwJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGV2ZWw6ICdsb3cnLA0KICAgICAgICAgIHRpdGxlOiAn5pWw5o2u57uf6K6h5oql5ZGK5bey55Sf5oiQJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0xOSAxNDoyMDowMCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxldmVsOiAnaGlnaCcsDQogICAgICAgICAgdGl0bGU6ICfph43opoHoiIbmg4XpooTorabmj5DphpInLA0KICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTE5IDA5OjE1OjAwJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGV2ZWw6ICdtZWRpdW0nLA0KICAgICAgICAgIHRpdGxlOiAn57O757uf57u05oqk5a6M5oiQ6YCa55+lJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0xOCAxODozMDowMCcNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOWcsOWbvuaVsOaNrg0KICAgICAgbWFwRGF0YTogWw0KICAgICAgICB7bmFtZTogJ+WMl+S6rCcsIHZhbHVlOiAxNTAwMH0sDQogICAgICAgIHtuYW1lOiAn5LiK5rW3JywgdmFsdWU6IDEyMDAwfSwNCiAgICAgICAge25hbWU6ICflub/kuJwnLCB2YWx1ZTogMTgwMDB9LA0KICAgICAgICB7bmFtZTogJ+a1meaxnycsIHZhbHVlOiA4MDAwfSwNCiAgICAgICAge25hbWU6ICfmsZ/oi48nLCB2YWx1ZTogOTAwMH0sDQogICAgICAgIHtuYW1lOiAn5bGx5LicJywgdmFsdWU6IDcwMDB9LA0KICAgICAgICB7bmFtZTogJ+Wbm+W3nScsIHZhbHVlOiA2MDAwfSwNCiAgICAgICAge25hbWU6ICfmuZbljJcnLCB2YWx1ZTogNTAwMH0NCiAgICAgIF0sDQogICAgICAvLyDlubPlj7DmlbDmja4NCiAgICAgIHBsYXRmb3JtRGF0YTogWw0KICAgICAgICB7bmFtZTogJ+W+ruWNmicsIHZhbHVlOiAzNS4yLCBjb2xvcjogJyNmZjZiNmInfSwNCiAgICAgICAge25hbWU6ICflvq7kv6EnLCB2YWx1ZTogMjguNiwgY29sb3I6ICcjNTFjZjY2J30sDQogICAgICAgIHtuYW1lOiAn5oqW6Z+zJywgdmFsdWU6IDE4LjQsIGNvbG9yOiAnIzMzOWFmMCd9LA0KICAgICAgICB7bmFtZTogJ+S7iuaXpeWktOadoScsIHZhbHVlOiAxMi44LCBjb2xvcjogJyNmZmQ0M2InfSwNCiAgICAgICAge25hbWU6ICflhbbku5YnLCB2YWx1ZTogNS4wLCBjb2xvcjogJyM4NjhlOTYnfQ0KICAgICAgXSwNCiAgICAgIC8vIOaDheaEn+aVsOaNrg0KICAgICAgc2VudGltZW50RGF0YTogWw0KICAgICAgICB7bmFtZTogJ+ato+mdoicsIHZhbHVlOiA2NS4yLCBjb2xvcjogJyM1MmM0MWEnfSwNCiAgICAgICAge25hbWU6ICfkuK3mgKcnLCB2YWx1ZTogMjguMywgY29sb3I6ICcjZmFhZDE0J30sDQogICAgICAgIHtuYW1lOiAn6LSf6Z2iJywgdmFsdWU6IDYuNSwgY29sb3I6ICcjZmY0ZDRmJ30NCiAgICAgIF0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgLy8g6ZqQ6JeP6aG26YOo5a+86Iiq5qCPDQogICAgdGhpcy5vcmlnaW5hbFRvcE5hdiA9IHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRvcE5hdg0KICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgew0KICAgICAga2V5OiAndG9wTmF2JywNCiAgICAgIHZhbHVlOiBmYWxzZQ0KICAgIH0pDQoNCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAvLyDmoLnmja7lvZPliY3mv4DmtLvnmoTmoIfnrb7liJ3lp4vljJblr7nlupTnmoTlm77ooagNCiAgICAgIGlmICh0aGlzLmFjdGl2ZVRhYiA9PT0gJ29waW5pb24tbW9uaXRvcicpIHsNCiAgICAgICAgdGhpcy5pbml0VHJlbmRDaGFydCgpDQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAnaW5mby1zdW1tYXJ5Jykgew0KICAgICAgICB0aGlzLmluaXRDaGFydHMoKQ0KICAgICAgfQ0KICAgIH0pDQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgLy8g5oGi5aSN6aG26YOo5a+86Iiq5qCP6K6+572uDQogICAgaWYgKHRoaXMub3JpZ2luYWxUb3BOYXYgIT09IHVuZGVmaW5lZCkgew0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7DQogICAgICAgIGtleTogJ3RvcE5hdicsDQogICAgICAgIHZhbHVlOiB0aGlzLm9yaWdpbmFsVG9wTmF2DQogICAgICB9KQ0KICAgIH0NCg0KICAgIC8vIOmUgOavgeWbvuihqOWunuS+iw0KICAgIGlmICh0aGlzLm1hcENoYXJ0KSB7DQogICAgICB0aGlzLm1hcENoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0NCiAgICBpZiAodGhpcy5wbGF0Zm9ybUNoYXJ0KSB7DQogICAgICB0aGlzLnBsYXRmb3JtQ2hhcnQuZGlzcG9zZSgpDQogICAgfQ0KICAgIGlmICh0aGlzLnNlbnRpbWVudENoYXJ0KSB7DQogICAgICB0aGlzLnNlbnRpbWVudENoYXJ0LmRpc3Bvc2UoKQ0KICAgIH0NCiAgICBpZiAodGhpcy50cmVuZENoYXJ0KSB7DQogICAgICB0aGlzLnRyZW5kQ2hhcnQuZGlzcG9zZSgpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlTWVudVNlbGVjdChpbmRleCkgew0KICAgICAgdGhpcy5hY3RpdmVNZW51SXRlbSA9IGluZGV4DQogICAgICBjb25zb2xlLmxvZygnTWVudSBzZWxlY3RlZDonLCBpbmRleCkNCiAgICAgIC8vIOi/memHjOWPr+S7peagueaNrumAieaLqeeahOaWueahiOWKoOi9veS4jeWQjOeahOaVsOaNrg0KICAgIH0sDQogICAgaGFuZGxlVGFiQ2xpY2sodGFiKSB7DQogICAgICBjb25zb2xlLmxvZygnVGFiIGNsaWNrZWQ6JywgdGFiLm5hbWUpDQogICAgICAvLyDmoLnmja7kuI3lkIzmoIfnrb7liqDovb3kuI3lkIzlhoXlrrkNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRhYi5uYW1lID09PSAnb3Bpbmlvbi1tb25pdG9yJykgew0KICAgICAgICAgIHRoaXMuaW5pdFRyZW5kQ2hhcnQoKQ0KICAgICAgICB9IGVsc2UgaWYgKHRhYi5uYW1lID09PSAnaW5mby1zdW1tYXJ5Jykgew0KICAgICAgICAgIHRoaXMuaW5pdENoYXJ0cygpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBpbml0Q2hhcnRzKCkgew0KICAgICAgLy8g5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLmluaXRNYXAoKQ0KICAgICAgdGhpcy5pbml0UGxhdGZvcm1DaGFydCgpDQogICAgICB0aGlzLmluaXRTZW50aW1lbnRDaGFydCgpDQogICAgfSwNCiAgICBpbml0VHJlbmRDaGFydCgpIHsNCiAgICAgIC8vIOWIneWni+WMluiIhuaDhei2i+WKv+WbvuihqA0KICAgICAgY29uc3QgY2hhcnRDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgndHJlbmQtY2hhcnQnKQ0KICAgICAgaWYgKCFjaGFydENvbnRhaW5lcikgcmV0dXJuDQoNCiAgICAgIHRoaXMudHJlbmRDaGFydCA9IGVjaGFydHMuaW5pdChjaGFydENvbnRhaW5lcikNCg0KICAgICAgLy8g55Sf5oiQMzDlpKnnmoTml6XmnJ/mlbDmja4NCiAgICAgIGNvbnN0IGRhdGVzID0gW10NCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKQ0KICAgICAgZm9yIChsZXQgaSA9IDI5OyBpID49IDA7IGktLSkgew0KICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodG9kYXkpDQogICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpDQogICAgICAgIGRhdGVzLnB1c2goZGF0ZS5nZXRNb250aCgpICsgMSArICcuJyArIGRhdGUuZ2V0RGF0ZSgpKQ0KICAgICAgfQ0KDQogICAgICAvLyDmqKHmi5/lkITlubPlj7DnmoTmlbDmja4NCiAgICAgIGNvbnN0IHdlaWJvRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAxNTAwLCAyNTAwKQ0KICAgICAgY29uc3Qgd2VjaGF0RGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAxMjAwLCAyMDAwKQ0KICAgICAgY29uc3QgZG91eWluRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCA4MDAsIDE1MDApDQogICAgICBjb25zdCB0b3V0aWFEYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDYwMCwgMTIwMCkNCiAgICAgIGNvbnN0IG90aGVyRGF0YSA9IHRoaXMuZ2VuZXJhdGVSYW5kb21EYXRhKDMwLCAzMDAsIDgwMCkNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7DQogICAgICAgICAgICB0eXBlOiAnY3Jvc3MnLA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzZhNzk4NScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zWzBdLm5hbWUgKyAnPGJyLz4nDQogICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChwYXJhbSA9PiB7DQogICAgICAgICAgICAgIHJlc3VsdCArPSBgPHNwYW4gc3R5bGU9ImNvbG9yOiR7cGFyYW0uY29sb3J9Ij7il488L3NwYW4+ICR7cGFyYW0uc2VyaWVzTmFtZX06ICR7cGFyYW0udmFsdWV9PGJyLz5gDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuIHJlc3VsdA0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyflvq7ljZonLCAn5b6u5L+hJywgJ+aKlumfsycsICflpLTmnaEnLCAn5YW25LuWJ10sDQogICAgICAgICAgdG9wOiAyMCwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMyUnLA0KICAgICAgICAgIHRvcDogJzE1JScsDQogICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsDQogICAgICAgICAgZGF0YTogZGF0ZXMsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTAsDQogICAgICAgICAgICBjb2xvcjogJyM2NjYnDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2UwZTBlMCcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMCwNCiAgICAgICAgICAgIGNvbG9yOiAnIzY2NicNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZTBlMGUwJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZjBmMGYwJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+W+ruWNmicsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywNCiAgICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgyNCwgMTQ0LCAyNTUsIDAuNiknIH0sDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgyNCwgMTQ0LCAyNTUsIDAuMSknIH0NCiAgICAgICAgICAgICAgXSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMTg5MGZmJywNCiAgICAgICAgICAgICAgd2lkdGg6IDINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMTg5MGZmJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IHdlaWJvRGF0YQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+W+ruS/oScsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywNCiAgICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSg4MiwgMTk2LCAyNiwgMC42KScgfSwNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDgyLCAxOTYsIDI2LCAwLjEpJyB9DQogICAgICAgICAgICAgIF0pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzUyYzQxYScsDQogICAgICAgICAgICAgIHdpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzUyYzQxYScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiB3ZWNoYXREYXRhDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5oqW6Z+zJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHN0YWNrOiAnVG90YWwnLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDExNCwgNDYsIDIwOSwgMC42KScgfSwNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDExNCwgNDYsIDIwOSwgMC4xKScgfQ0KICAgICAgICAgICAgICBdKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM3MjJlZDEnLA0KICAgICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM3MjJlZDEnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogZG91eWluRGF0YQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WktOadoScsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywNCiAgICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgyNTAsIDE0MCwgMjIsIDAuNiknIH0sDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgyNTAsIDE0MCwgMjIsIDAuMSknIH0NCiAgICAgICAgICAgICAgXSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmE4YzE2JywNCiAgICAgICAgICAgICAgd2lkdGg6IDINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmE4YzE2Jw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IHRvdXRpYURhdGENCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICflhbbku5YnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsDQogICAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWw0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMTYwLCAyMTcsIDE3LCAwLjYpJyB9LA0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoMTYwLCAyMTcsIDE3LCAwLjEpJyB9DQogICAgICAgICAgICAgIF0pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2EwZDkxMScsDQogICAgICAgICAgICAgIHdpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2EwZDkxMScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBvdGhlckRhdGENCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCg0KICAgICAgdGhpcy50cmVuZENoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgfSwNCiAgICBpbml0TWFwKCkgew0KICAgICAgLy8g5pqC5pe26Lez6L+H5Zyw5Zu+5Yid5aeL5YyW77yM6YG/5YWN57y65bCR5Zyw5Zu+5pWw5o2u5a+86Ie055qE6ZSZ6K+vDQogICAgICBjb25zb2xlLmxvZygn5Zyw5Zu+5Yid5aeL5YyW5bey6Lez6L+H77yM6ZyA6KaB5byV5YWl5Lit5Zu95Zyw5Zu+5pWw5o2uJykNCiAgICB9LA0KICAgIGluaXRQbGF0Zm9ybUNoYXJ0KCkgew0KICAgICAgLy8g5Yid5aeL5YyW5bmz5Y+w5YiG5p6Q6Z2i56ev5Zu+DQogICAgICBjb25zdCBjaGFydENvbnRhaW5lciA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdwbGF0Zm9ybS1jaGFydCcpDQogICAgICBpZiAoIWNoYXJ0Q29udGFpbmVyKSByZXR1cm4NCg0KICAgICAgdGhpcy5wbGF0Zm9ybUNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0Q29udGFpbmVyKQ0KDQogICAgICAvLyDnlJ/miJAzMOWkqeeahOaXpeacn+aVsOaNrg0KICAgICAgY29uc3QgZGF0ZXMgPSBbXQ0KICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpDQogICAgICBmb3IgKGxldCBpID0gMjk7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0b2RheSkNCiAgICAgICAgZGF0ZS5zZXREYXRlKGRhdGUuZ2V0RGF0ZSgpIC0gaSkNCiAgICAgICAgZGF0ZXMucHVzaChkYXRlLmdldE1vbnRoKCkgKyAxICsgJy4nICsgZGF0ZS5nZXREYXRlKCkpDQogICAgICB9DQoNCiAgICAgIC8vIOaooeaLn+WQhOW5s+WPsOeahOaVsOaNrg0KICAgICAgY29uc3Qgd2VpYm9EYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDE1MDAsIDIwMDApDQogICAgICBjb25zdCB3ZWNoYXREYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDEyMDAsIDE4MDApDQogICAgICBjb25zdCBkb3V5aW5EYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDgwMCwgMTIwMCkNCiAgICAgIGNvbnN0IHRvdXRpYURhdGEgPSB0aGlzLmdlbmVyYXRlUmFuZG9tRGF0YSgzMCwgNjAwLCAxMDAwKQ0KICAgICAgY29uc3Qgb3RoZXJEYXRhID0gdGhpcy5nZW5lcmF0ZVJhbmRvbURhdGEoMzAsIDMwMCwgNjAwKQ0KDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgdGV4dDogJ+i/kTMw5aSp5bmz5Y+w6IiG5oOF6LaL5Yq/JywNCiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxNiwNCiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdub3JtYWwnLA0KICAgICAgICAgICAgY29sb3I6ICcjMzMzJw0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ2Nyb3NzJywNCiAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyM2YTc5ODUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPicNCiAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKHBhcmFtID0+IHsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9IGA8c3BhbiBzdHlsZT0iY29sb3I6JHtwYXJhbS5jb2xvcn0iPuKXjzwvc3Bhbj4gJHtwYXJhbS5zZXJpZXNOYW1lfTogJHtwYXJhbS52YWx1ZX08YnIvPmANCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICByZXR1cm4gcmVzdWx0DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBkYXRhOiBbJ+W+ruWNmicsICflvq7kv6EnLCAn5oqW6Z+zJywgJ+WktOadoScsICflhbbku5YnXSwNCiAgICAgICAgICB0b3A6IDMwLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgZm9udFNpemU6IDEyDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgbGVmdDogJzMlJywNCiAgICAgICAgICByaWdodDogJzQlJywNCiAgICAgICAgICBib3R0b206ICczJScsDQogICAgICAgICAgdG9wOiAnMTUlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICBkYXRhOiBkYXRlcywNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMCwNCiAgICAgICAgICAgIGNvbG9yOiAnIzY2NicNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZTBlMGUwJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgZm9udFNpemU6IDEwLA0KICAgICAgICAgICAgY29sb3I6ICcjNjY2Jw0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNlMGUwZTAnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNmMGYwZjAnDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5b6u5Y2aJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHN0YWNrOiAnVG90YWwnLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDI0LCAxNDQsIDI1NSwgMC42KScgfSwNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDI0LCAxNDQsIDI1NSwgMC4xKScgfQ0KICAgICAgICAgICAgICBdKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyMxODkwZmYnLA0KICAgICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyMxODkwZmYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogd2VpYm9EYXRhDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5b6u5L+hJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHN0YWNrOiAnVG90YWwnLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDgyLCAxOTYsIDI2LCAwLjYpJyB9LA0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoODIsIDE5NiwgMjYsIDAuMSknIH0NCiAgICAgICAgICAgICAgXSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjNTJjNDFhJywNCiAgICAgICAgICAgICAgd2lkdGg6IDINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjNTJjNDFhJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IHdlY2hhdERhdGENCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmipbpn7MnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgc3RhY2s6ICdUb3RhbCcsDQogICAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgWw0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoMTE0LCA0NiwgMjA5LCAwLjYpJyB9LA0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoMTE0LCA0NiwgMjA5LCAwLjEpJyB9DQogICAgICAgICAgICAgIF0pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzcyMmVkMScsDQogICAgICAgICAgICAgIHdpZHRoOiAyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzcyMmVkMScNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBkYXRhOiBkb3V5aW5EYXRhDQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5aS05p2hJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHN0YWNrOiAnVG90YWwnLA0KICAgICAgICAgICAgYXJlYVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiBuZXcgZWNoYXJ0cy5ncmFwaGljLkxpbmVhckdyYWRpZW50KDAsIDAsIDAsIDEsIFsNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDI1MCwgMTQwLCAyMiwgMC42KScgfSwNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICdyZ2JhKDI1MCwgMTQwLCAyMiwgMC4xKScgfQ0KICAgICAgICAgICAgICBdKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNmYThjMTYnLA0KICAgICAgICAgICAgICB3aWR0aDogMg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNmYThjMTYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogdG91dGlhRGF0YQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WFtuS7licsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBzdGFjazogJ1RvdGFsJywNCiAgICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDAsIGNvbG9yOiAncmdiYSgxNjAsIDIxNywgMTcsIDAuNiknIH0sDQogICAgICAgICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAncmdiYSgxNjAsIDIxNywgMTcsIDAuMSknIH0NCiAgICAgICAgICAgICAgXSkNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjYTBkOTExJywNCiAgICAgICAgICAgICAgd2lkdGg6IDINCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjYTBkOTExJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGRhdGE6IG90aGVyRGF0YQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KDQogICAgICB0aGlzLnBsYXRmb3JtQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICB9LA0KICAgIGluaXRTZW50aW1lbnRDaGFydCgpIHsNCiAgICAgIC8vIOWIneWni+WMluaDheaEn+WxnuaAp+mlvOWbvg0KICAgICAgY29uc3QgY2hhcnRDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VudGltZW50LWNoYXJ0JykNCiAgICAgIGlmICghY2hhcnRDb250YWluZXIpIHJldHVybg0KDQogICAgICB0aGlzLnNlbnRpbWVudENoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0Q29udGFpbmVyKQ0KDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIHRpdGxlOiB7DQogICAgICAgICAgdGV4dDogJ+aDheaEn+WIhuW4gycsDQogICAgICAgICAgbGVmdDogJ2NlbnRlcicsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBmb250U2l6ZTogMTQsDQogICAgICAgICAgICBmb250V2VpZ2h0OiAnbm9ybWFsJywNCiAgICAgICAgICAgIGNvbG9yOiAnIzMzMycNCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgZm9ybWF0dGVyOiAne2F9IDxici8+e2J9OiB7Y30gKHtkfSUpJw0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBvcmllbnQ6ICd2ZXJ0aWNhbCcsDQogICAgICAgICAgbGVmdDogJ2xlZnQnLA0KICAgICAgICAgIHRvcDogJ21pZGRsZScsDQogICAgICAgICAgZGF0YTogWyfmraPpnaInLCAn5Lit5oCnJywgJ+i0n+mdoiddLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgZm9udFNpemU6IDEyDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5oOF5oSf5YiG5biDJywNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiBbJzQwJScsICc3MCUnXSwNCiAgICAgICAgICAgIGNlbnRlcjogWyc2MCUnLCAnNTAlJ10sDQogICAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsDQogICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICBzaG93OiBmYWxzZSwNCiAgICAgICAgICAgICAgcG9zaXRpb246ICdjZW50ZXInDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTgnLA0KICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbGFiZWxMaW5lOiB7DQogICAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogWw0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbmFtZTogJ+ato+mdoicsDQogICAgICAgICAgICAgICAgdmFsdWU6IDY1LjIsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogJyM1MmM0MWEnDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbmFtZTogJ+S4reaApycsDQogICAgICAgICAgICAgICAgdmFsdWU6IDI4LjMsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgICBjb2xvcjogJyNmYWFkMTQnDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgbmFtZTogJ+i0n+mdoicsDQogICAgICAgICAgICAgICAgdmFsdWU6IDYuNSwNCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmNGQ0ZicNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIF0NCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCg0KICAgICAgdGhpcy5zZW50aW1lbnRDaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQogICAgLy8g55Sf5oiQ6ZqP5py65pWw5o2u55qE6L6F5Yqp5pa55rOVDQogICAgZ2VuZXJhdGVSYW5kb21EYXRhKGNvdW50LCBtaW4sIG1heCkgew0KICAgICAgY29uc3QgZGF0YSA9IFtdDQogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNvdW50OyBpKyspIHsNCiAgICAgICAgY29uc3QgdmFsdWUgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAobWF4IC0gbWluICsgMSkpICsgbWluDQogICAgICAgIGRhdGEucHVzaCh2YWx1ZSkNCiAgICAgIH0NCiAgICAgIHJldHVybiBkYXRhDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "index.vue", "sourceRoot": "src/views/opinion-overview", "sourcesContent": ["<template>\r\n  <div class=\"opinion-overview\">\r\n    <!-- 左侧导航栏 -->\r\n    <div class=\"left-sidebar\">\r\n      <div class=\"sidebar-header\">\r\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\">新建方案</el-button>\r\n      </div>\r\n\r\n      <div class=\"sidebar-search\">\r\n        <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"搜索方案\"\r\n          size=\"small\"\r\n          prefix-icon=\"el-icon-search\">\r\n        </el-input>\r\n      </div>\r\n\r\n      <div class=\"sidebar-menu\">\r\n        <div class=\"menu-section\">\r\n          <div class=\"section-title\">已有方案</div>\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\">\r\n            <el-menu-item index=\"基础(1)\">\r\n              <i class=\"el-icon-s-custom\"></i>\r\n              <span>基础(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"品牌(1)\">\r\n              <i class=\"el-icon-s-goods\"></i>\r\n              <span>品牌(1)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"方太\" class=\"active-item\">\r\n              <i class=\"el-icon-star-off\"></i>\r\n              <span>方太</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"人物(0)\">\r\n              <i class=\"el-icon-user\"></i>\r\n              <span>人物(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"机构(0)\">\r\n              <i class=\"el-icon-office-building\"></i>\r\n              <span>机构(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"产品(0)\">\r\n              <i class=\"el-icon-goods\"></i>\r\n              <span>产品(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"事件(0)\">\r\n              <i class=\"el-icon-warning\"></i>\r\n              <span>事件(0)</span>\r\n            </el-menu-item>\r\n            <el-menu-item index=\"话题(0)\">\r\n              <i class=\"el-icon-chat-dot-round\"></i>\r\n              <span>话题(0)</span>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 右侧内容区 -->\r\n    <div class=\"right-content\">\r\n      <!-- 顶部导航标签栏 -->\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"main-content\">\r\n        <!-- 根据activeTab显示不同内容 -->\r\n        <div v-if=\"activeTab === 'opinion-monitor'\">\r\n          <!-- 舆情监测内容 -->\r\n\r\n          <!-- 舆情趋势图表 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"chart-container\">\r\n              <div id=\"trend-chart\" style=\"width: 100%; height: 400px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 热门文章和最新公告 -->\r\n          <div class=\"bottom-section\">\r\n            <div class=\"left-articles\">\r\n              <div class=\"article-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-document\" style=\"color: #1890ff; margin-right: 8px;\"></i>热门文章</h3>\r\n                </div>\r\n                <div class=\"article-list\">\r\n                  <div class=\"article-item\" v-for=\"(article, index) in hotArticles\" :key=\"index\">\r\n                    <div class=\"article-icon\" :class=\"article.type\">{{ article.icon }}</div>\r\n                    <div class=\"article-content\">\r\n                      <div class=\"article-title\">{{ article.title }}</div>\r\n                      <div class=\"article-meta\">\r\n                        <span class=\"article-source\">{{ article.source }}</span>\r\n                        <span class=\"article-author\">{{ article.author }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"right-announcements\">\r\n              <div class=\"announcement-card\">\r\n                <div class=\"card-header\">\r\n                  <h3><i class=\"el-icon-bell\" style=\"color: #52c41a; margin-right: 8px;\"></i>最新公告</h3>\r\n                </div>\r\n                <div class=\"announcement-list\">\r\n                  <div class=\"announcement-item\" v-for=\"(announcement, index) in announcements\" :key=\"index\">\r\n                    <div class=\"announcement-indicator\" :class=\"announcement.level\"></div>\r\n                    <div class=\"announcement-content\">\r\n                      <div class=\"announcement-title\">{{ announcement.title }}</div>\r\n                      <div class=\"announcement-time\">{{ announcement.time }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-else-if=\"activeTab === 'info-summary'\">\r\n          <!-- 信息汇总内容 -->\r\n          <!-- 近30天舆情发布地区 -->\r\n          <div class=\"section-card\">\r\n            <div class=\"card-header\">\r\n              <h3><i class=\"el-icon-location\" style=\"color: #409EFF; margin-right: 8px;\"></i>近30天舆情发布地区</h3>\r\n              <div class=\"stats\">\r\n                <div class=\"stat-item positive\">\r\n                  <span class=\"label\">正面舆情</span>\r\n                  <span class=\"value\">111930</span>\r\n                </div>\r\n                <div class=\"stat-item neutral\">\r\n                  <span class=\"label\">中性舆情</span>\r\n                  <span class=\"value\">1118</span>\r\n                </div>\r\n                <div class=\"stat-item negative\">\r\n                  <span class=\"label\">负面舆情</span>\r\n                  <span class=\"value\">444</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"map-container\">\r\n              <div id=\"china-map\" style=\"width: 100%; height: 400px; background-color: #f0f2f5; display: flex; align-items: center; justify-content: center; color: #999;\">\r\n                <div>地图组件加载中...</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 今日舆情总量和平台分析 -->\r\n          <div class=\"bottom-section\">\r\n          <div class=\"left-charts\">\r\n            <!-- 近30天平台分析 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-pie-chart\" style=\"color: #52C41A; margin-right: 8px;\"></i>近30天平台分析</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"platform-chart\" style=\"width: 100%; height: 300px;\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"right-stats\">\r\n            <!-- 今日舆情总量 -->\r\n            <div class=\"stats-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-data-line\" style=\"color: #FA8C16; margin-right: 8px;\"></i>今日舆情总量</h3>\r\n              </div>\r\n              <div class=\"total-count\">0 0 0,0 0 4,6 8 1</div>\r\n              <div class=\"platform-stats\">\r\n                <div class=\"platform-item weibo\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">534</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item wechat\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微信</span>\r\n                    <span class=\"platform-count\">1483</span>\r\n                    <span class=\"platform-change\">今日新增 15.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item weibo-red\">\r\n                  <div class=\"platform-icon\">微</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">微博</span>\r\n                    <span class=\"platform-count\">279</span>\r\n                    <span class=\"platform-change\">今日新增 -0.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item xiaohongshu\">\r\n                  <div class=\"platform-icon\">小</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">小红书</span>\r\n                    <span class=\"platform-count\">129</span>\r\n                    <span class=\"platform-change\">今日新增 3.2%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item app\">\r\n                  <div class=\"platform-icon\">A</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">APP</span>\r\n                    <span class=\"platform-count\">764</span>\r\n                    <span class=\"platform-change\">今日新增 -1.8%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item toutiao\">\r\n                  <div class=\"platform-icon\">头</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">头条</span>\r\n                    <span class=\"platform-count\">1455</span>\r\n                    <span class=\"platform-change\">今日新增 4.5%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item douyin\">\r\n                  <div class=\"platform-icon\">抖</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">抖音</span>\r\n                    <span class=\"platform-count\">23</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item news\">\r\n                  <div class=\"platform-icon\">新</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">新闻</span>\r\n                    <span class=\"platform-count\">2</span>\r\n                    <span class=\"platform-change\">今日新增 100%</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"platform-item forum\">\r\n                  <div class=\"platform-icon\">论</div>\r\n                  <div class=\"platform-info\">\r\n                    <span class=\"platform-name\">论坛</span>\r\n                    <span class=\"platform-count\">12</span>\r\n                    <span class=\"platform-change\">今日新增 -2.8%</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 近30天情感属性 -->\r\n            <div class=\"chart-card\">\r\n              <div class=\"card-header\">\r\n                <h3><i class=\"el-icon-sunny\" style=\"color: #722ED1; margin-right: 8px;\"></i>近30天情感属性</h3>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出</el-button>\r\n              </div>\r\n              <div id=\"sentiment-chart\" style=\"width: 100%; height: 200px;\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'OpinionOverview',\r\n  data() {\r\n    return {\r\n      activeMenuItem: '方太',\r\n      searchText: '',\r\n      activeTab: 'opinion-monitor', // 默认激活舆情监测标签\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      // 图表实例\r\n      mapChart: null,\r\n      platformChart: null,\r\n      sentimentChart: null,\r\n      trendChart: null, // 趋势图表实例\r\n      // 热门文章数据\r\n      hotArticles: [\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '某品牌产品质量问题引发消费者不满',\r\n          source: '新浪财经',\r\n          author: '财经记者'\r\n        },\r\n        {\r\n          icon: '黄',\r\n          type: 'neutral',\r\n          title: '市场分析：家电行业发展趋势',\r\n          source: '中国经济网',\r\n          author: '市场分析师'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '创新技术推动行业发展',\r\n          source: '科技日报',\r\n          author: '科技记者'\r\n        },\r\n        {\r\n          icon: '红',\r\n          type: 'negative',\r\n          title: '消费者投诉处理不当引发关注',\r\n          source: '消费者报',\r\n          author: '消费维权'\r\n        },\r\n        {\r\n          icon: '绿',\r\n          type: 'positive',\r\n          title: '企业社会责任获得认可',\r\n          source: '人民日报',\r\n          author: '社会记者'\r\n        }\r\n      ],\r\n      // 最新公告数据\r\n      announcements: [\r\n        {\r\n          level: 'high',\r\n          title: '舆情监测系统升级通知',\r\n          time: '2023-04-20 10:30:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '五一假期监测安排',\r\n          time: '2023-04-19 16:45:00'\r\n        },\r\n        {\r\n          level: 'low',\r\n          title: '数据统计报告已生成',\r\n          time: '2023-04-19 14:20:00'\r\n        },\r\n        {\r\n          level: 'high',\r\n          title: '重要舆情预警提醒',\r\n          time: '2023-04-19 09:15:00'\r\n        },\r\n        {\r\n          level: 'medium',\r\n          title: '系统维护完成通知',\r\n          time: '2023-04-18 18:30:00'\r\n        }\r\n      ],\r\n      // 地图数据\r\n      mapData: [\r\n        {name: '北京', value: 15000},\r\n        {name: '上海', value: 12000},\r\n        {name: '广东', value: 18000},\r\n        {name: '浙江', value: 8000},\r\n        {name: '江苏', value: 9000},\r\n        {name: '山东', value: 7000},\r\n        {name: '四川', value: 6000},\r\n        {name: '湖北', value: 5000}\r\n      ],\r\n      // 平台数据\r\n      platformData: [\r\n        {name: '微博', value: 35.2, color: '#ff6b6b'},\r\n        {name: '微信', value: 28.6, color: '#51cf66'},\r\n        {name: '抖音', value: 18.4, color: '#339af0'},\r\n        {name: '今日头条', value: 12.8, color: '#ffd43b'},\r\n        {name: '其他', value: 5.0, color: '#868e96'}\r\n      ],\r\n      // 情感数据\r\n      sentimentData: [\r\n        {name: '正面', value: 65.2, color: '#52c41a'},\r\n        {name: '中性', value: 28.3, color: '#faad14'},\r\n        {name: '负面', value: 6.5, color: '#ff4d4f'}\r\n      ]\r\n    }\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    this.$nextTick(() => {\r\n      // 根据当前激活的标签初始化对应的图表\r\n      if (this.activeTab === 'opinion-monitor') {\r\n        this.initTrendChart()\r\n      } else if (this.activeTab === 'info-summary') {\r\n        this.initCharts()\r\n      }\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n\r\n    // 销毁图表实例\r\n    if (this.mapChart) {\r\n      this.mapChart.dispose()\r\n    }\r\n    if (this.platformChart) {\r\n      this.platformChart.dispose()\r\n    }\r\n    if (this.sentimentChart) {\r\n      this.sentimentChart.dispose()\r\n    }\r\n    if (this.trendChart) {\r\n      this.trendChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index\r\n      console.log('Menu selected:', index)\r\n      // 这里可以根据选择的方案加载不同的数据\r\n    },\r\n    handleTabClick(tab) {\r\n      console.log('Tab clicked:', tab.name)\r\n      // 根据不同标签加载不同内容\r\n      this.$nextTick(() => {\r\n        if (tab.name === 'opinion-monitor') {\r\n          this.initTrendChart()\r\n        } else if (tab.name === 'info-summary') {\r\n          this.initCharts()\r\n        }\r\n      })\r\n    },\r\n    initCharts() {\r\n      // 初始化图表\r\n      this.initMap()\r\n      this.initPlatformChart()\r\n      this.initSentimentChart()\r\n    },\r\n    initTrendChart() {\r\n      // 初始化舆情趋势图表\r\n      const chartContainer = document.getElementById('trend-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.trendChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2500)\r\n      const wechatData = this.generateRandomData(30, 1200, 2000)\r\n      const douyinData = this.generateRandomData(30, 800, 1500)\r\n      const toutiaData = this.generateRandomData(30, 600, 1200)\r\n      const otherData = this.generateRandomData(30, 300, 800)\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 20,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.trendChart.setOption(option)\r\n    },\r\n    initMap() {\r\n      // 暂时跳过地图初始化，避免缺少地图数据导致的错误\r\n      console.log('地图初始化已跳过，需要引入中国地图数据')\r\n    },\r\n    initPlatformChart() {\r\n      // 初始化平台分析面积图\r\n      const chartContainer = document.getElementById('platform-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.platformChart = echarts.init(chartContainer)\r\n\r\n      // 生成30天的日期数据\r\n      const dates = []\r\n      const today = new Date()\r\n      for (let i = 29; i >= 0; i--) {\r\n        const date = new Date(today)\r\n        date.setDate(date.getDate() - i)\r\n        dates.push(date.getMonth() + 1 + '.' + date.getDate())\r\n      }\r\n\r\n      // 模拟各平台的数据\r\n      const weiboData = this.generateRandomData(30, 1500, 2000)\r\n      const wechatData = this.generateRandomData(30, 1200, 1800)\r\n      const douyinData = this.generateRandomData(30, 800, 1200)\r\n      const toutiaData = this.generateRandomData(30, 600, 1000)\r\n      const otherData = this.generateRandomData(30, 300, 600)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '近30天平台舆情趋势',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 16,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            label: {\r\n              backgroundColor: '#6a7985'\r\n            }\r\n          },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              result += `<span style=\"color:${param.color}\">●</span> ${param.seriesName}: ${param.value}<br/>`\r\n            })\r\n            return result\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['微博', '微信', '抖音', '头条', '其他'],\r\n          top: 30,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '3%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: dates,\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            fontSize: 10,\r\n            color: '#666'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#e0e0e0'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: '#f0f0f0'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '微博',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },\r\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#1890ff',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#1890ff'\r\n            },\r\n            data: weiboData\r\n          },\r\n          {\r\n            name: '微信',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(82, 196, 26, 0.6)' },\r\n                { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#52c41a',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#52c41a'\r\n            },\r\n            data: wechatData\r\n          },\r\n          {\r\n            name: '抖音',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(114, 46, 209, 0.6)' },\r\n                { offset: 1, color: 'rgba(114, 46, 209, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#722ed1',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#722ed1'\r\n            },\r\n            data: douyinData\r\n          },\r\n          {\r\n            name: '头条',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(250, 140, 22, 0.6)' },\r\n                { offset: 1, color: 'rgba(250, 140, 22, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#fa8c16',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#fa8c16'\r\n            },\r\n            data: toutiaData\r\n          },\r\n          {\r\n            name: '其他',\r\n            type: 'line',\r\n            stack: 'Total',\r\n            areaStyle: {\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: 'rgba(160, 217, 17, 0.6)' },\r\n                { offset: 1, color: 'rgba(160, 217, 17, 0.1)' }\r\n              ])\r\n            },\r\n            lineStyle: {\r\n              color: '#a0d911',\r\n              width: 2\r\n            },\r\n            itemStyle: {\r\n              color: '#a0d911'\r\n            },\r\n            data: otherData\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.platformChart.setOption(option)\r\n    },\r\n    initSentimentChart() {\r\n      // 初始化情感属性饼图\r\n      const chartContainer = document.getElementById('sentiment-chart')\r\n      if (!chartContainer) return\r\n\r\n      this.sentimentChart = echarts.init(chartContainer)\r\n\r\n      const option = {\r\n        title: {\r\n          text: '情感分布',\r\n          left: 'center',\r\n          textStyle: {\r\n            fontSize: 14,\r\n            fontWeight: 'normal',\r\n            color: '#333'\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: 'left',\r\n          top: 'middle',\r\n          data: ['正面', '中性', '负面'],\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '情感分布',\r\n            type: 'pie',\r\n            radius: ['40%', '70%'],\r\n            center: ['60%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '18',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              {\r\n                name: '正面',\r\n                value: 65.2,\r\n                itemStyle: {\r\n                  color: '#52c41a'\r\n                }\r\n              },\r\n              {\r\n                name: '中性',\r\n                value: 28.3,\r\n                itemStyle: {\r\n                  color: '#faad14'\r\n                }\r\n              },\r\n              {\r\n                name: '负面',\r\n                value: 6.5,\r\n                itemStyle: {\r\n                  color: '#ff4d4f'\r\n                }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.sentimentChart.setOption(option)\r\n    },\r\n    // 生成随机数据的辅助方法\r\n    generateRandomData(count, min, max) {\r\n      const data = []\r\n      for (let i = 0; i < count; i++) {\r\n        const value = Math.floor(Math.random() * (max - min + 1)) + min\r\n        data.push(value)\r\n      }\r\n      return data\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.opinion-overview {\r\n  display: flex;\r\n  height: 100vh;\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.left-sidebar {\r\n  width: 280px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e8e8e8;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .sidebar-header {\r\n    padding: 16px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n  }\r\n\r\n  .sidebar-search {\r\n    padding: 16px;\r\n  }\r\n\r\n  .sidebar-menu {\r\n    flex: 1;\r\n    padding: 0 16px;\r\n\r\n    .section-title {\r\n      font-size: 14px;\r\n      color: #666;\r\n      margin-bottom: 8px;\r\n    }\r\n\r\n    .sidebar-menu-list {\r\n      border: none;\r\n\r\n      .el-menu-item {\r\n        height: 40px;\r\n        line-height: 40px;\r\n        margin-bottom: 4px;\r\n        border-radius: 4px;\r\n\r\n        &.active-item {\r\n          background-color: #e6f7ff;\r\n          color: #1890ff;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .top-tabs {\r\n    background-color: #fff;\r\n    border-bottom: 1px solid #e8e8e8;\r\n\r\n    .el-tabs {\r\n      .el-tabs__header {\r\n        margin: 0;\r\n\r\n        .el-tabs__nav-wrap {\r\n          padding: 0 24px;\r\n        }\r\n\r\n        .el-tabs__item {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          font-size: 14px;\r\n\r\n          &.is-active {\r\n            color: #1890ff;\r\n            border-bottom-color: #1890ff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .main-content {\r\n    flex: 1;\r\n    padding: 24px;\r\n    overflow-y: auto;\r\n  }\r\n}\r\n\r\n.section-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 24px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n    }\r\n\r\n    .stats {\r\n      display: flex;\r\n      gap: 24px;\r\n\r\n      .stat-item {\r\n        text-align: center;\r\n\r\n        .label {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #666;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .value {\r\n          display: block;\r\n          font-size: 24px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        &.positive .value {\r\n          color: #52c41a;\r\n        }\r\n\r\n        &.neutral .value {\r\n          color: #faad14;\r\n        }\r\n\r\n        &.negative .value {\r\n          color: #ff4d4f;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-section {\r\n  display: flex;\r\n  gap: 24px;\r\n\r\n  .left-charts {\r\n    flex: 2;\r\n  }\r\n\r\n  .right-stats {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n\r\n  .left-articles {\r\n    flex: 1;\r\n  }\r\n\r\n  .right-announcements {\r\n    flex: 1;\r\n  }\r\n}\r\n\r\n.chart-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n.stats-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .total-count {\r\n    font-size: 32px;\r\n    font-weight: 600;\r\n    text-align: center;\r\n    margin: 24px 0;\r\n    letter-spacing: 2px;\r\n  }\r\n\r\n  .platform-stats {\r\n    .platform-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .platform-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n      }\r\n\r\n      &.weibo .platform-icon {\r\n        background-color: #1890ff;\r\n      }\r\n\r\n      &.wechat .platform-icon {\r\n        background-color: #52c41a;\r\n      }\r\n\r\n      &.weibo-red .platform-icon {\r\n        background-color: #ff4d4f;\r\n      }\r\n\r\n      &.xiaohongshu .platform-icon {\r\n        background-color: #eb2f96;\r\n      }\r\n\r\n      &.app .platform-icon {\r\n        background-color: #13c2c2;\r\n      }\r\n\r\n      &.toutiao .platform-icon {\r\n        background-color: #fa8c16;\r\n      }\r\n\r\n      &.douyin .platform-icon {\r\n        background-color: #722ed1;\r\n      }\r\n\r\n      &.news .platform-icon {\r\n        background-color: #faad14;\r\n      }\r\n\r\n      &.forum .platform-icon {\r\n        background-color: #a0d911;\r\n      }\r\n\r\n      .platform-info {\r\n        flex: 1;\r\n\r\n        .platform-name {\r\n          display: block;\r\n          font-size: 14px;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-count {\r\n          display: block;\r\n          font-size: 18px;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n\r\n        .platform-change {\r\n          display: block;\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 热门文章样式\r\n.article-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .article-list {\r\n    .article-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .article-icon {\r\n        width: 32px;\r\n        height: 32px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: #fff;\r\n        font-weight: 600;\r\n        margin-right: 12px;\r\n        font-size: 14px;\r\n\r\n        &.negative {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.neutral {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.positive {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .article-content {\r\n        flex: 1;\r\n\r\n        .article-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .article-meta {\r\n          font-size: 12px;\r\n          color: #666;\r\n\r\n          .article-source {\r\n            margin-right: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 最新公告样式\r\n.announcement-card {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n  .card-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .announcement-list {\r\n    .announcement-item {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #f0f0f0;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .announcement-indicator {\r\n        width: 8px;\r\n        height: 8px;\r\n        border-radius: 50%;\r\n        margin-right: 12px;\r\n\r\n        &.high {\r\n          background-color: #ff4d4f;\r\n        }\r\n\r\n        &.medium {\r\n          background-color: #faad14;\r\n        }\r\n\r\n        &.low {\r\n          background-color: #52c41a;\r\n        }\r\n      }\r\n\r\n      .announcement-content {\r\n        flex: 1;\r\n\r\n        .announcement-title {\r\n          font-size: 14px;\r\n          color: #333;\r\n          margin-bottom: 4px;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .announcement-time {\r\n          font-size: 12px;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}