{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\store\\modules\\settings.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\store\\modules\\settings.js", "mtime": 1749109381335}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_settings", "_interopRequireDefault", "require", "sideTheme", "defaultSettings", "showSettings", "topNav", "tagsView", "fixedHeader", "sidebarLogo", "dynamicTitle", "storageSetting", "JSON", "parse", "localStorage", "getItem", "state", "title", "theme", "undefined", "mutations", "CHANGE_SETTING", "_ref", "key", "value", "hasOwnProperty", "actions", "changeSetting", "_ref2", "data", "commit", "setTitle", "_ref3", "_default", "exports", "default", "namespaced"], "sources": ["D:/thinktank/thinktankui/src/store/modules/settings.js"], "sourcesContent": ["import defaultSettings from '@/settings'\r\n\r\nconst { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } = defaultSettings\r\n\r\nconst storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''\r\nconst state = {\r\n  title: '',\r\n  theme: storageSetting.theme || '#409EFF',\r\n  sideTheme: storageSetting.sideTheme || sideTheme,\r\n  showSettings: showSettings,\r\n  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,\r\n  tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,\r\n  fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,\r\n  sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,\r\n  dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle\r\n}\r\nconst mutations = {\r\n  CHANGE_SETTING: (state, { key, value }) => {\r\n    if (state.hasOwnProperty(key)) {\r\n      state[key] = value\r\n    }\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  // 修改布局设置\r\n  changeSetting({ commit }, data) {\r\n    commit('CHANGE_SETTING', data)\r\n  },\r\n  // 设置网页标题\r\n  setTitle({ commit }, title) {\r\n    state.title = title\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n\r\n"], "mappings": ";;;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAQC,SAAS,GAA6EC,iBAAe,CAArGD,SAAS;EAAEE,YAAY,GAA+DD,iBAAe,CAA1FC,YAAY;EAAEC,MAAM,GAAuDF,iBAAe,CAA5EE,MAAM;EAAEC,QAAQ,GAA6CH,iBAAe,CAApEG,QAAQ;EAAEC,WAAW,GAAgCJ,iBAAe,CAA1DI,WAAW;EAAEC,WAAW,GAAmBL,iBAAe,CAA7CK,WAAW;EAAEC,YAAY,GAAKN,iBAAe,CAAhCM,YAAY;AAEzF,IAAMC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE;AAC/E,IAAMC,KAAK,GAAG;EACZC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAEP,cAAc,CAACO,KAAK,IAAI,SAAS;EACxCf,SAAS,EAAEQ,cAAc,CAACR,SAAS,IAAIA,SAAS;EAChDE,YAAY,EAAEA,YAAY;EAC1BC,MAAM,EAAEK,cAAc,CAACL,MAAM,KAAKa,SAAS,GAAGb,MAAM,GAAGK,cAAc,CAACL,MAAM;EAC5EC,QAAQ,EAAEI,cAAc,CAACJ,QAAQ,KAAKY,SAAS,GAAGZ,QAAQ,GAAGI,cAAc,CAACJ,QAAQ;EACpFC,WAAW,EAAEG,cAAc,CAACH,WAAW,KAAKW,SAAS,GAAGX,WAAW,GAAGG,cAAc,CAACH,WAAW;EAChGC,WAAW,EAAEE,cAAc,CAACF,WAAW,KAAKU,SAAS,GAAGV,WAAW,GAAGE,cAAc,CAACF,WAAW;EAChGC,YAAY,EAAEC,cAAc,CAACD,YAAY,KAAKS,SAAS,GAAGT,YAAY,GAAGC,cAAc,CAACD;AAC1F,CAAC;AACD,IAAMU,SAAS,GAAG;EAChBC,cAAc,EAAE,SAAhBA,cAAcA,CAAGL,KAAK,EAAAM,IAAA,EAAqB;IAAA,IAAjBC,GAAG,GAAAD,IAAA,CAAHC,GAAG;MAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAClC,IAAIR,KAAK,CAACS,cAAc,CAACF,GAAG,CAAC,EAAE;MAC7BP,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF;AACF,CAAC;AAED,IAAME,OAAO,GAAG;EACd;EACAC,aAAa,WAAbA,aAAaA,CAAAC,KAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,KAAA,CAANE,MAAM;IACpBA,MAAM,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAChC,CAAC;EACD;EACAE,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAaf,KAAK,EAAE;IAAA,IAAjBa,MAAM,GAAAE,KAAA,CAANF,MAAM;IACfd,KAAK,CAACC,KAAK,GAAGA,KAAK;EACrB;AACF,CAAC;AAAA,IAAAgB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACbC,UAAU,EAAE,IAAI;EAChBpB,KAAK,EAALA,KAAK;EACLI,SAAS,EAATA,SAAS;EACTM,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}