{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\api\\system\\menu.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\api\\system\\menu.js", "mtime": 1749109381296}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listMenu", "query", "request", "url", "method", "params", "getMenu", "menuId", "treeselect", "roleMenuTreeselect", "roleId", "addMenu", "data", "updateMenu", "delMenu"], "sources": ["D:/thinktank/thinktankui/src/api/system/menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询菜单列表\r\nexport function listMenu(query) {\r\n  return request({\r\n    url: '/system/menu/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询菜单详细\r\nexport function getMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询菜单下拉树结构\r\nexport function treeselect() {\r\n  return request({\r\n    url: '/system/menu/treeselect',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据角色ID查询菜单下拉树结构\r\nexport function roleMenuTreeselect(roleId) {\r\n  return request({\r\n    url: '/system/menu/roleMenuTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增菜单\r\nexport function addMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改菜单\r\nexport function updateMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除菜单\r\nexport function delMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'delete'\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGO,MAAM;IAChDN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}