{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=style&index=0&id=6494804b&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1749109381331}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5zaWRlYmFyTG9nb0ZhZGUtZW50ZXItYWN0aXZlIHsNCiAgdHJhbnNpdGlvbjogb3BhY2l0eSAxLjVzOw0KfQ0KDQouc2lkZWJhckxvZ29GYWRlLWVudGVyLA0KLnNpZGViYXJMb2dvRmFkZS1sZWF2ZS10byB7DQogIG9wYWNpdHk6IDA7DQp9DQoNCi5zaWRlYmFyLWxvZ28tY29udGFpbmVyIHsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA1MHB4Ow0KICBsaW5lLWhlaWdodDogNTBweDsNCiAgYmFja2dyb3VuZDogIzJiMmYzYTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KDQogICYgLnNpZGViYXItbG9nby1saW5rIHsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgd2lkdGg6IDEwMCU7DQoNCiAgICAmIC5zaWRlYmFyLWxvZ28gew0KICAgICAgd2lkdGg6IDMycHg7DQogICAgICBoZWlnaHQ6IDMycHg7DQogICAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOw0KICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgIH0NCg0KICAgICYgLnNpZGViYXItdGl0bGUgew0KICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgbWFyZ2luOiAwOw0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgbGluZS1oZWlnaHQ6IDUwcHg7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBmb250LWZhbWlseTogQXZlbmlyLCBIZWx2ZXRpY2EgTmV1ZSwgQXJpYWwsIEhlbHZldGljYSwgc2Fucy1zZXJpZjsNCiAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogICAgfQ0KICB9DQoNCiAgJi5jb2xsYXBzZSB7DQogICAgLnNpZGViYXItbG9nbyB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDBweDsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Logo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Logo.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div class=\"sidebar-logo-container\" :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\r\n        <h1 v-else class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\r\n        <h1 class=\"sidebar-title\" :style=\"{ color: sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\">{{ title }} </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logoImg from '@/assets/logo/logo.png'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'SidebarLogo',\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    variables() {\r\n      return variables;\r\n    },\r\n    sideTheme() {\r\n      return this.$store.state.settings.sideTheme\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: process.env.VUE_APP_TITLE,\r\n      logo: logoImg\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  background: #2b2f3a;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      width: 32px;\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}