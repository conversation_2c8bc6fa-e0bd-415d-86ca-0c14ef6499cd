{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/box/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC1F,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAExE,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAExC;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA0B;IAC/B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAmD,OAAO,OAA1D,EAAE,MAAM,GAA2C,OAAO,OAAlD,EAAE,UAAU,GAA+B,OAAO,WAAtC,EAAE,KAAK,GAAwB,OAAO,MAA/B,EAAE,OAAO,GAAe,OAAO,QAAtB,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAEzE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAEhD,IAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;IACxD,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAEtE,IAAI,cAAc,GAAG,OAAO,CAAC;IAC7B,IAAI,cAAc,KAAK,KAAK,EAAE;QAC5B,cAAc,GAAG,UAAU,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,cAAc,CAAC,CAAC;KAC5F;IAEO,IAAA,GAAG,GAAK,MAAM,CACpB,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,QAAA;YACN,MAAM,EAAE,UAAU;YAClB,WAAW,EAAE,UAAU;YACvB,OAAO,EAAE,cAAc;YACvB,SAAS,WAAA;YACT,kBAAkB;YAClB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE;gBACN,KAAK,EAAE,KAAK;gBACZ,KAAK,OAAA;gBACL,KAAK,EAAE,QAAQ;aAChB;SACF;KACF,CAAC,CACH,IAjBU,CAiBT;IAEF,IAAI,UAAU,EAAE;QACd,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC9B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,MAA0B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAyD,OAAO,OAAhE,EAAE,IAAI,GAAmD,OAAO,KAA1D,EAAE,aAAa,GAAoC,OAAO,cAA3C,EAAE,aAAa,GAAqB,OAAO,cAA5B,EAAE,OAAO,GAAY,OAAO,QAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE/E,IAAI,CAAC,aAAa;QAAE,OAAO,MAAM,CAAC;IAElC,IAAM,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,OAAO,SAAA,EAAE,EAAE,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACzE,IAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK;QAC9C,IAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;QAC1C,YAAY,CAAC,OAAO,CAAC,UAAC,CAAC;;YAAK,OAAA,GAAG,CAAC,IAAI,uBAAM,KAAK,gBAAG,aAAa,IAAG,CAAC,OAAG;QAA1C,CAA0C,CAAC,CAAC;QACxE,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAkB,CAAC;IAExB,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACpC,KAAK,CAAC;QACJ,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE;YACP,MAAM,QAAA;YACN,MAAM,EAAE,aAAa;YACrB,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE;YAChD,KAAK,OAAA;SACN;KACF,CAAC,CAAC;IAEH,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEzB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA0B;;IAC9B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAkD,OAAO,KAAzD,EAAE,KAAK,GAA2C,OAAO,MAAlD,EAAE,KAAK,GAAoC,OAAO,MAA3C,EAAE,MAAM,GAA4B,OAAO,OAAnC,EAAE,MAAM,GAAoB,OAAO,OAA3B,EAAE,aAAa,GAAK,OAAO,cAAZ,CAAa;IACtE,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;IAE9D,IAAI,QAAQ,GAAG,EAAE,CAAC;IAElB,6CAA6C;IAC7C,IAAI,aAAa,EAAE;QACjB,IAAM,QAAQ,GAAG,aAAa,CAAC;QAC/B,QAAQ;YACN,GAAC,aAAa,IAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;YAC/C,GAAC,UAAU,IAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;eAC7C,CAAC;KACH;IAED,IAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI;QACtC,GAAC,MAAM,IAAG,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;QAC5C,GAAC,UAAU,IAAG,IAAI,CAAC,KAAK,EAAE,qBAAqB,CAAC;YAChD,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA0B;IAC9B,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IACjD,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;IAE9D,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;KAC9B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAC/B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA0B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAiB,OAAO,OAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAEvC,IAAI,UAAU,EAAE;QACd,IAAI,MAAM,EAAE;YACV,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;SAClC;aAAM;YACL,sEAAsE;YACtE,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;SAClD;KACF;SAAM;QACL,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA0B;IAChD,OAAO,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;AACtH,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { isArray } from '@antv/util';\nimport { animation, annotation, interaction, theme, tooltip } from '../../adaptor/common';\nimport { point, schema } from '../../adaptor/geometries';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow, pick } from '../../utils';\nimport { BOX_RANGE, BOX_SYNC_NAME, OUTLIERS_VIEW_ID } from './constant';\nimport { BoxOptions } from './types';\nimport { transformData } from './utils';\n\n/**\n * 字段\n * @param params\n */\nfunction field(params: Params<BoxOptions>): Params<BoxOptions> {\n  const { chart, options } = params;\n  const { xField, yField, groupField, color, tooltip, boxStyle } = options;\n\n  chart.data(transformData(options.data, yField));\n\n  const yFieldName = isArray(yField) ? BOX_RANGE : yField;\n  const rawFields = yField ? (isArray(yField) ? yField : [yField]) : [];\n\n  let tooltipOptions = tooltip;\n  if (tooltipOptions !== false) {\n    tooltipOptions = deepAssign({}, { fields: isArray(yField) ? yField : [] }, tooltipOptions);\n  }\n\n  const { ext } = schema(\n    deepAssign({}, params, {\n      options: {\n        xField,\n        yField: yFieldName,\n        seriesField: groupField,\n        tooltip: tooltipOptions,\n        rawFields,\n        // 只有异常点视图展示 label\n        label: false,\n        schema: {\n          shape: 'box',\n          color,\n          style: boxStyle,\n        },\n      },\n    })\n  );\n\n  if (groupField) {\n    ext.geometry.adjust('dodge');\n  }\n\n  return params;\n}\n\n/**\n * 创建异常点 view\n */\nfunction outliersPoint(params: Params<BoxOptions>): Params<BoxOptions> {\n  const { chart, options } = params;\n  const { xField, data, outliersField, outliersStyle, padding, label } = options;\n\n  if (!outliersField) return params;\n\n  const outliersView = chart.createView({ padding, id: OUTLIERS_VIEW_ID });\n  const outliersViewData = data.reduce((ret, datum) => {\n    const outliersData = datum[outliersField];\n    outliersData.forEach((d) => ret.push({ ...datum, [outliersField]: d }));\n    return ret;\n  }, []) as Types.Datum[];\n\n  outliersView.data(outliersViewData);\n  point({\n    chart: outliersView,\n    options: {\n      xField,\n      yField: outliersField,\n      point: { shape: 'circle', style: outliersStyle },\n      label,\n    },\n  });\n\n  outliersView.axis(false);\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<BoxOptions>): Params<BoxOptions> {\n  const { chart, options } = params;\n  const { meta, xAxis, yAxis, xField, yField, outliersField } = options;\n  const yFieldName = Array.isArray(yField) ? BOX_RANGE : yField;\n\n  let baseMeta = {};\n\n  // make yField and outliersField share y mate\n  if (outliersField) {\n    const syncName = BOX_SYNC_NAME;\n    baseMeta = {\n      [outliersField]: { sync: syncName, nice: true },\n      [yFieldName]: { sync: syncName, nice: true },\n    };\n  }\n\n  const scales = deepAssign(baseMeta, meta, {\n    [xField]: pick(xAxis, AXIS_META_CONFIG_KEYS),\n    [yFieldName]: pick(yAxis, AXIS_META_CONFIG_KEYS),\n  });\n\n  chart.scale(scales);\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<BoxOptions>): Params<BoxOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n  const yFieldName = Array.isArray(yField) ? BOX_RANGE : yField;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(BOX_RANGE, false);\n  } else {\n    chart.axis(yFieldName, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<BoxOptions>): Params<BoxOptions> {\n  const { chart, options } = params;\n  const { legend, groupField } = options;\n\n  if (groupField) {\n    if (legend) {\n      chart.legend(groupField, legend);\n    } else {\n      // Grouped Box Chart default has legend, and it's position is `bottom`\n      chart.legend(groupField, { position: 'bottom' });\n    }\n  } else {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * 箱型图适配器\n * @param params\n */\nexport function adaptor(params: Params<BoxOptions>) {\n  return flow(field, outliersPoint, meta, axis, legend, tooltip, annotation(), interaction, animation, theme)(params);\n}\n"]}