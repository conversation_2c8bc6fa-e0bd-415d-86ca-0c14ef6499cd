{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/area/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACxC,OAAO,EACL,SAAS,EACT,UAAU,EACV,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,KAAK,EACL,OAAO,EACP,eAAe,GAChB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAG7D,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC7E,OAAO,EAAE,wBAAwB,EAAE,MAAM,+BAA+B,CAAC;AACzE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AAGrD,OAAO,EAAE,IAAI,EAAE,CAAC;AAEhB;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA2B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAEhC,IAAA,IAAI,GAYF,OAAO,KAZL,EACJ,SAAS,GAWP,OAAO,UAXA,EACT,SAAS,GAUP,OAAO,UAVA,EACT,KAAK,GASH,OAAO,MATJ,EACE,YAAY,GAQjB,OAAO,MARU,EACb,WAAW,GAOf,OAAO,KAPQ,EACjB,SAAS,GAMP,OAAO,UANA,EACT,MAAM,GAKJ,OAAO,OALH,EACN,MAAM,GAIJ,OAAO,OAJH,EACN,OAAO,GAGL,OAAO,QAHF,EACP,WAAW,GAET,OAAO,YAFE,EACX,WAAW,GACT,OAAO,YADE,CACD;IACZ,IAAM,UAAU,GAAG,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,CAAC;IAEvC,IAAM,SAAS,GAAG,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IACpF,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACtB,gCAAgC;IAChC,IAAM,cAAc,GAAG,SAAS;QAC9B,CAAC,YACG,SAAS,EAAE,UAAC,KAAY,IAAK,OAAA,CAAC;gBAC5B,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;gBACzC,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;aACtD,CAAC,EAH2B,CAG3B,IACC,OAAO,EAEd,CAAC,CAAC,OAAO,CAAC;IACZ,IAAM,OAAO,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrC,OAAO,EAAE;YACP,IAAI,EAAE;gBACJ,KAAK,OAAA;gBACL,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,SAAS;aACjB;YACD,KAAK,EAAE,YAAY,eACjB,KAAK,OAAA,IACF,YAAY,CAChB;YACD,OAAO,EAAE,cAAc;YACvB,mDAAmD;YACnD,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE;gBACJ,WAAW,aAAA;aACZ;SACF;KACF,CAAC,CAAC;IACH,kDAAkD;IAClD,IAAM,UAAU,GAAG;QACjB,KAAK,OAAA;QACL,OAAO,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAc,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE;YAC1E,sCAAsC;YACtC,gBAAgB;YAChB,IAAI,EAAE,WAAW,eACf,KAAK,OAAA,IACF,WAAW,CACf;YACD,SAAS,EAAE,WAAW;YACtB,KAAK,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK;YACzB,OAAO,EAAE,KAAK;YACd,mDAAmD;YACnD,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE;gBACJ,WAAW,aAAA;aACZ;SACF,CAAC;KACH,CAAC;IACF,IAAM,WAAW,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IAEhG,mBAAmB;IACnB,IAAI,CAAC,OAAO,CAAC,CAAC;IACd,IAAI,CAAC,UAAU,CAAC,CAAC;IACjB,KAAK,CAAC,WAAW,CAAC,CAAC;IAEnB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA2B;IAChC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAM,YAAY,GAAG,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAEjD,8BAA8B;IAC9B,IAAI,CAAC,KAAK,EAAE;QACV,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC3B;SAAM;QACG,IAAA,MAAM,GAAuB,KAAK,OAA5B,EAAE,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAApC,sBAA4B,CAAF,CAAW;QAC3C,YAAY,CAAC,KAAK,CAAC;YACjB,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;YAC1B,QAAQ,UAAA;YACR,GAAG,aACD,MAAM,EAAE;oBACN,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;iBACnD,IACE,cAAc,CAAC,GAAG,CAAC,CACvB;SACF,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA2B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAA6B,OAAO,QAApC,EAAE,SAAS,GAAkB,OAAO,UAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IACpD,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,WAAW,EAAE;QACzC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,UAAC,CAAW;YACjC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA2B;IACjD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,OAAO,CAAC,WAAW,CAAC,EACpB,eAAe,CAAC,MAAM,CAAC,EACvB,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,UAAU,EAAE,EACZ,WAAW,EACX,SAAS,EACT,WAAW,CACZ,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { each, omit } from '@antv/util';\nimport {\n  animation,\n  annotation,\n  interaction,\n  limitInPlot,\n  pattern,\n  slider,\n  theme,\n  tooltip,\n  transformations,\n} from '../../adaptor/common';\nimport { area, line, point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport { deepAssign, findGeometry, flow, transformLabel } from '../../utils';\nimport { getDataWhetherPercentage } from '../../utils/transform/percent';\nimport { axis, legend, meta } from '../line/adaptor';\nimport { AreaOptions } from './types';\n\nexport { meta };\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<AreaOptions>): Params<AreaOptions> {\n  const { chart, options } = params;\n  const {\n    data,\n    areaStyle,\n    areaShape,\n    color,\n    point: pointMapping,\n    line: lineMapping,\n    isPercent,\n    xField,\n    yField,\n    tooltip,\n    seriesField,\n    startOnZero,\n  } = options;\n  const pointState = pointMapping?.state;\n\n  const chartData = getDataWhetherPercentage(data, yField, xField, yField, isPercent);\n  chart.data(chartData);\n  // 百分比堆积图，默认会给一个 % 格式化逻辑, 用户可自定义\n  const tooltipOptions = isPercent\n    ? {\n        formatter: (datum: Datum) => ({\n          name: datum[seriesField] || datum[xField],\n          value: (Number(datum[yField]) * 100).toFixed(2) + '%',\n        }),\n        ...tooltip,\n      }\n    : tooltip;\n  const primary = deepAssign({}, params, {\n    options: {\n      area: {\n        color,\n        style: areaStyle,\n        shape: areaShape,\n      },\n      point: pointMapping && {\n        color,\n        ...pointMapping,\n      },\n      tooltip: tooltipOptions,\n      // label 不传递给各个 geometry adaptor，由 label adaptor 处理\n      label: undefined,\n      args: {\n        startOnZero,\n      },\n    },\n  });\n  // 线默认 2px (折线不能复用面积图的 state，因为 fill 和 stroke 不匹配)\n  const lineParams = {\n    chart,\n    options: deepAssign({ line: { size: 2 } }, omit(options as any, ['state']), {\n      // 颜色保持一致，因为如果颜色不一致，会导致 tooltip 中元素重复。\n      // 如果存在，才设置，否则为空\n      line: lineMapping && {\n        color,\n        ...lineMapping,\n      },\n      sizeField: seriesField,\n      state: lineMapping?.state,\n      tooltip: false,\n      // label 不传递给各个 geometry adaptor，由 label adaptor 处理\n      label: undefined,\n      args: {\n        startOnZero,\n      },\n    }),\n  };\n  const pointParams = deepAssign({}, primary, { options: { tooltip: false, state: pointState } });\n\n  // area geometry 处理\n  area(primary);\n  line(lineParams);\n  point(pointParams);\n\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<AreaOptions>): Params<AreaOptions> {\n  const { chart, options } = params;\n  const { label, yField } = options;\n\n  const areaGeometry = findGeometry(chart, 'area');\n\n  // label 为 false, 空 则不显示 label\n  if (!label) {\n    areaGeometry.label(false);\n  } else {\n    const { fields, callback, ...cfg } = label;\n    areaGeometry.label({\n      fields: fields || [yField],\n      callback,\n      cfg: {\n        layout: [\n          { type: 'limit-in-plot' },\n          { type: 'path-adjust-position' },\n          { type: 'point-adjust-position' },\n          { type: 'limit-in-plot', cfg: { action: 'hide' } },\n        ],\n        ...transformLabel(cfg),\n      },\n    });\n  }\n\n  return params;\n}\n\n/**\n * 处理 adjust\n * @param params\n */\nfunction adjust(params: Params<AreaOptions>): Params<AreaOptions> {\n  const { chart, options } = params;\n  const { isStack, isPercent, seriesField } = options;\n  if ((isPercent || isStack) && seriesField) {\n    each(chart.geometries, (g: Geometry) => {\n      g.adjust('stack');\n    });\n  }\n\n  return params;\n}\n\n/**\n * 折线图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<AreaOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    pattern('areaStyle'),\n    transformations('rect'),\n    geometry,\n    meta,\n    adjust,\n    axis,\n    legend,\n    tooltip,\n    label,\n    slider,\n    annotation(),\n    interaction,\n    animation,\n    limitInPlot\n  )(params);\n}\n"]}