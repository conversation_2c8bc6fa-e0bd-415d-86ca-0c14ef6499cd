{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/gauge/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gBAAgB,CAAC;AAEvD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,kBAAkB,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAEjH,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAEzD;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA4B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GACb,OAAO,QADM,EAAE,KAAK,GACpB,OAAO,MADa,EAAE,MAAM,GAC5B,OAAO,OADqB,EAAE,WAAW,GACzC,OAAO,YADkC,EAAE,UAAU,GACrD,OAAO,WAD8C,EAAE,QAAQ,GAC/D,OAAO,SADwD,EAAE,IAAI,GACrE,OAAO,KAD8D,EAAE,SAAS,GAChF,OAAO,UADyE,EAAE,UAAU,GAC5F,OAAO,WADqF,EAAE,IAAI,GAClG,OAAO,KAD2F,EAAE,KAAK,GACzG,OAAO,MADkG,CACjG;IACF,IAAA,KAAK,GAAwB,KAAK,MAA7B,EAAS,UAAU,GAAK,KAAK,MAAV,CAAW;IAE3C,UAAU;IACV,UAAU;IACV,IAAI,SAAS,EAAE;QACb,IAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,CAAC,CAAC;QACxD,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEvB,EAAE,CAAC,KAAK,EAAE;aACP,QAAQ,CAAC,UAAG,OAAO,OAAI,CAAC;aACxB,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,iBAAiB,CAAC;YAC5C,sBAAsB;aACrB,UAAU,CAAC;YACV,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,YAAY;YAC3C,SAAS,WAAA;SACV,CAAC,CAAC;QAEL,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,UAAU,YAAA;YACV,QAAQ,UAAA;YACR,MAAM,EAAE,WAAW,GAAG,MAAM,EAAE,wCAAwC;SACvE,CAAC,CAAC;QAEH,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvB,iBAAiB;QACjB,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC,CAAC;KACtD;IAED,WAAW;IACX,8CAA8C;IAC9C,IAAM,SAAS,GAAG,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;IACvD,IAAM,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IACnD,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEnB,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAE5D,IAAA,GAAG,GAAK,QAAQ,CAAC;QACvB,KAAK,EAAE,EAAE;QACT,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,WAAW;YACnB,WAAW,EAAE,UAAU;YACvB,SAAS,EAAE,CAAC,OAAO,CAAC;YACpB,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE;gBACR,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;aAC/C;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,IAAI;aACjB;YACD,cAAc,EAAE,UAAU;YAC1B,cAAc,EAAE,UAAU;SAC3B;KACF,CAAC,IApBS,CAoBR;IAEH,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAoB,CAAC;IAC1C,iBAAiB;IACjB,QAAQ,CAAC,UAAU,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;IAE/B,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE;QACrB,WAAW,aAAA;QACX,MAAM,QAAA;QACN,UAAU,YAAA;QACV,QAAQ,UAAA;KACT,CAAC,CAAC,SAAS,EAAE,CAAC;IAEf,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA4B;;IACxC,OAAO,IAAI,CACT,KAAK;YACH,KAAK,EAAE;gBACL,GAAG,EAAE,CAAC;gBACN,GAAG,EAAE,CAAC;gBACN,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;aACZ;;QACD,GAAC,OAAO,IAAG,EAAE;YACb,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,SAAS,CAAC,MAA4B,EAAE,OAAiB;IACxD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAc,OAAO,UAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEvC,cAAc;IACd,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC9C,IAAI,SAAS,EAAE;QACL,IAAS,aAAa,GAAK,SAAS,QAAd,CAAe;QAC7C,IAAI,gBAAgB,SAAA,CAAC;QACrB,yBAAyB;QACzB,IAAI,aAAa,EAAE;YACjB,gBAAgB,GAAG,UAAU,CAC3B,EAAE,EACF;gBACE,OAAO,EAAE,UAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG;gBACzC,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,MAAM;oBAChB,UAAU,EAAE,CAAC;oBACb,SAAS,EAAE,QAAQ;oBACnB,KAAK,EAAE,qBAAqB;iBAC7B;aACF,EACD,aAAa,CACd,CAAC;SACH;QACD,oBAAoB,CAAC,KAAK,EAAE,EAAE,SAAS,wBAAO,SAAS,KAAE,OAAO,EAAE,gBAAgB,GAAE,EAAE,EAAE,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;KACtG;IAED,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,OAAO,CAAC,MAA4B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,CACX,UAAU,CACR;YACE,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,mEAAmE;YACjF,SAAS,EAAE;gBACT,YAAY,EAAE;oBACZ,OAAO,EAAE,SAAS;oBAClB,QAAQ,EAAE,MAAM;iBACjB;aACF;YACD,aAAa,EAAE,UAAC,CAAS,EAAE,IAAW;gBACpC,IAAM,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnD,OAAO,UAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAG,CAAC;YAC1C,CAAC;SACF,EACD,OAAO,CACR,CACF,CAAC;KACH;SAAM;QACL,iBAAiB;QACjB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA4B;IACjC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,OAAO,EAAE,SAAS,EAAE,CAAC;AAErB;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAClD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK;IACL,+CAA+C;IAC/C,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,SAAS,EACT,WAAW,EACX,UAAU,EAAE,EACZ,KAAK;IACL,uBAAuB;KACxB,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { get, isString } from '@antv/util';\nimport { animation, annotation, interaction, scale, theme } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { AXIS_META_CONFIG_KEYS } from '../../constant';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow, pick, renderGaugeStatistic } from '../../utils';\nimport { DEFAULT_COLOR, INDICATEOR_VIEW_ID, PERCENT, RANGE_TYPE, RANGE_VALUE, RANGE_VIEW_ID } from './constants';\nimport { GaugeOptions } from './types';\nimport { getIndicatorData, getRangeData } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<GaugeOptions>): Params<GaugeOptions> {\n  const { chart, options } = params;\n  const { percent, range, radius, innerRadius, startAngle, endAngle, axis, indicator, gaugeStyle, type, meter } =\n    options;\n  const { color, width: rangeWidth } = range;\n\n  // 指标 & 指针\n  // 如果开启在应用\n  if (indicator) {\n    const indicatorData = getIndicatorData(percent);\n\n    const v1 = chart.createView({ id: INDICATEOR_VIEW_ID });\n    v1.data(indicatorData);\n\n    v1.point()\n      .position(`${PERCENT}*1`)\n      .shape(indicator.shape || 'gauge-indicator')\n      // 传入指针的样式到自定义 shape 中\n      .customInfo({\n        defaultColor: chart.getTheme().defaultColor,\n        indicator,\n      });\n\n    v1.coordinate('polar', {\n      startAngle,\n      endAngle,\n      radius: innerRadius * radius, // 外部的 innerRadius * radius = 这里的 radius\n    });\n\n    v1.axis(PERCENT, axis);\n    // 一部分应用到 scale 中\n    v1.scale(PERCENT, pick(axis, AXIS_META_CONFIG_KEYS));\n  }\n\n  // 辅助 range\n  // [{ range: 1, type: '0', percent: 原始进度百分比 }]\n  const rangeData = getRangeData(percent, options.range);\n  const v2 = chart.createView({ id: RANGE_VIEW_ID });\n  v2.data(rangeData);\n\n  const rangeColor = isString(color) ? [color, DEFAULT_COLOR] : color;\n\n  const { ext } = interval({\n    chart: v2,\n    options: {\n      xField: '1',\n      yField: RANGE_VALUE,\n      seriesField: RANGE_TYPE,\n      rawFields: [PERCENT],\n      isStack: true,\n      interval: {\n        color: rangeColor,\n        style: gaugeStyle,\n        shape: type === 'meter' ? 'meter-gauge' : null,\n      },\n      args: {\n        zIndexReversed: true,\n        sortZIndex: true,\n      },\n      minColumnWidth: rangeWidth,\n      maxColumnWidth: rangeWidth,\n    },\n  });\n\n  const geometry = ext.geometry as Geometry;\n  // 传入到自定义 shape 中\n  geometry.customInfo({ meter });\n\n  v2.coordinate('polar', {\n    innerRadius,\n    radius,\n    startAngle,\n    endAngle,\n  }).transpose();\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nfunction meta(params: Params<GaugeOptions>): Params<GaugeOptions> {\n  return flow(\n    scale({\n      range: {\n        min: 0,\n        max: 1,\n        maxLimit: 1,\n        minLimit: 0,\n      },\n      [PERCENT]: {},\n    })\n  )(params);\n}\n\n/**\n * 统计指标文档\n * @param params\n */\nfunction statistic(params: Params<GaugeOptions>, updated?: boolean): Params<GaugeOptions> {\n  const { chart, options } = params;\n  const { statistic, percent } = options;\n\n  // 先清空标注，再重新渲染\n  chart.getController('annotation').clear(true);\n  if (statistic) {\n    const { content: contentOption } = statistic;\n    let transformContent;\n    // 当设置 content 的时候，设置默认样式\n    if (contentOption) {\n      transformContent = deepAssign(\n        {},\n        {\n          content: `${(percent * 100).toFixed(2)}%`,\n          style: {\n            opacity: 0.75,\n            fontSize: '30px',\n            lineHeight: 1,\n            textAlign: 'center',\n            color: 'rgba(44,53,66,0.85)',\n          },\n        },\n        contentOption\n      );\n    }\n    renderGaugeStatistic(chart, { statistic: { ...statistic, content: transformContent } }, { percent });\n  }\n\n  if (updated) {\n    chart.render(true);\n  }\n\n  return params;\n}\n\n/**\n * tooltip 配置\n */\nfunction tooltip(params: Params<GaugeOptions>): Params<GaugeOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  if (tooltip) {\n    chart.tooltip(\n      deepAssign(\n        {\n          showTitle: false,\n          showMarkers: false,\n          containerTpl: '<div class=\"g2-tooltip\"><div class=\"g2-tooltip-list\"></div></div>',\n          domStyles: {\n            'g2-tooltip': {\n              padding: '4px 8px',\n              fontSize: '10px',\n            },\n          },\n          customContent: (x: string, data: any[]) => {\n            const percent = get(data, [0, 'data', PERCENT], 0);\n            return `${(percent * 100).toFixed(2)}%`;\n          },\n        },\n        tooltip\n      )\n    );\n  } else {\n    // 默认，不展示 tooltip\n    chart.tooltip(false);\n  }\n\n  return params;\n}\n\n/**\n * other 配置\n * @param params\n */\nfunction other(params: Params<GaugeOptions>): Params<GaugeOptions> {\n  const { chart } = params;\n\n  chart.legend(false);\n\n  return params;\n}\n\n/**\n * 对外暴露的 adaptor\n */\nexport { statistic };\n\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<GaugeOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    // animation 配置必须在 createView 之前，不然无法让子 View 生效\n    animation,\n    geometry,\n    meta,\n    tooltip,\n    statistic,\n    interaction,\n    annotation(),\n    other\n    // ... 其他的 adaptor flow\n  )(params);\n}\n"]}