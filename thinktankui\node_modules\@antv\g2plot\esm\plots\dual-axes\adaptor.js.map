{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/dual-axes/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC7F,OAAO,EACL,SAAS,IAAI,eAAe,EAC5B,UAAU,IAAI,gBAAgB,EAC9B,WAAW,IAAI,iBAAiB,EAChC,WAAW,IAAI,iBAAiB,EAChC,KAAK,EACL,KAAK,IAAI,WAAW,GACrB,MAAM,sBAAsB,CAAC;AAG9B,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAmB,MAAM,SAAS,CAAC;AACtE,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACnD,OAAO,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AACzG,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD;;;;;;;;;GASG;AACH,MAAM,UAAU,gBAAgB,CAAC,MAA+B;;IACtD,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAyC,OAAO,gBAA5B,EAApB,eAAe,mBAAG,EAAE,KAAA,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IACzD,IAAM,OAAO,GAAG,KAAK,CACnB,eAAe,EACf,UAAC,EAAY;YAAV,QAAQ,cAAA;QAAO,OAAA,QAAQ,KAAK,gBAAgB,CAAC,IAAI,IAAI,QAAQ,KAAK,SAAS;IAA5D,CAA4D,CAC/E,CAAC;IACF,OAAO,UAAU,CACf,EAAE,EACF;QACE,OAAO,EAAE;YACP,eAAe,EAAE,EAAE;YACnB,IAAI;gBACF,GAAC,MAAM,IAAG;oBACR,aAAa;oBACb,IAAI,EAAE,KAAK;oBACX,mBAAmB;oBACnB,IAAI,EAAE,IAAI;oBACV,YAAY;oBACZ,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBACpC;mBACF;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,OAAO;gBACpB,uBAAuB;gBACvB,cAAc,EAAE,OAAO;gBACvB,MAAM,EAAE,IAAI;gBACZ,UAAU,EAAE;oBACV,IAAI,EAAE,GAAG;iBACV;aACF;YACD,YAAY,EAAE,CAAC,OAAO;gBACpB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;gBAChE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC;YACvC,MAAM,EAAE;gBACN,QAAQ,EAAE,UAAU;aACrB;SACF;KACF,EACD,MAAM,EACN;QACE,OAAO,EAAE;YACP,QAAQ;YACR,KAAK,EAAE,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;YACpD,kBAAkB;YAClB,eAAe,EAAE;gBACf,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;gBACxD,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;aACzD;YACD,cAAc;YACd,WAAW,EAAE,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC;SACjE;KACF,CACF,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,MAA+B;;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,eAAe,GAAK,OAAO,gBAAZ,CAAa;IAEpC,IAAM,QAAQ,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IAExC,gBAAgB;IAChB,IAAM,UAAU,GAAG;QACjB,EAAE,IAAI,EAAE,MAAA,eAAe,CAAC,CAAC,CAAC,0CAAE,QAAQ,EAAE,EAAE,EAAE,cAAc,EAAE;QAC1D,EAAE,IAAI,EAAE,MAAA,eAAe,CAAC,CAAC,CAAC,0CAAE,QAAQ,EAAE,EAAE,EAAE,eAAe,EAAE;KAC5D,CAAC;IAEF,+BAA+B;IAC/B,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAApC,CAAoC,CAAC,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,KAAK,CAAC,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAA9B,CAA8B,CAAC,CAAC;IAE/G,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA+B;IACvC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA6C,OAAO,OAApD,EAAE,MAAM,GAAqC,OAAO,OAA5C,EAAE,eAAe,GAAoB,OAAO,gBAA3B,EAAE,IAAI,GAAc,OAAO,KAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEnE,gBAAgB;IAChB,IAAM,UAAU,GAAG;8BACZ,eAAe,CAAC,CAAC,CAAC,KAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;8BACxE,eAAe,CAAC,CAAC,CAAC,KAAE,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;KAC/E,CAAC;IAEF,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ;QAClB,IAAA,EAAE,GAAmB,QAAQ,GAA3B,EAAE,IAAI,GAAa,QAAQ,KAArB,EAAE,MAAM,GAAK,QAAQ,OAAb,CAAc;QACtC,mBAAmB;QACnB,IAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC;QAC3D,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5E,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtD,IAAM,cAAc,GAAG,SAAS;YAC9B,CAAC,YACG,SAAS,EAAE,UAAC,KAAY,IAAK,OAAA,CAAC;oBAC5B,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM;oBAC3C,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;iBACtD,CAAC,EAH2B,CAG3B,IACC,OAAO,EAEd,CAAC,CAAC,OAAO,CAAC;QAEZ,OAAO;QACP,kBAAkB,CAAC;YACjB,KAAK,EAAE,IAAI;YACX,OAAO,EAAE;gBACP,MAAM,QAAA;gBACN,MAAM,QAAA;gBACN,OAAO,EAAE,cAAc;gBACvB,cAAc,EAAE,QAAQ;aACzB;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,MAA+B;;IAC3C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,eAAe,GAAK,OAAO,gBAAZ,CAAa;IACpC,IAAM,UAAU,GAAG,CAAA,MAAA,KAAK,CAAC,QAAQ,EAAE,0CAAE,QAAQ,KAAI,EAAE,CAAC;IAEpD,IAAI,KAAK,GAAG,CAAC,CAAC;IACd;;;;;;OAMG;IACH,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE;QACxB,IAAI,CAAC,eAAe,EAAE,UAAC,cAAc,EAAE,KAAK;YAC1C,IAAM,IAAI,GAAG,YAAY,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;YACjF,IAAI,cAAc,CAAC,KAAK;gBAAE,OAAO;YACjC,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACzC,IAAM,KAAK,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1D,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;YAC3F,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,QAAQ;gBAC/B,IAAI,cAAc,CAAC,WAAW,EAAE;oBAC9B,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBACnD;qBAAM;oBACL,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC1B;YACH,CAAC,CAAC,CAAC;YACH,KAAK,IAAI,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,CAAC,CAAC,CAAC,IAAG,KAAK,CAAC,CAAC,CAAC;YACrB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IAE3E,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,CAAC,CAAC,CAAC,IAAG,KAAK,CAAC,CAAC,CAAC;YACrB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA+B;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAClC,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACrD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAC/C,IAAA,MAAM,GAA2B,OAAO,OAAlC,EAAE,MAAM,GAAmB,OAAO,OAA1B,EAAE,KAAK,GAAY,OAAO,MAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAEjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC7B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAE7B,SAAS;IACT,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAEvE,QAAQ;IACR,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9B,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;IAEzE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IAC7C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAC5B,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACrD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvD,iDAAiD;IACjD,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvB,+DAA+D;IAC/D,2CAA2C;IAC3C,QAAQ,CAAC,OAAO,CAAC;QACf,MAAM,EAAE,IAAI;KACb,CAAC,CAAC;IACH,SAAS,CAAC,OAAO,CAAC;QAChB,MAAM,EAAE,IAAI;KACb,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAA+B;IACjD,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,iBAAiB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1F,iBAAiB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IAE3F,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,MAA+B;IAChD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAa;IAEhC,IAAM,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,IAAM,EAAE,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAEjC,gBAAgB,CAAC,EAAE,CAAC,CAClB,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC;QAC1C,OAAO,EAAE;YACP,WAAW,EAAE,EAAE;SAChB;KACF,CAAC,CACH,CAAC;IACF,gBAAgB,CAAC,EAAE,CAAC,CAClB,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC;QAC3C,OAAO,EAAE;YACP,WAAW,EAAE,EAAE;SAChB;KACF,CAAC,CACH,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,MAA+B;IAC3C,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB;;;OAGG;IACH,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACpF,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACrF,WAAW,CAAC,MAAM,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,MAA+B;IAC/C,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACxF,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IAEzF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAA+B;IACjD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1B,iBAAiB,CACf,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC;QAC1C,OAAO,EAAE;YACP,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SAChB;KACF,CAAC,CACH,CAAC;IAEF,iBAAiB,CACf,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC;QAC3C,OAAO,EAAE;YACP,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;SAChB;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,MAAM,CAAC,MAA+B;IAC5C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAoC,OAAO,OAA3C,EAAE,eAAe,GAAmB,OAAO,gBAA1B,EAAE,MAAM,GAAW,OAAO,OAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAC1D,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACrD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAEvD,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;QACrD,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KACtB;SAAM;QACL,IAAM,YAAU,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAM,aAAW,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;QAChE,WAAW;QACX,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE;YACxB,IAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;gBAC9B,CAAC,CAAC,kBAAkB,CAAC;oBACjB,IAAI,EAAE,QAAQ;oBACd,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;oBAClC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;oBACjB,MAAM,EAAE,YAAU;iBACnB,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC;YAEP,IAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;gBAC/B,CAAC,CAAC,kBAAkB,CAAC;oBACjB,IAAI,EAAE,SAAS;oBACf,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;oBAClC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;oBACjB,MAAM,EAAE,aAAW;iBACpB,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC;YAEP,KAAK,CAAC,MAAM,CACV,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;gBACrB,MAAM,EAAE,IAAI;gBACZ,cAAc;gBACd,aAAa;gBACb,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;aACpC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;YAClC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,YAAU,CAAC,CAAC;SAC7D;QACD,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;YAClC,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,aAAW,CAAC,CAAC;SAC/D;QAED,UAAU;QACV,KAAK,CAAC,EAAE,CAAC,mBAAmB,EAAE,UAAC,GAAG;YAChC,IAAM,cAAc,GAAG,GAAG,CAAC,GAAG,EAAE,uBAAuB,EAAE,EAAE,CAAC,CAAC;YAC7D,IAAI,cAAc,IAAI,cAAc,CAAC,IAAI,EAAE;gBACnC,IAAA,KAAuC,cAAc,CAAC,IAAI,EAAjD,OAAK,WAAA,EAAE,UAAU,gBAAA,EAAE,MAAM,YAAwB,CAAC;gBACjE,uCAAuC;gBACvC,IAAI,UAAU,EAAE;oBACd,IAAM,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,UAAC,EAAU,IAAK,OAAA,EAAE,KAAK,OAAK,EAAZ,CAAY,CAAC,CAAC;oBAC5D,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;wBACZ,IAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC;wBAClE,IAAI,CAAC,UAAU,EAAE,UAAC,CAAC;4BACjB,CAAC,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAClD,CAAC,CAAC,CAAC;qBACJ;iBACF;qBAAM;oBACL,IAAM,YAAU,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;oBAC1E,QAAQ;oBACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,IAAI;wBACrB,OAAO;wBACP,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;wBACzC,IAAI,CAAC,UAAU,EAAE,UAAC,KAAY;4BAC5B,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAK,CAAC,GAAG,CAAC,CAAC,EAAE;gCACpD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,UAAC,KAAK;oCAC7B,IAAM,aAAa,GAAqB,IAAI,CAC1C,YAAU,EACV,UAAC,IAAsB,IAAK,OAAA,IAAI,CAAC,KAAK,KAAK,KAAK,EAApB,CAAoB,CACjD,CAAC;oCACF,wCAAwC;oCACxC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC;gCAClC,CAAC,CAAC,CAAC;6BACJ;wBACH,CAAC,CAAC,CAAC;wBACH,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;iBACJ;aACF;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA+B;IAC5C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IAC3B,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;IACrD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvD,IAAI,MAAM,EAAE;QACV,SAAS;QACT,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAClC,iCAAiC;QACjC,QAAQ,CAAC,EAAE,CAAC,qBAAqB,EAAE,UAAC,GAAU;YAE1C,IAAA,KACE,GAAG,MADwB,EAApB,KAAK,WAAA,EAAE,WAAW,iBAAE,CACvB;YACR,IAAI,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;gBAC/B,OAAO;aACR;YACD,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;YACvB,mBAAmB;YACnB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;gBACd,IAAA,KAAK,GAAU,MAAM,MAAhB,EAAE,GAAG,GAAK,MAAM,IAAX,CAAY;gBAC9B,IAAI,KAAK,IAAI,GAAG,EAAE;oBAChB,cAAc,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;iBACzC;aACF;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA+B;IACrD,oEAAoE;IACpE,OAAO,IAAI,CACT,gBAAgB,EAChB,WAAW;IACX,iBAAiB;IACjB,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,OAAO,EACP,WAAW,EACX,UAAU,EACV,SAAS,EACT,KAAK,EACL,MAAM,EACN,MAAM,CACP,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Event, Scale, Types } from '@antv/g2';\nimport { each, every, find, findIndex, get, isBoolean, isEqual, isObject } from '@antv/util';\nimport {\n  animation as commonAnimation,\n  annotation as commonAnnotation,\n  interaction as commonInteraction,\n  limitInPlot as commonLimitInPlot,\n  scale,\n  theme as commonTheme,\n} from '../../adaptor/common';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport { deepAssign, flow } from '../../utils';\nimport { percent } from '../../utils/transform/percent';\nimport { findViewById } from '../../utils/view';\nimport { LEFT_AXES_VIEW, RIGHT_AXES_VIEW } from './constant';\nimport { AxisType, DualAxesGeometry, DualAxesOptions } from './types';\nimport { drawSingleGeometry } from './util/geometry';\nimport { getViewLegendItems } from './util/legend';\nimport { getGeometryOption, getYAxisWithDefault, isColumn, transformObjectToArray } from './util/option';\nimport { doSliderFilter } from './util/render-sider';\n\n/**\n * transformOptions，双轴图整体的取参逻辑如下\n * 1. get index getOptions: 对应的是默认的图表参数，如 appendPadding，syncView 等\n * 2. get adpator transformOption: 对应的是双轴图的默认参数，deepAssign 优先级从低到高如下\n *    2.1 defaultoption，如 tooltip，legend\n *    2.2 用户填写 options\n *    2.3 根据用户填写的 options 补充的数组型 options，如 yaxis，GeometryOption，因为 deepAssign 无法 assign 数组\n *\n * @param params\n */\nexport function transformOptions(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { options } = params;\n  const { geometryOptions = [], xField, yField } = options;\n  const allLine = every(\n    geometryOptions,\n    ({ geometry }) => geometry === DualAxesGeometry.Line || geometry === undefined\n  );\n  return deepAssign(\n    {},\n    {\n      options: {\n        geometryOptions: [],\n        meta: {\n          [xField]: {\n            // 默认为 cat 类型\n            type: 'cat',\n            // x 轴一定是同步 scale 的\n            sync: true,\n            // 如果有没有柱子，则\n            range: allLine ? [0, 1] : undefined,\n          },\n        },\n        tooltip: {\n          showMarkers: allLine,\n          // 存在柱状图，不显示 crosshairs\n          showCrosshairs: allLine,\n          shared: true,\n          crosshairs: {\n            type: 'x',\n          },\n        },\n        interactions: !allLine\n          ? [{ type: 'legend-visible-filter' }, { type: 'active-region' }]\n          : [{ type: 'legend-visible-filter' }],\n        legend: {\n          position: 'top-left',\n        },\n      },\n    },\n    params,\n    {\n      options: {\n        // yAxis\n        yAxis: transformObjectToArray(yField, options.yAxis),\n        // geometryOptions\n        geometryOptions: [\n          getGeometryOption(xField, yField[0], geometryOptions[0]),\n          getGeometryOption(xField, yField[1], geometryOptions[1]),\n        ],\n        // annotations\n        annotations: transformObjectToArray(yField, options.annotations),\n      },\n    }\n  );\n}\n\n/**\n * 创建 双轴图 中绘制图形的 view，提前创建是因为 theme 适配器的需要\n * @param params\n */\nfunction createViews(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { geometryOptions } = options;\n\n  const SORT_MAP = { line: 0, column: 1 };\n\n  // 包含配置，id，数据的结构\n  const geometries = [\n    { type: geometryOptions[0]?.geometry, id: LEFT_AXES_VIEW },\n    { type: geometryOptions[1]?.geometry, id: RIGHT_AXES_VIEW },\n  ];\n\n  // 将线的 view 放置在更上一层，防止线柱遮挡。先柱后先\n  geometries.sort((a, b) => -SORT_MAP[a.type] + SORT_MAP[b.type]).forEach((g) => chart.createView({ id: g.id }));\n\n  return params;\n}\n\n/**\n * 绘制图形\n * @param params\n */\nfunction geometry(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { xField, yField, geometryOptions, data, tooltip } = options;\n\n  // 包含配置，id，数据的结构\n  const geometries = [\n    { ...geometryOptions[0], id: LEFT_AXES_VIEW, data: data[0], yField: yField[0] },\n    { ...geometryOptions[1], id: RIGHT_AXES_VIEW, data: data[1], yField: yField[1] },\n  ];\n\n  geometries.forEach((geometry) => {\n    const { id, data, yField } = geometry;\n    // 百分比柱状图需要额外处理一次数据\n    const isPercent = isColumn(geometry) && geometry.isPercent;\n    const formatData = isPercent ? percent(data, yField, xField, yField) : data;\n    const view = findViewById(chart, id).data(formatData);\n\n    const tooltipOptions = isPercent\n      ? {\n          formatter: (datum: Datum) => ({\n            name: datum[geometry.seriesField] || yField,\n            value: (Number(datum[yField]) * 100).toFixed(2) + '%',\n          }),\n          ...tooltip,\n        }\n      : tooltip;\n\n    // 绘制图形\n    drawSingleGeometry({\n      chart: view,\n      options: {\n        xField,\n        yField,\n        tooltip: tooltipOptions,\n        geometryOption: geometry,\n      },\n    });\n  });\n  return params;\n}\n\nexport function color(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { geometryOptions } = options;\n  const themeColor = chart.getTheme()?.colors10 || [];\n\n  let start = 0;\n  /* 为 geometry 添加默认 color。\n   * 1. 若 geometryOptions 存在 color，则在 drawGeometry 时已处理\n   * 2. 若 不存在 color，获取 Geometry group scales个数，在 theme color 10 中提取\n   * 3. 为防止 group 过多导致右色板无值或值很少，右 view 面板在依次提取剩下的 N 个 后再 concat 一次 themeColor\n   * 4. 为简便获取 Geometry group scales个数，在绘制完后再执行 color\n   * 5. 考虑之后将不同 view 使用同一个色板的需求沉淀到 g2\n   */\n  chart.once('beforepaint', () => {\n    each(geometryOptions, (geometryOption, index) => {\n      const view = findViewById(chart, index === 0 ? LEFT_AXES_VIEW : RIGHT_AXES_VIEW);\n      if (geometryOption.color) return;\n      const groupScale = view.getGroupScales();\n      const count = get(groupScale, [0, 'values', 'length'], 1);\n      const color = themeColor.slice(start, start + count).concat(index === 0 ? [] : themeColor);\n      view.geometries.forEach((geometry) => {\n        if (geometryOption.seriesField) {\n          geometry.color(geometryOption.seriesField, color);\n        } else {\n          geometry.color(color[0]);\n        }\n      });\n      start += count;\n    });\n    chart.render(true);\n  });\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  scale({\n    [xField]: xAxis,\n    [yField[0]]: yAxis[0],\n  })(deepAssign({}, params, { chart: findViewById(chart, LEFT_AXES_VIEW) }));\n\n  scale({\n    [xField]: xAxis,\n    [yField[1]]: yAxis[1],\n  })(deepAssign({}, params, { chart: findViewById(chart, RIGHT_AXES_VIEW) }));\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nexport function axis(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const leftView = findViewById(chart, LEFT_AXES_VIEW);\n  const rightView = findViewById(chart, RIGHT_AXES_VIEW);\n  const { xField, yField, xAxis, yAxis } = options;\n\n  chart.axis(xField, false);\n  chart.axis(yField[0], false);\n  chart.axis(yField[1], false);\n\n  // 左 View\n  leftView.axis(xField, xAxis);\n  leftView.axis(yField[0], getYAxisWithDefault(yAxis[0], AxisType.Left));\n\n  // 右 Y 轴\n  rightView.axis(xField, false);\n  rightView.axis(yField[1], getYAxisWithDefault(yAxis[1], AxisType.Right));\n\n  return params;\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nexport function tooltip(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n  const leftView = findViewById(chart, LEFT_AXES_VIEW);\n  const rightView = findViewById(chart, RIGHT_AXES_VIEW);\n  // tooltip 经过 getDefaultOption 处理后，一定不为 undefined\n  chart.tooltip(tooltip);\n  // 在 view 上添加 tooltip，使得 shared 和 interaction active-region 起作用\n  // view 应该继承 chart 里的 shared，但是从表现看来，继承有点问题\n  leftView.tooltip({\n    shared: true,\n  });\n  rightView.tooltip({\n    shared: true,\n  });\n  return params;\n}\n\n/**\n * interaction 配置\n * @param params\n */\nexport function interaction(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart } = params;\n\n  commonInteraction(deepAssign({}, params, { chart: findViewById(chart, LEFT_AXES_VIEW) }));\n  commonInteraction(deepAssign({}, params, { chart: findViewById(chart, RIGHT_AXES_VIEW) }));\n\n  return params;\n}\n\n/**\n * annotation 配置\n * @param params\n */\nexport function annotation(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { annotations } = options;\n\n  const a1 = get(annotations, [0]);\n  const a2 = get(annotations, [1]);\n\n  commonAnnotation(a1)(\n    deepAssign({}, params, {\n      chart: findViewById(chart, LEFT_AXES_VIEW),\n      options: {\n        annotations: a1,\n      },\n    })\n  );\n  commonAnnotation(a2)(\n    deepAssign({}, params, {\n      chart: findViewById(chart, RIGHT_AXES_VIEW),\n      options: {\n        annotations: a2,\n      },\n    })\n  );\n  return params;\n}\n\nexport function theme(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart } = params;\n\n  /*\n   * 双轴图中，部分组件是绘制在子 view 层（例如 axis，line），部分组件是绘制在 chart （例如 legend)\n   * 为 chart 和 子 view 均注册 theme，使其自行遵循 G2 theme geometry > view > chart 进行渲染。\n   */\n  commonTheme(deepAssign({}, params, { chart: findViewById(chart, LEFT_AXES_VIEW) }));\n  commonTheme(deepAssign({}, params, { chart: findViewById(chart, RIGHT_AXES_VIEW) }));\n  commonTheme(params);\n\n  return params;\n}\n\nexport function animation(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart } = params;\n\n  commonAnimation(deepAssign({}, params, { chart: findViewById(chart, LEFT_AXES_VIEW) }));\n  commonAnimation(deepAssign({}, params, { chart: findViewById(chart, RIGHT_AXES_VIEW) }));\n\n  return params;\n}\n\n/**\n * 双轴图 limitInPlot\n * @param params\n */\nexport function limitInPlot(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { yAxis } = options;\n\n  commonLimitInPlot(\n    deepAssign({}, params, {\n      chart: findViewById(chart, LEFT_AXES_VIEW),\n      options: {\n        yAxis: yAxis[0],\n      },\n    })\n  );\n\n  commonLimitInPlot(\n    deepAssign({}, params, {\n      chart: findViewById(chart, RIGHT_AXES_VIEW),\n      options: {\n        yAxis: yAxis[1],\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * legend 配置\n * 使用 custom，便于和类似于分组柱状图-单折线图的逻辑统一\n * @param params\n */\nexport function legend(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { legend, geometryOptions, yField, data } = options;\n  const leftView = findViewById(chart, LEFT_AXES_VIEW);\n  const rightView = findViewById(chart, RIGHT_AXES_VIEW);\n\n  if (legend === false) {\n    chart.legend(false);\n  } else if (isObject(legend) && legend.custom === true) {\n    chart.legend(legend);\n  } else {\n    const leftLegend = get(geometryOptions, [0, 'legend'], legend);\n    const rightLegend = get(geometryOptions, [1, 'legend'], legend);\n    // 均使用自定义图例\n    chart.once('beforepaint', () => {\n      const leftItems = data[0].length\n        ? getViewLegendItems({\n            view: leftView,\n            geometryOption: geometryOptions[0],\n            yField: yField[0],\n            legend: leftLegend,\n          })\n        : [];\n\n      const rightItems = data[1].length\n        ? getViewLegendItems({\n            view: rightView,\n            geometryOption: geometryOptions[1],\n            yField: yField[1],\n            legend: rightLegend,\n          })\n        : [];\n\n      chart.legend(\n        deepAssign({}, legend, {\n          custom: true,\n          // todo 修改类型定义\n          // @ts-ignore\n          items: leftItems.concat(rightItems),\n        })\n      );\n    });\n\n    if (geometryOptions[0].seriesField) {\n      leftView.legend(geometryOptions[0].seriesField, leftLegend);\n    }\n    if (geometryOptions[1].seriesField) {\n      rightView.legend(geometryOptions[1].seriesField, rightLegend);\n    }\n\n    // 自定义图例交互\n    chart.on('legend-item:click', (evt) => {\n      const delegateObject = get(evt, 'gEvent.delegateObject', {});\n      if (delegateObject && delegateObject.item) {\n        const { value: field, isGeometry, viewId } = delegateObject.item;\n        // geometry 的时候，直接使用 view.changeVisible\n        if (isGeometry) {\n          const idx = findIndex(yField, (yF: string) => yF === field);\n          if (idx > -1) {\n            const geometries = get(findViewById(chart, viewId), 'geometries');\n            each(geometries, (g) => {\n              g.changeVisible(!delegateObject.item.unchecked);\n            });\n          }\n        } else {\n          const legendItem = get(chart.getController('legend'), 'option.items', []);\n          // 分组柱线图\n          each(chart.views, (view) => {\n            // 单折柱图\n            const groupScale = view.getGroupScales();\n            each(groupScale, (scale: Scale) => {\n              if (scale.values && scale.values.indexOf(field) > -1) {\n                view.filter(scale.field, (value) => {\n                  const curLegendItem: Types.LegendItem = find(\n                    legendItem,\n                    (item: Types.LegendItem) => item.value === value\n                  );\n                  // 使用 legend 中的 unchecked 来判断，使得支持关闭多个图例\n                  return !curLegendItem.unchecked;\n                });\n              }\n            });\n            chart.render(true);\n          });\n        }\n      }\n    });\n  }\n\n  return params;\n}\n\n/**\n * 双轴图 slider 适配器\n * @param params\n */\nexport function slider(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  const { chart, options } = params;\n  const { slider } = options;\n  const leftView = findViewById(chart, LEFT_AXES_VIEW);\n  const rightView = findViewById(chart, RIGHT_AXES_VIEW);\n  if (slider) {\n    // 左 View\n    leftView.option('slider', slider);\n    // 监听左侧 slider 改变事件， 同步右侧 View 视图\n    leftView.on('slider:valuechanged', (evt: Event) => {\n      const {\n        event: { value, originValue },\n      } = evt;\n      if (isEqual(value, originValue)) {\n        return;\n      }\n      doSliderFilter(rightView, value);\n    });\n    chart.once('afterpaint', () => {\n      // 初始化数据，配置默认值时需要同步\n      if (!isBoolean(slider)) {\n        const { start, end } = slider;\n        if (start || end) {\n          doSliderFilter(rightView, [start, end]);\n        }\n      }\n    });\n  }\n\n  return params;\n}\n\n/**\n * 双折线图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<DualAxesOptions>): Params<DualAxesOptions> {\n  // transformOptions 一定在最前面处理；color legend 使用了 beforepaint，为便于理解放在最后面\n  return flow(\n    transformOptions,\n    createViews,\n    // 主题靠前设置，作为最低优先级\n    theme,\n    geometry,\n    meta,\n    axis,\n    limitInPlot,\n    tooltip,\n    interaction,\n    annotation,\n    animation,\n    color,\n    legend,\n    slider\n  )(params);\n}\n"]}