{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\CodeTypeDialog.vue", "mtime": 1749109381356}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inheritAttrs", "props", "data", "formData", "fileName", "undefined", "type", "rules", "required", "message", "trigger", "typeOptions", "label", "value", "computed", "watch", "mounted", "methods", "onOpen", "showFileName", "concat", "Date", "onClose", "close", "e", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "_objectSpread2", "default"], "sources": ["src/views/tool/build/CodeTypeDialog.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      v-bind=\"$attrs\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-on=\"$listeners\"\r\n      @open=\"onOpen\"\r\n      @close=\"onClose\"\r\n    >\r\n      <el-row :gutter=\"15\">\r\n        <el-form\r\n          ref=\"elForm\"\r\n          :model=\"formData\"\r\n          :rules=\"rules\"\r\n          size=\"medium\"\r\n          label-width=\"100px\"\r\n        >\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"生成类型\" prop=\"type\">\r\n              <el-radio-group v-model=\"formData.type\">\r\n                <el-radio-button\r\n                  v-for=\"(item, index) in typeOptions\"\r\n                  :key=\"index\"\r\n                  :label=\"item.value\"\r\n                  :disabled=\"item.disabled\"\r\n                >\r\n                  {{ item.label }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\r\n              <el-input v-model=\"formData.fileName\" placeholder=\"请输入文件名\" clearable />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"close\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">\r\n          确定\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['showFileName'],\r\n  data() {\r\n    return {\r\n      formData: {\r\n        fileName: undefined,\r\n        type: 'file'\r\n      },\r\n      rules: {\r\n        fileName: [{\r\n          required: true,\r\n          message: '请输入文件名',\r\n          trigger: 'blur'\r\n        }],\r\n        type: [{\r\n          required: true,\r\n          message: '生成类型不能为空',\r\n          trigger: 'change'\r\n        }]\r\n      },\r\n      typeOptions: [{\r\n        label: '页面',\r\n        value: 'file'\r\n      }, {\r\n        label: '弹窗',\r\n        value: 'dialog'\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  watch: {},\r\n  mounted() {},\r\n  methods: {\r\n    onOpen() {\r\n      if (this.showFileName) {\r\n        this.formData.fileName = `${+new Date()}.vue`\r\n      }\r\n    },\r\n    onClose() {\r\n    },\r\n    close(e) {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handleConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        this.$emit('confirm', { ...this.formData })\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAmDA;EACAA,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,QAAA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAJ,IAAA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA,GACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAC,YAAA;QACA,KAAAhB,QAAA,CAAAC,QAAA,MAAAgB,MAAA,MAAAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAAC,CAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,KAAA,CAAAF,KAAA,gBAAAO,cAAA,CAAAC,OAAA,MAAAN,KAAA,CAAAxB,QAAA;QACAwB,KAAA,CAAAJ,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}