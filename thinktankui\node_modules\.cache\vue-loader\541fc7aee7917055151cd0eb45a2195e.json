{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Crontab\\year.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Crontab\\year.vue", "mtime": 1749109381323}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["year.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "year.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n\t<el-form size=\"small\">\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"1\" v-model='radioValue'>\r\n\t\t\t\t不填，允许的通配符[, - * /]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"2\" v-model='radioValue'>\r\n\t\t\t\t每年\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"3\" v-model='radioValue'>\r\n\t\t\t\t周期从\r\n\t\t\t\t<el-input-number v-model='cycle01' :min='fullYear' :max=\"2098\" /> -\r\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : fullYear + 1\" :max=\"2099\" />\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"4\" v-model='radioValue'>\r\n\t\t\t\t从\r\n\t\t\t\t<el-input-number v-model='average01' :min='fullYear' :max=\"2098\"/> 年开始，每\r\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"2099 - average01 || fullYear\" /> 年执行一次\r\n\t\t\t</el-radio>\r\n\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio :label=\"5\" v-model='radioValue'>\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple>\r\n\t\t\t\t\t<el-option v-for=\"item in 9\" :key=\"item\" :value=\"item - 1 + fullYear\" :label=\"item -1 + fullYear\" />\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfullYear: 0,\r\n\t\t\tradioValue: 1,\r\n\t\t\tcycle01: 0,\r\n\t\t\tcycle02: 0,\r\n\t\t\taverage01: 0,\r\n\t\t\taverage02: 1,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-year',\r\n\tprops: ['check', 'month', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'year', '');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'year', '*');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 5:\r\n\t\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'year', this.cycleTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'year', this.averageTotal);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '5') {\r\n\t\t\t\tthis.$emit('update', 'year', this.checkboxString);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'checkboxString': 'checkboxChange'\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, this.fullYear, 2098)\r\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : this.fullYear + 1, 2099)\r\n\t\t\treturn cycle01 + '-' + cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tconst average01 = this.checkNum(this.average01, this.fullYear, 2098)\r\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 2099 - average01 || this.fullYear)\r\n\t\t\treturn average01 + '/' + average02;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str;\r\n\t\t}\r\n\t},\r\n\tmounted: function () {\r\n\t\t// 仅获取当前年份\r\n\t\tthis.fullYear = Number(new Date().getFullYear());\r\n\t\tthis.cycle01 = this.fullYear\r\n\t\tthis.average01 = this.fullYear\r\n\t}\r\n}\r\n</script>\r\n"]}]}