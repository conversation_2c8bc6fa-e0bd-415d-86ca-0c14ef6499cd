{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/venn/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAC5D,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACrF,OAAO,EAAE,MAAM,IAAI,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAGpE,OAAO,EACL,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,sBAAsB,EACtB,KAAK,EACL,GAAG,EACH,aAAa,EACb,iBAAiB,EACjB,cAAc,GACf,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,gBAAgB,CAAC;AACxB,OAAO,SAAS,CAAC;AACjB,OAAO,SAAS,CAAC;AAEjB,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,SAAS,CAAC;AAEnE,eAAe;AACf,MAAM,CAAC,IAAM,YAAY,GAAG,EAAE,CAAC;AAE/B;;GAEG;AACH,SAAS,QAAQ,CAAC,MAA2B,EAAE,IAAc,EAAE,YAAuB;IAC5E,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAgB,OAAO,UAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IACnC,IAAA,KAAyB,KAAK,CAAC,QAAQ,EAAE,EAAvC,QAAQ,cAAA,EAAE,QAAQ,cAAqB,CAAC;IAChD,IAAI,OAAO,GAAG,YAAY,CAAC;IAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAzB,CAAyB,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;KAC5F;IACD,IAAM,GAAG,GAAG,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IAE7D,OAAO,UAAC,EAAU,IAAK,OAAA,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAzB,CAAyB,CAAC;AACnD,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,MAA2B,EAAE,IAAc;IACzD,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QAC/B,IAAM,YAAY,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACjE,IAAM,KAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;QACjD,OAAO,UAAC,KAAY,IAAK,OAAA,KAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAApB,CAAoB,CAAC;KAC/C;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,OAAO,CAAC,MAA2B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAA6B,OAAO,OAApC,EAAE,aAAa,GAAc,OAAO,cAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEnD,qDAAqD;IACrD,IAAI,WAAW,GAAa,aAAa,CAAC,aAAa,CAAC,CAAC;IACzD,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,WAAW,GAAG,sBAAsB,CAAC,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,YAAY,CAAC,CAAC;KAC5F;IAED,KAAK,CAAC,aAAa,GAAG,iBAAiB,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAEhE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA2B;IAC/B,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IAE3B;;;;;;;;;OASG;IAEH,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3B,IAAI,CAAC,IAAI,EAAE;QACT,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7C,IAAI,GAAG,EAAE,CAAC;KACX;IAED,qBAAqB;IACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAvB,CAAuB,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAb,CAAa,CAAC,CAAC;IAC/F,UAAU;IACV,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAC,KAAK;QACnC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,iBAAiB;QACjB,OAAO,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;QAAE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,wBAAwB,CAAC,CAAC;IAE7F,OAAO,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE;QACzB,OAAO,EAAE;YACP,IAAI,EAAE,UAAU;SACjB;KACF,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA2B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,UAAU,GAA2B,OAAO,WAAlC,EAAE,SAAS,GAAgB,OAAO,UAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAErD,SAAS;IACH,IAAA,KAAe,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,EAAhD,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAA,EAAE,CAAC,QAAsC,CAAC;IACxD,qDAAqD;IACrD,IAAM,UAAU,GAAe,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,qFAAqF;IAC/E,IAAA,KAAoB,KAAK,CAAC,QAAQ,EAAhC,KAAK,WAAA,EAAE,MAAM,YAAmB,CAAC;IACzC,iCAAiC;IACjC,IAAM,QAAQ,GAAa,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnH,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEb,IAAA,GAAG,GAAK,cAAc,CAC5B,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,GAAG;YACX,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,QAAQ;YACrB,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;YACjC,MAAM,EAAE;gBACN,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,UAAU;aAClB;SACF;KACF,CAAC,CACH,IAdU,CAcT;IAEF,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAoB,CAAC;IAC1C,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAEhC,IAAM,YAAY,GAAG,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACtD,0CAA0C;IAC1C,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE;QACtC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAC,EAAE;YAC1B,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAlB,CAAkB,CAAC,CAAC;YACvD,IAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;YACpD,OAAO,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA2B;IAChC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAK,OAAO,MAAZ,CAAa;IAE1B,SAAS;IACH,IAAA,KAAa,aAAa,CAAC,KAAK,CAAC,aAAa,CAAC,EAA9C,CAAC,QAAA,EAAM,CAAC,QAAsC,CAAC;IACtD,yBAAyB;IACzB,IAAM,eAAe,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAEnD,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE/C,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,CAAC,IAAI,CAAC;YACd,QAAQ,UAAA;YACR,GAAG,EAAE,OAAO,CAAC,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,EAAE;gBACpC,wBAAwB;gBACxB,IAAI,EAAE,MAAM;gBACZ,eAAe,iBAAA;aAChB,CAAC;SACH,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA2B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAgB,OAAO,OAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAEtC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC/B,aAAa;IACb,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAE/B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA2B;IACtC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAElB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,MAA2B;IAC1C,IAAA,OAAO,GAAY,MAAM,QAAlB,EAAE,KAAK,GAAK,MAAM,MAAX,CAAY;IAC1B,IAAA,YAAY,GAAK,OAAO,aAAZ,CAAa;IAEjC,IAAI,YAAY,EAAE;QAChB,IAAM,KAAG,GAAG;YACV,eAAe,EAAE,oBAAoB;YACrC,kBAAkB,EAAE,uBAAuB;SAC5C,CAAC;QACF,WAAW,CACT,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;YACrB,OAAO,EAAE;gBACP,YAAY,EAAE,YAAY,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,uBACjC,CAAC,KACJ,IAAI,EAAE,KAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAC3B,EAHoC,CAGpC,CAAC;aACJ;SACF,CAAC,CACH,CAAC;KACH;IAED,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IACzC,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IAC5C,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA2B;IACjD,0BAA0B;IAC1B,OAAO,IAAI,CACT,OAAO,EACP,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,KAAK,CAAC,EAAE,CAAC,EACT,MAAM,EACN,IAAI,EACJ,OAAO,EACP,eAAe,EACf,SAAS;IACT,uBAAuB;KACxB,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Geometry } from '@antv/g2';\nimport { deepMix, get, isArray, isEqual } from '@antv/util';\nimport { animation, interaction, scale, theme, tooltip } from '../../adaptor/common';\nimport { schema as schemaGeometry } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport {\n  deepAssign,\n  findGeometry,\n  flow,\n  getAdjustAppendPadding,\n  LEVEL,\n  log,\n  normalPadding,\n  resolveAllPadding,\n  transformLabel,\n} from '../../utils';\nimport { ID_FIELD } from './constant';\nimport './interactions';\nimport './label';\nimport './shape';\nimport { CustomInfo, VennData, VennOptions } from './types';\nimport { getColorMap, islegalSets, layoutVennData } from './utils';\n\n/** 图例默认预留空间 */\nexport const LEGEND_SPACE = 40;\n\n/**\n * 获取 color 映射\n */\nfunction colorMap(params: Params<VennOptions>, data: VennData, colorPalette?: string[]) {\n  const { chart, options } = params;\n  const { blendMode, setsField } = options;\n  const { colors10, colors20 } = chart.getTheme();\n  let palette = colorPalette;\n  if (!isArray(palette)) {\n    palette = data.filter((d) => d[setsField].length === 1).length <= 10 ? colors10 : colors20;\n  }\n  const map = getColorMap(palette, data, blendMode, setsField);\n\n  return (id: string) => map.get(id) || palette[0];\n}\n\n/**\n * color options 转换\n */\nfunction transformColor(params: Params<VennOptions>, data: VennData): VennOptions['color'] {\n  const { options } = params;\n  const { color } = options;\n\n  if (typeof color !== 'function') {\n    const colorPalette = typeof color === 'string' ? [color] : color;\n    const map = colorMap(params, data, colorPalette);\n    return (datum: Datum) => map(datum[ID_FIELD]);\n  }\n  return color;\n}\n\n/**\n * 处理 padding\n */\nfunction padding(params: Params<VennOptions>): Params<VennOptions> {\n  const { chart, options } = params;\n  const { legend, appendPadding, padding } = options;\n\n  // 处理 legend 的位置. 默认预留 40px, 业务上可以通过 appendPadding 增加\n  let tempPadding: number[] = normalPadding(appendPadding);\n  if (legend !== false) {\n    tempPadding = getAdjustAppendPadding(appendPadding, get(legend, 'position'), LEGEND_SPACE);\n  }\n\n  chart.appendPadding = resolveAllPadding([tempPadding, padding]);\n\n  return params;\n}\n\n/**\n * 处理非法数据\n * @param params\n */\nfunction data(params: Params<VennOptions>): Params<VennOptions> {\n  const { options } = params;\n\n  /* 如遇到 交集 中存在 非法元素 的情况，就过滤掉\n   * 如：\n   * data = [\n   *   { sets: ['A'], size: 3 }, // 集合\n   *   { sets: ['B'], size: 4 }, // 集合\n   *   { sets: ['A', 'B'], size: 2 }, // 交集\n   *   { sets: ['A', 'B', 'C'], size: 2 }, // 交集 (存在非法 C，过滤该条数据)\n   *   ...\n   * ]\n   */\n\n  let data = options['data'];\n  if (!data) {\n    log(LEVEL.WARN, false, 'warn: %s', '数据不能为空');\n    data = [];\n  }\n\n  // 合法元素的集合：['A', 'B']\n  const currSets = data.filter((datum) => datum.sets.length === 1).map((datum) => datum.sets[0]);\n  // 过滤 data\n  const filterSets = data.filter((datum) => {\n    const sets = datum.sets;\n    // 存在非法元素，就过滤这条数据\n    return islegalSets(currSets, sets);\n  });\n\n  if (!isEqual(filterSets, data)) log(LEVEL.WARN, false, 'warn: %s', '交集中不能出现不存在的集合, 请输入合法数据');\n\n  return deepMix({}, params, {\n    options: {\n      data: filterSets,\n    },\n  });\n}\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<VennOptions>): Params<VennOptions> {\n  const { chart, options } = params;\n  const { pointStyle, setsField, sizeField } = options;\n\n  // 获取容器大小\n  const [t, r, b, l] = normalPadding(chart.appendPadding);\n  // 处理 legend 的位置. 默认预留 40px, 业务上可以通过 appendPadding 增加\n  const customInfo: CustomInfo = { offsetX: l, offsetY: t };\n  // coordinateBBox + appendPadding = viewBBox, 不需要再计算 appendPadding 部分，因此直接使用 viewBBox\n  const { width, height } = chart.viewBBox;\n  // 处理padding输入不合理的情况， w 和 h 不能为负数\n  const vennData: VennData = layoutVennData(options, Math.max(width - (r + l), 0), Math.max(height - (t + b), 0), 0);\n  chart.data(vennData);\n\n  const { ext } = schemaGeometry(\n    deepAssign({}, params, {\n      options: {\n        xField: 'x',\n        yField: 'y',\n        sizeField: sizeField,\n        seriesField: ID_FIELD,\n        rawFields: [setsField, sizeField],\n        schema: {\n          shape: 'venn',\n          style: pointStyle,\n        },\n      },\n    })\n  );\n\n  const geometry = ext.geometry as Geometry;\n  geometry.customInfo(customInfo);\n\n  const colorOptions = transformColor(params, vennData);\n  // 韦恩图试点, color 通道只能映射一个字段. 通过外部查找获取 datum\n  if (typeof colorOptions === 'function') {\n    geometry.color(ID_FIELD, (id) => {\n      const datum = vennData.find((d) => d[ID_FIELD] === id);\n      const defaultColor = colorMap(params, vennData)(id);\n      return colorOptions(datum, defaultColor);\n    });\n  }\n\n  return params;\n}\n\n/**\n * 处理 label\n * @param params\n */\nfunction label(params: Params<VennOptions>): Params<VennOptions> {\n  const { chart, options } = params;\n  const { label } = options;\n\n  // 获取容器大小\n  const [t, , , l] = normalPadding(chart.appendPadding);\n  // 传入 label 布局函数所需的 自定义参数\n  const customLabelInfo = { offsetX: l, offsetY: t };\n\n  const geometry = findGeometry(chart, 'schema');\n\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    geometry.label({\n      fields: ['id'],\n      callback,\n      cfg: deepMix({}, transformLabel(cfg), {\n        // 使用 G2 的 自定义label 修改位置\n        type: 'venn',\n        customLabelInfo,\n      }),\n    });\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<VennOptions>): Params<VennOptions> {\n  const { chart, options } = params;\n  const { legend, sizeField } = options;\n\n  chart.legend(ID_FIELD, legend);\n  // 强制不开启 连续图例\n  chart.legend(sizeField, false);\n\n  return params;\n}\n\n/**\n * 默认关闭坐标轴\n * @param params\n */\nexport function axis(params: Params<VennOptions>): Params<VennOptions> {\n  const { chart } = params;\n  chart.axis(false);\n\n  return params;\n}\n\n/**\n * 韦恩图 interaction 交互适配器\n */\nfunction vennInteraction(params: Params<VennOptions>): Params<VennOptions> {\n  const { options, chart } = params;\n  const { interactions } = options;\n\n  if (interactions) {\n    const MAP = {\n      'legend-active': 'venn-legend-active',\n      'legend-highlight': 'venn-legend-highlight',\n    };\n    interaction(\n      deepAssign({}, params, {\n        options: {\n          interactions: interactions.map((i) => ({\n            ...i,\n            type: MAP[i.type] || i.type,\n          })),\n        },\n      })\n    );\n  }\n\n  chart.removeInteraction('legend-active');\n  chart.removeInteraction('legend-highlight');\n  return params;\n}\n\n/**\n * 图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<VennOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    padding,\n    theme,\n    data,\n    geometry,\n    label,\n    scale({}),\n    legend,\n    axis,\n    tooltip,\n    vennInteraction,\n    animation\n    // ... 其他的 adaptor flow\n  )(params);\n}\n"]}