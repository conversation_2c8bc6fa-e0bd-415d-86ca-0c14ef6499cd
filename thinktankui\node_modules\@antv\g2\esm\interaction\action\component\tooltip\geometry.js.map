{"version": 3, "file": "geometry.js", "sourceRoot": "", "sources": ["../../../../../src/interaction/action/component/tooltip/geometry.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAG1C,OAAO,MAAM,MAAM,YAAY,CAAC;AAGhC;;;GAGG;AACH;IAA4B,iCAAM;IAAlC;QAAA,qEAgEC;QA/DS,eAAS,GAAW,CAAC,CAAC;;IA+DhC,CAAC;IA5DC;;;OAGG;IACI,4BAAI,GAAX;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACzB,IAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAM,eAAe,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/C,IAAI,eAAe,EAAE;YACnB,iBAAiB;YACjB,OAAO;SACR;QACD,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAM,SAAS,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QAE9B,2DAA2D;QAC3D,IAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,SAAS,GAAG,aAAa,GAAG,SAAS,EAAE;YACzC,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC7B,IAAM,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;gBACvC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aAChC;YACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;SACxB;IACH,CAAC;IAED;;;OAGG;IACI,4BAAI,GAAX;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAE/B,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACxC,IAAA,KAAuB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAvC,OAAO,aAAA,EAAE,OAAO,aAAuB,CAAC;QAEhD,uCAAuC;QACvC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;YACvD,OAAO;SACR;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAES,mCAAW,GAArB,UAAsB,IAAU,EAAE,KAAY;QAC5C,YAAY;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,mCAAW,GAArB,UAAsB,IAAI;QACxB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IACH,oBAAC;AAAD,CAAC,AAhED,CAA4B,MAAM,GAgEjC;AAED,eAAe,aAAa,CAAC", "sourcesContent": ["import { isEqual, get } from '@antv/util';\nimport { View } from '../../../../chart';\nimport { Point } from '../../../../interface';\nimport Action from '../../base';\nimport Tooltip from '../../../../chart/controller/tooltip';\n\n/**\n * Tooltip 展示隐藏的 Action\n * @ignore\n */\nclass TooltipAction extends Action {\n  private timeStamp: number = 0;\n  private location: Point;\n\n  /**\n   * 显示 Tooltip\n   * @returns\n   */\n  public show() {\n    const context = this.context;\n    const ev = context.event;\n    const view = context.view;\n    const isTooltipLocked = view.isTooltipLocked();\n    if (isTooltipLocked) {\n      // 锁定时不移动 tooltip\n      return;\n    }\n    const lastTimeStamp = this.timeStamp;\n    const timeStamp = +new Date();\n\n    // 在 showDelay 毫秒（默认 16ms）内到 tooltip 上可以实现 enterable（调参工程师）\n    const showDelay = get(context.view.getOptions(), 'tooltip.showDelay', 16);\n    if (timeStamp - lastTimeStamp > showDelay) {\n      const preLoc = this.location;\n      const curLoc = { x: ev.x, y: ev.y };\n      if (!preLoc || !isEqual(preLoc, curLoc)) {\n        this.showTooltip(view, curLoc);\n      }\n      this.timeStamp = timeStamp;\n      this.location = curLoc;\n    }\n  }\n\n  /**\n   * 隐藏 Tooltip。\n   * @returns\n   */\n  public hide() {\n    const view = this.context.view;\n\n    const tooltip = view.getController('tooltip');\n    const { clientX, clientY } = this.context.event;\n\n    // 如果已经 enterable + 已经在 tooltip 上，那么不隐藏\n    if (tooltip.isCursorEntered({ x: clientX, y: clientY })) {\n      return;\n    }\n\n    // 锁定 tooltip 时不隐藏\n    if (view.isTooltipLocked()) {\n      return;\n    }\n    this.hideTooltip(view);\n    this.location = null;\n  }\n\n  protected showTooltip(view: View, point: Point) {\n    // 相同位置不重复展示\n    view.showTooltip(point);\n  }\n\n  protected hideTooltip(view) {\n    view.hideTooltip();\n  }\n}\n\nexport default TooltipAction;\n"]}