{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\userInfo.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\userInfo.vue", "mtime": 1749109381355}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userInfo.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n    <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n      <el-input v-model=\"form.nickName\" maxlength=\"30\" />\r\n    </el-form-item> \r\n    <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n      <el-input v-model=\"form.phonenumber\" maxlength=\"11\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"邮箱\" prop=\"email\">\r\n      <el-input v-model=\"form.email\" maxlength=\"50\" />\r\n    </el-form-item>\r\n    <el-form-item label=\"性别\">\r\n      <el-radio-group v-model=\"form.sex\">\r\n        <el-radio label=\"0\">男</el-radio>\r\n        <el-radio label=\"1\">女</el-radio>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\r\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserProfile } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  props: {\r\n    user: {\r\n      type: Object\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        nickName: [\r\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          { required: true, message: \"邮箱地址不能为空\", trigger: \"blur\" },\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phonenumber: [\r\n          { required: true, message: \"手机号码不能为空\", trigger: \"blur\" },\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    user: {\r\n      handler(user) {\r\n        if (user) {\r\n          this.form = { nickName: user.nickName, phonenumber: user.phonenumber, email: user.email, sex: user.sex };\r\n        }\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          updateUserProfile(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.user.phonenumber = this.form.phonenumber;\r\n            this.user.email = this.form.email;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    close() {\r\n      this.$tab.closePage();\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}