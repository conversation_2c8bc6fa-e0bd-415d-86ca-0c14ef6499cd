{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RightPanel\\index.vue", "mtime": 1749109381326}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUmlnaHRQYW5lbCcsCiAgcHJvcHM6IHsKICAgIGNsaWNrTm90Q2xvc2U6IHsKICAgICAgZGVmYXVsdDogZmFsc2UsCiAgICAgIHR5cGU6IEJvb2xlYW4KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBzaG93OiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaG93U2V0dGluZ3M7CiAgICAgIH0sCiAgICAgIHNldDogZnVuY3Rpb24gc2V0KHZhbCkgewogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdzZXR0aW5ncy9jaGFuZ2VTZXR0aW5nJywgewogICAgICAgICAga2V5OiAnc2hvd1NldHRpbmdzJywKICAgICAgICAgIHZhbHVlOiB2YWwKICAgICAgICB9KTsKICAgICAgfQogICAgfQogIH0sCiAgd2F0Y2g6IHsKICAgIHNob3c6IGZ1bmN0aW9uIHNob3codmFsdWUpIHsKICAgICAgaWYgKHZhbHVlICYmICF0aGlzLmNsaWNrTm90Q2xvc2UpIHsKICAgICAgICB0aGlzLmFkZEV2ZW50Q2xpY2soKTsKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuYWRkRXZlbnRDbGljaygpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHZhciBlbHggPSB0aGlzLiRyZWZzLnJpZ2h0UGFuZWw7CiAgICBlbHgucmVtb3ZlKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBhZGRFdmVudENsaWNrOiBmdW5jdGlvbiBhZGRFdmVudENsaWNrKCkgewogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcik7CiAgICB9LAogICAgY2xvc2VTaWRlYmFyOiBmdW5jdGlvbiBjbG9zZVNpZGViYXIoZXZ0KSB7CiAgICAgIHZhciBwYXJlbnQgPSBldnQudGFyZ2V0LmNsb3Nlc3QoJy5lbC1kcmF3ZXJfX2JvZHknKTsKICAgICAgaWYgKCFwYXJlbnQpIHsKICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcik7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "props", "clickNotClose", "default", "type", "Boolean", "computed", "show", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "watch", "addEventClick", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "elx", "$refs", "rightPanel", "remove", "methods", "window", "addEventListener", "closeSidebar", "evt", "parent", "target", "closest", "removeEventListener"], "sources": ["src/components/RightPanel/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    }\r\n  },\r\n  computed: {\r\n    show: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.addEventClick()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.el-drawer__body')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 260px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;iCAYA;EACAA,IAAA;EACAC,KAAA;IACAC,aAAA;MACAC,OAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,QAAA;IACAC,IAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAL,MAAA,CAAAM,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EACA;EACAI,KAAA;IACAX,IAAA,WAAAA,KAAAU,KAAA;MACA,IAAAA,KAAA,UAAAf,aAAA;QACA,KAAAiB,aAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,aAAA,WAAAA,cAAA;IACA,IAAAC,GAAA,QAAAC,KAAA,CAAAC,UAAA;IACAF,GAAA,CAAAG,MAAA;EACA;EACAC,OAAA;IACAP,aAAA,WAAAA,cAAA;MACAQ,MAAA,CAAAC,gBAAA,eAAAC,YAAA;IACA;IACAA,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAC,MAAA,GAAAD,GAAA,CAAAE,MAAA,CAAAC,OAAA;MACA,KAAAF,MAAA;QACA,KAAAxB,IAAA;QACAoB,MAAA,CAAAO,mBAAA,eAAAL,YAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}