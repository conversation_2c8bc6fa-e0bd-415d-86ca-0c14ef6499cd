{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\AppMain.vue", "mtime": 1749109381330}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtbWFpbiB7DQogIC8qIDUwPSBuYXZiYXIgIDUwICAqLw0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gNTBweCk7DQogIHdpZHRoOiAxMDAlOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5maXhlZC1oZWFkZXIgKyAuYXBwLW1haW4gew0KICBwYWRkaW5nLXRvcDogNTBweDsNCn0NCg0KLmhhc1RhZ3NWaWV3IHsNCiAgLmFwcC1tYWluIHsNCiAgICAvKiA4NCA9IG5hdmJhciArIHRhZ3MtdmlldyA9IDUwICsgMzQgKi8NCiAgICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7DQogIH0NCg0KICAuZml4ZWQtaGVhZGVyICsgLmFwcC1tYWluIHsNCiAgICBwYWRkaW5nLXRvcDogODRweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <iframe-toggle />\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport iframeToggle from \"./IframeToggle/index\"\r\n\r\nexport default {\r\n  name: 'AppMain',\r\n  components: { iframeToggle },\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addIframe()\r\n    }\r\n  },\r\n  mounted() {\r\n    this.addIframe()\r\n  },\r\n  methods: {\r\n    addIframe() {\r\n      const {name} = this.$route\r\n      if (name && this.$route.meta.link) {\r\n        this.$store.dispatch('tagsView/addIframeView', this.$route)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 6px;\r\n  }\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 6px;\r\n  height: 6px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n</style>\r\n"]}]}