{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\iFrame\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\iFrame\\index.vue", "mtime": 1749109381328}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgc3JjOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCAtIDk0LjUgKyAicHg7IiwNCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICB1cmw6IHRoaXMuc3JjDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZDogZnVuY3Rpb24gKCkgew0KICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgfSwgMzAwKTsNCiAgICBjb25zdCB0aGF0ID0gdGhpczsNCiAgICB3aW5kb3cub25yZXNpemUgPSBmdW5jdGlvbiB0ZW1wKCkgew0KICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiOw0KICAgIH07DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/iFrame", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" :style=\"'height:' + height\">\r\n    <iframe\r\n      :src=\"src\"\r\n      frameborder=\"no\"\r\n      style=\"width: 100%; height: 100%\"\r\n      scrolling=\"auto\"\r\n    />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  props: {\r\n    src: {\r\n      type: String,\r\n      required: true\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      height: document.documentElement.clientHeight - 94.5 + \"px;\",\r\n      loading: true,\r\n      url: this.src\r\n    };\r\n  },\r\n  mounted: function () {\r\n    setTimeout(() => {\r\n      this.loading = false;\r\n    }, 300);\r\n    const that = this;\r\n    window.onresize = function temp() {\r\n      that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\r\n    };\r\n  }\r\n};\r\n</script>\r\n"]}]}