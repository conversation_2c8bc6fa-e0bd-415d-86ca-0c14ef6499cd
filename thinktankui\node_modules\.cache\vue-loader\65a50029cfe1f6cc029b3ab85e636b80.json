{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\IconSelect\\index.vue", "mtime": 1749109381325}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgaWNvbnMgZnJvbSAnLi9yZXF1aXJlSWNvbnMnDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdJY29uU2VsZWN0JywNCiAgcHJvcHM6IHsNCiAgICBhY3RpdmVJY29uOiB7DQogICAgICB0eXBlOiBTdHJpbmcNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIG5hbWU6ICcnLA0KICAgICAgaWNvbkxpc3Q6IGljb25zDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZmlsdGVySWNvbnMoKSB7DQogICAgICB0aGlzLmljb25MaXN0ID0gaWNvbnMNCiAgICAgIGlmICh0aGlzLm5hbWUpIHsNCiAgICAgICAgdGhpcy5pY29uTGlzdCA9IHRoaXMuaWNvbkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pbmNsdWRlcyh0aGlzLm5hbWUpKQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2VsZWN0ZWRJY29uKG5hbWUpIHsNCiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdGVkJywgbmFtZSkNCiAgICAgIGRvY3VtZW50LmJvZHkuY2xpY2soKQ0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLm5hbWUgPSAnJw0KICAgICAgdGhpcy5pY29uTGlzdCA9IGljb25zDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\r\n<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" class=\"icon-search\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div class=\"list-container\">\r\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\r\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\r\n            <span>{{ item }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\nexport default {\r\n  name: 'IconSelect',\r\n  props: {\r\n    activeIcon: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      this.iconList = icons\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n  .icon-body {\r\n    width: 100%;\r\n    padding: 10px;\r\n    .icon-search {\r\n      position: relative;\r\n      margin-bottom: 5px;\r\n    }\r\n    .icon-list {\r\n      height: 200px;\r\n      overflow: auto;\r\n      .list-container {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .icon-item-wrapper {\r\n          width: calc(100% / 3);\r\n          height: 25px;\r\n          line-height: 25px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          .icon-item {\r\n            display: flex;\r\n            max-width: 100%;\r\n            height: 100%;\r\n            padding: 0 5px;\r\n            &:hover {\r\n              background: #ececec;\r\n              border-radius: 5px;\r\n            }\r\n            .icon {\r\n              flex-shrink: 0;\r\n            }\r\n            span {\r\n              display: inline-block;\r\n              vertical-align: -0.15em;\r\n              fill: currentColor;\r\n              padding-left: 2px;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n            }\r\n          }\r\n          .icon-item.active {\r\n            background: #ececec;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n"]}]}