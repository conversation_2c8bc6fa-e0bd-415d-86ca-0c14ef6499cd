{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RuoYi\\Git\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RuoYi\\Git\\index.vue", "mtime": 1749109381327}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSdW9ZaUdpdCcsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVybDogJ2h0dHBzOi8vZ2l0ZWUuY29tL2luc2lzdGVuY2UyMDIyL1J1b1lpLVZ1ZS1GYXN0QVBJJw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdvdG8oKSB7DQogICAgICB3aW5kb3cub3Blbih0aGlzLnVybCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RuoYi/Git", "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"github\" @click=\"goto\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiGit',\r\n  data() {\r\n    return {\r\n      url: 'https://gitee.com/insistence2022/RuoYi-Vue-FastAPI'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>"]}]}