{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/chord/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AACjE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAEzD,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE,2BAA2B,EAAE,IAAI,EAAE,2BAA2B,EAAE,MAAM,aAAa,CAAC;AACrH,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGlF,SAAS,aAAa,CAAC,MAA4B;IACjD,0CAA0C;IAElC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,IAAI,GAA8F,OAAO,KAArG,EAAE,WAAW,GAAiF,OAAO,YAAxF,EAAE,WAAW,GAAoE,OAAO,YAA3E,EAAE,WAAW,GAAuD,OAAO,YAA9D,EAAE,gBAAgB,GAAqC,OAAO,iBAA5C,EAAE,cAAc,GAAqB,OAAO,eAA5B,EAAE,KAAmB,OAAO,UAAZ,EAAd,SAAS,mBAAG,EAAE,KAAA,CAAa;IAElH,oBAAoB;IACpB,IAAM,oBAAoB,GAAG,2BAA2B,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAEhG,IAAA,KAAmB,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,kBAAA,EAAE,cAAc,gBAAA,EAAE,EAAE,oBAAoB,CAAC,EAAtG,KAAK,WAAA,EAAE,KAAK,WAA0F,CAAC;IAE/G,kBAAkB;IAClB,IAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;QAC/B,6BACK,IAAI,CAAC,IAAI,iBAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,GAAK,SAAS,QAAE,KACrD,MAAM,EAAE,IAAI,IACZ;IACJ,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,IAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;QAC/B,2BACE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EACxB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EACxB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IACvC,IAAI,CAAC,IAAI,iBAAG,GAAG,EAAE,GAAG,EAAE,OAAO,GAAK,SAAS,QAAE,KAChD,MAAM,EAAE,KAAK,IACb;IACJ,CAAC,CAAC,CAAC;IAEH,6BACK,MAAM,KACT,GAAG,wBACE,MAAM,CAAC,GAAG;YACb,mCAAmC;YACnC,SAAS,EAAE,EAAE,SAAS,WAAA,EAAE,SAAS,WAAA,EAAE,OAErC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA4B;;IACjC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,KAAK;YACT,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;YAC7B,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;;QACrC,GAAC,gBAAgB,IAAG,EAAE,IAAI,EAAE,OAAO,EAAE;QACrC,GAAC,gBAAgB,IAAG,EAAE,IAAI,EAAE,OAAO,EAAE;YACrC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA4B;IAChC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA4B;IAClC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,OAAO,CAAC,MAA4B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,UAAU,CAAC,MAA4B;IACtC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,MAA4B;IAChD,YAAY;IACJ,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAK,MAAM,CAAC,GAAG,CAAC,SAAS,UAAzB,CAA0B;IACnC,IAAA,SAAS,GAAqB,OAAO,UAA5B,EAAE,KAAK,GAAc,OAAO,MAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE9C,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IACpC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEzB,IAAI;IACJ,OAAO,CAAC;QACN,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,WAAW,EAAE,gBAAgB;YAC7B,OAAO,EAAE;gBACP,KAAK,EAAE,SAAS;aACjB;YACD,KAAK,OAAA;YACL,OAAO,SAAA;SACR;KACF,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,MAA4B;IACxC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAK,MAAM,CAAC,GAAG,CAAC,SAAS,UAAzB,CAA0B;IACnC,IAAA,SAAS,GAAc,OAAO,UAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEvC,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IACpC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEzB,OAAO;IACP,IAAM,WAAW,GAAG;QAClB,MAAM,EAAE,OAAO;QACf,MAAM,EAAE,OAAO;QACf,WAAW,EAAE,gBAAgB;QAC7B,IAAI,EAAE;YACJ,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,KAAK;SACb;QACD,OAAO,SAAA;KACR,CAAC;IACF,IAAI,CAAC;QACH,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE,WAAW;KACrB,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,MAA4B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,SAAS,GAAK,OAAO,UAAZ,CAAa;IAE9B,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,2BAA2B,CAAC,KAAK,CAAC,CAAC,CAAC;IAEvE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA4B;IAClD,0BAA0B;IAC1B,OAAO,IAAI,CACT,KAAK,EACL,aAAa,EACb,UAAU,EACV,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,KAAK,EACL,SAAS,CACV,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { interaction, state, theme } from '../../adaptor/common';\nimport { edge, polygon } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { addViewAnimation, flow, getAllGeometriesRecursively, pick, transformDataToNodeLinkData } from '../../utils';\nimport { chordLayout } from '../../utils/transform/chord';\nimport { EDGE_COLOR_FIELD, NODE_COLOR_FIELD, X_FIELD, Y_FIELD } from './constant';\nimport { ChordOptions } from './types';\n\nfunction transformData(params: Params<ChordOptions>): Params<ChordOptions> {\n  // 将弦图数据放到ext中，nodeGeometry edgeGeometry使用\n\n  const { options } = params;\n  const { data, sourceField, targetField, weightField, nodePaddingRatio, nodeWidthRatio, rawFields = [] } = options;\n\n  // 将数据转换为node link格式\n  const chordLayoutInputData = transformDataToNodeLinkData(data, sourceField, targetField, weightField);\n\n  const { nodes, links } = chordLayout({ weight: true, nodePaddingRatio, nodeWidthRatio }, chordLayoutInputData);\n\n  // 1. 生成绘制node使用数据\n  const nodesData = nodes.map((node) => {\n    return {\n      ...pick(node, ['id', 'x', 'y', 'name', ...rawFields]),\n      isNode: true,\n    };\n  });\n\n  // 2. 生成 edge 使用数据 （同桑基图）\n  const edgesData = links.map((link) => {\n    return {\n      source: link.source.name,\n      target: link.target.name,\n      name: link.source.name || link.target.name,\n      ...pick(link, ['x', 'y', 'value', ...rawFields]),\n      isNode: false,\n    };\n  });\n\n  return {\n    ...params,\n    ext: {\n      ...params.ext,\n      // 将chordData放到ext中，方便下面的geometry使用\n      chordData: { nodesData, edgesData },\n    },\n  };\n}\n\n/**\n * scale配置\n * @param params 参数\n */\nfunction scale(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart } = params;\n  chart.scale({\n    x: { sync: true, nice: true },\n    y: { sync: true, nice: true, max: 1 },\n    [NODE_COLOR_FIELD]: { sync: 'color' },\n    [EDGE_COLOR_FIELD]: { sync: 'color' },\n  });\n  return params;\n}\n\n/**\n * axis配置\n * @param params 参数\n */\nfunction axis(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart } = params;\n  chart.axis(false);\n  return params;\n}\n\n/**\n * legend配置\n * @param params 参数\n */\nfunction legend(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart } = params;\n  chart.legend(false);\n  return params;\n}\n\n/**\n * tooltip配置\n * @param params 参数\n */\nfunction tooltip(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  chart.tooltip(tooltip);\n  return params;\n}\n\n/**\n * coordinate配置\n * @param params 参数\n */\nfunction coordinate(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart } = params;\n  chart.coordinate('polar').reflect('y');\n  return params;\n}\n\n/**\n * nodeGeometry配置\n * @param params 参数\n */\nfunction nodeGeometry(params: Params<ChordOptions>): Params<ChordOptions> {\n  // node view\n  const { chart, options } = params;\n  const { nodesData } = params.ext.chordData;\n  const { nodeStyle, label, tooltip } = options;\n\n  const nodeView = chart.createView();\n  nodeView.data(nodesData);\n\n  // 面\n  polygon({\n    chart: nodeView,\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      seriesField: NODE_COLOR_FIELD,\n      polygon: {\n        style: nodeStyle,\n      },\n      label,\n      tooltip,\n    },\n  });\n  return params;\n}\n\n/**\n * edgeGeometry配置\n * @param params 参数\n */\nfunction edgeGeometry(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart, options } = params;\n  const { edgesData } = params.ext.chordData;\n  const { edgeStyle, tooltip } = options;\n\n  const edgeView = chart.createView();\n  edgeView.data(edgesData);\n\n  // edge\n  const edgeOptions = {\n    xField: X_FIELD,\n    yField: Y_FIELD,\n    seriesField: EDGE_COLOR_FIELD,\n    edge: {\n      style: edgeStyle,\n      shape: 'arc',\n    },\n    tooltip,\n  };\n  edge({\n    chart: edgeView,\n    options: edgeOptions,\n  });\n  return params;\n}\n\nfunction animation(params: Params<ChordOptions>): Params<ChordOptions> {\n  const { chart, options } = params;\n  const { animation } = options;\n\n  addViewAnimation(chart, animation, getAllGeometriesRecursively(chart));\n\n  return params;\n}\n\n/**\n * 弦图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<ChordOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    theme,\n    transformData,\n    coordinate,\n    scale,\n    axis,\n    legend,\n    tooltip,\n    edgeGeometry,\n    nodeGeometry,\n    interaction,\n    state,\n    animation\n  )(params);\n}\n"]}