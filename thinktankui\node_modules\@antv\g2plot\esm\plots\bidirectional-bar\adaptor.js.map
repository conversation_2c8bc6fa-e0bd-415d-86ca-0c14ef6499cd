{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/bidirectional-bar/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AACvC,OAAO,EACL,SAAS,IAAI,eAAe,EAC5B,WAAW,IAAI,iBAAiB,EAChC,WAAW,IAAI,iBAAiB,EAChC,KAAK,EACL,KAAK,IAAI,WAAW,EACpB,OAAO,GACR,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAC3F,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAEjF,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAEtD;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAuC;IAC/C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAkE,OAAO,KAAzE,EAAE,MAAM,GAA0D,OAAO,OAAjE,EAAE,MAAM,GAAkD,OAAO,OAAzD,EAAE,KAAK,GAA2C,OAAO,MAAlD,EAAE,QAAQ,GAAiC,OAAO,SAAxC,EAAE,UAAU,GAAqB,OAAO,WAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEtF,OAAO;IACP,IAAM,SAAS,GAAU,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;IACrG,iCAAiC;IACjC,IAAI,MAAM,EAAE;QACV,KAAK,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;KACxC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IACD,UAAU;IACV,IAAI,SAAe,CAAC;IACpB,IAAI,UAAgB,CAAC;IACd,IAAA,aAAa,GAAoB,SAAS,GAA7B,EAAE,cAAc,GAAI,SAAS,GAAb,CAAc;IAElD,KAAK;IACL,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;QACxB,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC;YAC3B,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrB,GAAG,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;aACtB;YACD,EAAE,EAAE,eAAe;SACpB,CAAC,CAAC;QAEH,SAAS,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEhD,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YAC5B,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;gBACvB,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;aACpB;YACD,EAAE,EAAE,gBAAgB;SACrB,CAAC,CAAC;QACH,UAAU,CAAC,UAAU,EAAE,CAAC,SAAS,EAAE,CAAC;QAEpC,+DAA+D;QAC/D,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9B,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACjC;SAAM;QACL,KAAK;QACL,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC;YAC3B,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACrB,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;aACtB;YACD,EAAE,EAAE,eAAe;SACpB,CAAC,CAAC;QACH,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YAC5B,MAAM,EAAE;gBACN,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;gBACvB,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;aACpB;YACD,EAAE,EAAE,gBAAgB;SACrB,CAAC,CAAC;QACH,UAAU,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAErC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9B,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KACjC;IACD,IAAM,IAAI,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAClC,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE;YACP,UAAU,YAAA;YACV,MAAM,QAAA;YACN,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YACjB,WAAW,EAAE,gBAAgB;YAC7B,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,EAAE,QAAQ;aAChB;SACF;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,IAAI,CAAC,CAAC;IAEf,IAAM,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACnC,KAAK,EAAE,UAAU;QACjB,OAAO,EAAE;YACP,MAAM,QAAA;YACN,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;YACjB,WAAW,EAAE,gBAAgB;YAC7B,UAAU,YAAA;YACV,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,EAAE,QAAQ;aAChB;SACF;KACF,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEhB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,IAAI,CAAC,MAAuC;;IAC3C,IAAA,OAAO,GAAY,MAAM,QAAlB,EAAE,KAAK,GAAK,MAAM,MAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IACjD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvD,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAEzD,IAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,OAAO;QACpC,IAAI,GAAG,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;YAC1C,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;SACjD;IACH,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK;QACT,GAAC,gBAAgB,IAAG;YAClB,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,UAAC,CAAC;gBACX,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;SACF;YACD,CAAC;IAEH,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,CAAC,CAAC,CAAC,IAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IAEjD,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,CAAC,CAAC,CAAC,IAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAElD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAAuC;IAC3C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAoC,OAAO,MAA3C,EAAE,KAAK,GAA6B,OAAO,MAApC,EAAE,MAAM,GAAqB,OAAO,OAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEzD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvD,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACzD,qDAAqD;IACrD,aAAa;IACb,IAAI,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,QAAQ,MAAK,QAAQ,EAAE;QAChC,2CAA2C;QAC3C,UAAU,CAAC,IAAI,CAAC,MAAM,wBAAO,KAAK,KAAE,KAAK,EAAE,EAAE,SAAS,EAAE,cAAM,OAAA,EAAE,EAAF,CAAE,EAAE,IAAG,CAAC;KACvE;SAAM;QACL,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChC;IAED,4BAA4B;IAC5B,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC/B;SAAM;QACL,SAAS,CAAC,IAAI,CAAC,MAAM;YACnB,0BAA0B;YAC1B,QAAQ,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,IAC9C,KAAK,EACR,CAAC;KACJ;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACjC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;KACnC;SAAM;QACL,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C;IACD;;;OAGG;IAEH,YAAY;IACZ,KAAK,CAAC,cAAc,GAAG;QACrB,QAAQ,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ;QACtD,MAAM,QAAA;KACP,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAAuC;IACzD,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,iBAAiB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3F,iBAAiB,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE5F,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,MAAuC;IACzD,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAY,OAAO,OAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;IAElC,iBAAiB,CACf,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC;QAC3C,OAAO,EAAE;YACP,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACxB;KACF,CAAC,CACH,CAAC;IAEF,iBAAiB,CACf,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC;QAC5C,OAAO,EAAE;YACP,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SACxB;KACF,CAAC,CACH,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,MAAuC;IACnD,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACrF,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IACtF,WAAW,CAAC,MAAM,CAAC,CAAC;IAEpB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,MAAuC;IACvD,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACzF,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1F,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAAuC;IAAtD,iBA0DC;;IAzDS,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE1C,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IACvD,IAAM,UAAU,GAAG,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACzD,IAAM,YAAY,GAAG,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACzD,IAAM,aAAa,GAAG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAE3D,IAAI,CAAC,KAAK,EAAE;QACV,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC5B;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,KAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,uBAAuB;QACvB,OAAO;QACP,IAAI,CAAC,KAAG,CAAC,QAAQ,EAAE;YACjB,KAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;SACzB;QACD,IAAI,KAAG,CAAC,MAAM,KAAK,SAAS,EAAE;YAC5B,KAAG,CAAC,MAAM,GAAG,CAAC,CAAC;SAChB;QAED,kCAAkC;QAClC,IAAM,YAAY,gBAAQ,KAAG,CAAE,CAAC;QAChC,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE;YACxB,mBAAmB;YACnB,IAAM,SAAS,GAAG,CAAA,MAAA,YAAY,CAAC,KAAK,0CAAE,SAAS,KAAI,CAAC,KAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACnG,KAAG,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,KAAG,CAAC,KAAK,EAAE,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;YACrD,IAAM,YAAY,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACxE,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;SACjG;aAAM;YACL,IAAM,aAAW,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YACvE,IAAI,OAAO,KAAG,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBACpC,KAAG,CAAC,QAAQ,GAAG,aAAW,CAAC,KAAG,CAAC,QAAQ,CAAC,CAAC;aAC1C;iBAAM,IAAI,OAAO,KAAG,CAAC,QAAQ,KAAK,UAAU,EAAE;gBAC7C,KAAG,CAAC,QAAQ,GAAG;oBAAC,cAAO;yBAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;wBAAP,yBAAO;;oBAAK,OAAA,aAAW,CAAE,KAAG,CAAC,QAAgB,CAAC,KAAK,CAAC,KAAI,EAAE,IAAI,CAAC,CAAC;gBAApD,CAAoD,CAAC;aAClF;YACD,sBAAsB;YACtB,IAAM,YAAY,GAAG,CAAA,MAAA,YAAY,CAAC,KAAK,0CAAE,YAAY,KAAI,QAAQ,CAAC;YAClE,YAAY,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;YAC1E,IAAM,eAAe,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;YAC3E,KAAG,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE,EAAE,KAAG,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;SACxF;QAED,YAAY,CAAC,KAAK,CAAC;YACjB,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC;SAClC,CAAC,CAAC;QACH,aAAa,CAAC,KAAK,CAAC;YAClB,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnB,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,KAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAuC;IAC7D,0BAA0B;IAC1B,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC;AACxG,CAAC", "sourcesContent": ["import { View } from '@antv/g2';\nimport { get, keys } from '@antv/util';\nimport {\n  animation as commonAnimation,\n  interaction as commonInteraction,\n  limitInPlot as commonLimitInPlot,\n  scale,\n  theme as commonTheme,\n  tooltip,\n} from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, findViewById, flow, transformLabel } from '../../utils';\nimport { FIRST_AXES_VIEW, SECOND_AXES_VIEW, SERIES_FIELD_KEY } from './constant';\nimport { BidirectionalBarOptions } from './types';\nimport { isHorizontal, transformData } from './utils';\n\n/**\n * geometry 处理\n * @param params\n */\nfunction geometry(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart, options } = params;\n  const { data, xField, yField, color, barStyle, widthRatio, legend, layout } = options;\n\n  // 处理数据\n  const groupData: any[] = transformData(xField, yField, SERIES_FIELD_KEY, data, isHorizontal(layout));\n  // 在创建子 view 执行后不行，需要在前面处理 legend\n  if (legend) {\n    chart.legend(SERIES_FIELD_KEY, legend);\n  } else if (legend === false) {\n    chart.legend(false);\n  }\n  // 创建 view\n  let firstView: View;\n  let secondView: View;\n  const [firstViewData, secondViewData] = groupData;\n\n  // 横向\n  if (isHorizontal(layout)) {\n    firstView = chart.createView({\n      region: {\n        start: { x: 0, y: 0 },\n        end: { x: 0.5, y: 1 },\n      },\n      id: FIRST_AXES_VIEW,\n    });\n\n    firstView.coordinate().transpose().reflect('x');\n\n    secondView = chart.createView({\n      region: {\n        start: { x: 0.5, y: 0 },\n        end: { x: 1, y: 1 },\n      },\n      id: SECOND_AXES_VIEW,\n    });\n    secondView.coordinate().transpose();\n\n    // @说明: 测试发现，横向因为轴的反转，需要数据也反转，不然会图形渲染是反的(翻转操作进入到 transform 中处理)\n    firstView.data(firstViewData);\n    secondView.data(secondViewData);\n  } else {\n    // 纵向\n    firstView = chart.createView({\n      region: {\n        start: { x: 0, y: 0 },\n        end: { x: 1, y: 0.5 },\n      },\n      id: FIRST_AXES_VIEW,\n    });\n    secondView = chart.createView({\n      region: {\n        start: { x: 0, y: 0.5 },\n        end: { x: 1, y: 1 },\n      },\n      id: SECOND_AXES_VIEW,\n    });\n    secondView.coordinate().reflect('y');\n\n    firstView.data(firstViewData);\n    secondView.data(secondViewData);\n  }\n  const left = deepAssign({}, params, {\n    chart: firstView,\n    options: {\n      widthRatio,\n      xField,\n      yField: yField[0],\n      seriesField: SERIES_FIELD_KEY,\n      interval: {\n        color,\n        style: barStyle,\n      },\n    },\n  });\n  interval(left);\n\n  const right = deepAssign({}, params, {\n    chart: secondView,\n    options: {\n      xField,\n      yField: yField[1],\n      seriesField: SERIES_FIELD_KEY,\n      widthRatio,\n      interval: {\n        color,\n        style: barStyle,\n      },\n    },\n  });\n\n  interval(right);\n\n  return params;\n}\n\n/**\n * meta 配置\n * - 对称条形图对数据进行了处理，通过 SERIES_FIELD_KEY 来对两条 yField 数据进行分类\n * @param params\n */\nfunction meta(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { options, chart } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n  const firstView = findViewById(chart, FIRST_AXES_VIEW);\n  const secondView = findViewById(chart, SECOND_AXES_VIEW);\n\n  const aliasMap = {};\n  keys(options?.meta || {}).map((metaKey) => {\n    if (get(options?.meta, [metaKey, 'alias'])) {\n      aliasMap[metaKey] = options.meta[metaKey].alias;\n    }\n  });\n\n  chart.scale({\n    [SERIES_FIELD_KEY]: {\n      sync: true,\n      formatter: (v) => {\n        return get(aliasMap, v, v);\n      },\n    },\n  });\n\n  scale({\n    [xField]: xAxis,\n    [yField[0]]: yAxis[yField[0]],\n  })(deepAssign({}, params, { chart: firstView }));\n\n  scale({\n    [xField]: xAxis,\n    [yField[1]]: yAxis[yField[1]],\n  })(deepAssign({}, params, { chart: secondView }));\n\n  return params;\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField, layout } = options;\n\n  const firstView = findViewById(chart, FIRST_AXES_VIEW);\n  const secondView = findViewById(chart, SECOND_AXES_VIEW);\n  // 第二个 view axis 始终隐藏; 注意 bottom 的时候，只隐藏 label，其他共用配置\n  // @ts-ignore\n  if (xAxis?.position === 'bottom') {\n    // fixme 直接设置 label: null 会导致 tickLine 无法显示\n    secondView.axis(xField, { ...xAxis, label: { formatter: () => '' } });\n  } else {\n    secondView.axis(xField, false);\n  }\n\n  // 为 false 则是不显示 firstView 轴\n  if (xAxis === false) {\n    firstView.axis(xField, false);\n  } else {\n    firstView.axis(xField, {\n      // 不同布局 firstView 的坐标轴显示位置\n      position: isHorizontal(layout) ? 'top' : 'bottom',\n      ...xAxis,\n    });\n  }\n\n  if (yAxis === false) {\n    firstView.axis(yField[0], false);\n    secondView.axis(yField[1], false);\n  } else {\n    firstView.axis(yField[0], yAxis[yField[0]]);\n    secondView.axis(yField[1], yAxis[yField[1]]);\n  }\n  /**\n   *  这个注入，主要是在syncViewPadding时候拿到相对应的配置：布局和轴的位置\n   *  TODO 之后希望 g2 View 对象可以开放 setter 可以设置一些需要的东西\n   */\n\n  //@ts-ignore\n  chart.__axisPosition = {\n    position: firstView.getOptions().axes[xField].position,\n    layout,\n  };\n  return params;\n}\n\n/**\n * interaction 配置\n * @param params\n */\nexport function interaction(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart } = params;\n\n  commonInteraction(deepAssign({}, params, { chart: findViewById(chart, FIRST_AXES_VIEW) }));\n  commonInteraction(deepAssign({}, params, { chart: findViewById(chart, SECOND_AXES_VIEW) }));\n\n  return params;\n}\n\n/**\n * limitInPlot\n * @param params\n */\nexport function limitInPlot(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart, options } = params;\n  const { yField, yAxis } = options;\n\n  commonLimitInPlot(\n    deepAssign({}, params, {\n      chart: findViewById(chart, FIRST_AXES_VIEW),\n      options: {\n        yAxis: yAxis[yField[0]],\n      },\n    })\n  );\n\n  commonLimitInPlot(\n    deepAssign({}, params, {\n      chart: findViewById(chart, SECOND_AXES_VIEW),\n      options: {\n        yAxis: yAxis[yField[1]],\n      },\n    })\n  );\n\n  return params;\n}\n\n/**\n * theme\n * @param params\n */\nexport function theme(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart } = params;\n\n  commonTheme(deepAssign({}, params, { chart: findViewById(chart, FIRST_AXES_VIEW) }));\n  commonTheme(deepAssign({}, params, { chart: findViewById(chart, SECOND_AXES_VIEW) }));\n  commonTheme(params);\n\n  return params;\n}\n\n/**\n * animation\n * @param params\n */\nexport function animation(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart } = params;\n\n  commonAnimation(deepAssign({}, params, { chart: findViewById(chart, FIRST_AXES_VIEW) }));\n  commonAnimation(deepAssign({}, params, { chart: findViewById(chart, SECOND_AXES_VIEW) }));\n\n  return params;\n}\n\n/**\n * label 配置 (1. 设置 offset 偏移量默认值 2. leftView 偏移量需要 *= -1)\n * @param params\n */\nfunction label(params: Params<BidirectionalBarOptions>): Params<BidirectionalBarOptions> {\n  const { chart, options } = params;\n  const { label, yField, layout } = options;\n\n  const firstView = findViewById(chart, FIRST_AXES_VIEW);\n  const secondView = findViewById(chart, SECOND_AXES_VIEW);\n  const leftGeometry = findGeometry(firstView, 'interval');\n  const rightGeometry = findGeometry(secondView, 'interval');\n\n  if (!label) {\n    leftGeometry.label(false);\n    rightGeometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    /** ---- 设置默认配置 ---- */\n    // 默认居中\n    if (!cfg.position) {\n      cfg.position = 'middle';\n    }\n    if (cfg.offset === undefined) {\n      cfg.offset = 2;\n    }\n\n    /** ---- leftView label 设置 ---- */\n    const leftLabelCfg = { ...cfg };\n    if (isHorizontal(layout)) {\n      // 设置 textAlign 默认值\n      const textAlign = leftLabelCfg.style?.textAlign || (cfg.position === 'middle' ? 'center' : 'left');\n      cfg.style = deepAssign({}, cfg.style, { textAlign });\n      const textAlignMap = { left: 'right', right: 'left', center: 'center' };\n      leftLabelCfg.style = deepAssign({}, leftLabelCfg.style, { textAlign: textAlignMap[textAlign] });\n    } else {\n      const positionMap = { top: 'bottom', bottom: 'top', middle: 'middle' };\n      if (typeof cfg.position === 'string') {\n        cfg.position = positionMap[cfg.position];\n      } else if (typeof cfg.position === 'function') {\n        cfg.position = (...args) => positionMap[(cfg.position as any).apply(this, args)];\n      }\n      // 设置 textBaseline 默认值\n      const textBaseline = leftLabelCfg.style?.textBaseline || 'bottom';\n      leftLabelCfg.style = deepAssign({}, leftLabelCfg.style, { textBaseline });\n      const textBaselineMap = { top: 'bottom', bottom: 'top', middle: 'middle' };\n      cfg.style = deepAssign({}, cfg.style, { textBaseline: textBaselineMap[textBaseline] });\n    }\n\n    leftGeometry.label({\n      fields: [yField[0]],\n      callback,\n      cfg: transformLabel(leftLabelCfg),\n    });\n    rightGeometry.label({\n      fields: [yField[1]],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * 对称条形图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<BidirectionalBarOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(geometry, meta, axis, limitInPlot, theme, label, tooltip, interaction, animation)(params);\n}\n"]}