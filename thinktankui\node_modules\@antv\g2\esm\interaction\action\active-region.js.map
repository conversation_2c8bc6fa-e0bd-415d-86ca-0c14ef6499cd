{"version": 3, "file": "active-region.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/active-region.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAE3F,OAAO,EAAE,2BAA2B,EAAE,MAAM,oBAAoB,CAAC;AAIjE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,IAAM,yBAAyB,GAAG;IAChC,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,GAAG;CACb,CAAC;AAEF,MAAM,UAAU,cAAc,CAAC,IAAU,EAAE,KAAY,EAAE,UAAsB;;IAC7E,IAAI,KAAK,GAAG,2BAA2B,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;IACjE,IAAI,KAAK,CAAC,MAAM,EAAE;QAChB,KAAK;QACL,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;;YACvB,KAAsB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;gBAAxB,IAAM,OAAO,kBAAA;;oBAChB,KAAmB,IAAA,2BAAA,SAAA,OAAO,CAAA,CAAA,gCAAA,qDAAE;wBAAvB,IAAM,IAAI,oBAAA;wBACP,IAAA,KAAW,IAAI,CAAC,WAAW,EAAzB,CAAC,OAAA,EAAE,CAAC,OAAqB,CAAC;wBAClC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3C;;;;;;;;;aACF;;;;;;;;;QAEO,IAAA,MAAM,GAAK,UAAU,OAAf,CAAgB;QAC9B,gFAAgF;QAChF,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACxC,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAC5C,KAAoB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;oBAAtB,IAAM,KAAK,kBAAA;oBACd,IAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,IAAI,SAAS,IAAI,GAAG,EAAE;wBACpB,QAAQ,GAAG,KAAK,CAAC;wBACjB,GAAG,GAAG,SAAS,CAAC;qBACjB;iBACF;;;;;;;;;YACD,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC;SACpB;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;KAC7B;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH;IAA2B,gCAAM;IAAjC;;IAiKA,CAAC;IA9JC;;;;;OAKG;IACI,2BAAI,GAAX,UAAY,IAAwE;QAClF,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAE9B,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;QACjE,IAAM,YAAY,GAAG,cAAc,CACjC,IAAI,EACJ;YACE,CAAC,EAAE,EAAE,CAAC,CAAC;YACP,CAAC,EAAE,EAAE,CAAC,CAAC;SACR,EACD,UAAU,CACX,CAAC;QAEF,IAAI,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YACrC,qBAAqB;YACrB,OAAO;SACR;QACD,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;QAC1B,IAAI,YAAY,CAAC,MAAM,EAAE;YACvB,IAAM,QAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC;YACtC,IAAM,QAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;YAC5C,uBAAuB;YACvB,IAAI,UAAQ,GAAc,EAAE,CAAC;YAC7B,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACnC,IAAI,CAAC,UAAU,EAAE,UAAC,QAAQ;gBACxB,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;oBAC9D,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAC,GAAG;wBACxC,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;wBAC9B,OAAO,OAAO,CAAC,QAAM,CAAC,KAAK,QAAM,CAAC;oBACpC,CAAC,CAAC,CAAC;oBAEH,UAAQ,GAAG,UAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBACpC;YACH,CAAC,CAAC,CAAC;YAEH,qBAAqB;YACrB,IAAI,UAAQ,CAAC,MAAM,EAAE;gBACnB,IAAM,YAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBAExC,IAAI,WAAS,GAAG,UAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBAClD,IAAI,UAAQ,GAAG,UAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjD,IAAM,WAAS,GAAgB,WAAS,CAAC;gBAEzC,IAAI,CAAC,UAAQ,EAAE,UAAC,GAAY;oBAC1B,IAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;oBACvC,IAAI,YAAU,CAAC,YAAY,EAAE;wBAC3B,IAAI,IAAI,CAAC,IAAI,GAAG,WAAS,CAAC,IAAI,EAAE;4BAC9B,WAAS,GAAG,IAAI,CAAC;yBAClB;wBACD,IAAI,IAAI,CAAC,IAAI,GAAG,UAAQ,CAAC,IAAI,EAAE;4BAC7B,UAAQ,GAAG,IAAI,CAAC;yBACjB;qBACF;yBAAM;wBACL,IAAI,IAAI,CAAC,IAAI,GAAG,WAAS,CAAC,IAAI,EAAE;4BAC9B,WAAS,GAAG,IAAI,CAAC;yBAClB;wBACD,IAAI,IAAI,CAAC,IAAI,GAAG,UAAQ,CAAC,IAAI,EAAE;4BAC7B,UAAQ,GAAG,IAAI,CAAC;yBACjB;qBACF;oBAED,WAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAS,CAAC,IAAI,CAAC,CAAC;oBAClD,WAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAS,CAAC,IAAI,CAAC,CAAC;oBAClD,WAAS,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAS,CAAC,IAAI,CAAC,GAAG,WAAS,CAAC,CAAC,CAAC;oBACpE,WAAS,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,WAAS,CAAC,IAAI,CAAC,GAAG,WAAS,CAAC,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBAEK,IAAA,eAAe,GAAqB,IAAI,gBAAzB,EAAE,cAAc,GAAK,IAAI,eAAT,CAAU;gBACjD,IAAI,IAAI,SAAA,CAAC;gBACT,IAAI,YAAU,CAAC,MAAM,EAAE;oBACrB,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAE5B,IAAA,KAA+B,IAAI,IAAI,EAAE,EAAvC,WAAW,iBAAA,EAAE,WAAW,iBAAe,CAAC;oBAC9C,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;wBACtB,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,+BAA+B;wBAC9G,WAAW,GAAG,YAAU,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,GAAG,UAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,WAAS,CAAC,KAAK,CAAC;qBACvG;oBAED,IAAI,IAAI,SAAQ,CAAC;oBACjB,IAAI,IAAI,SAAQ,CAAC;oBACjB,IAAI,KAAK,SAAQ,CAAC;oBAClB,IAAI,MAAM,SAAQ,CAAC;oBACnB,IAAI,YAAU,CAAC,YAAY,EAAE;wBAC3B,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;wBAC3B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAQ,CAAC,IAAI,EAAE,WAAS,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;wBAC7D,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;wBAC7B,MAAM,GAAG,WAAS,CAAC,MAAM,GAAG,WAAW,GAAG,CAAC,CAAC;qBAC7C;yBAAM;wBACL,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,WAAS,CAAC,IAAI,EAAE,UAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;wBAC7D,4BAA4B;wBAC5B,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;wBAC3B,KAAK,GAAG,WAAS,CAAC,KAAK,GAAG,WAAW,GAAG,CAAC,CAAC;wBAC1C,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;qBAChC;oBAED,IAAI,GAAG;wBACL,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;wBACjB,CAAC,GAAG,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;wBACzB,CAAC,GAAG,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG,MAAM,CAAC;wBAClC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC;wBAC1B,CAAC,GAAG,CAAC;qBACN,CAAC;iBACH;qBAAM;oBACL,IAAM,YAAY,GAAG,IAAI,CAAC,UAAQ,CAAC,CAAC;oBACpC,IAAM,WAAW,GAAG,IAAI,CAAC,UAAQ,CAAC,CAAC;oBAC3B,IAAA,UAAU,GAAK,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,YAAU,CAAC,WAAlD,CAAmD;oBAC7D,IAAA,QAAQ,GAAK,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,YAAU,CAAC,SAAjD,CAAkD;oBAClE,IAAM,MAAM,GAAG,YAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,IAAM,MAAM,GAAG,YAAU,CAAC,SAAS,EAAE,CAAC;oBACtC,IAAM,YAAY,GAAG,YAAU,CAAC,WAAW,GAAG,MAAM,CAAC;oBACrD,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;iBACtF;gBAED,IAAI,IAAI,CAAC,UAAU,EAAE;oBACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;iBACxB;qBAAM;oBACL,IAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;oBAC5D,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,QAAQ,CAAC;wBACzC,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,eAAe;wBACrB,OAAO,EAAE,KAAK;wBACd,KAAK,wBACA,KAAK,KACR,IAAI,MAAA,GACL;qBACF,CAAC,CAAC;iBACJ;aACF;SACF;IACH,CAAC;IACD;;OAEG;IACI,2BAAI,GAAX;QACE,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;SACxB;QACD,0BAA0B;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IACD;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC9B;QACD,iBAAM,OAAO,WAAE,CAAC;IAClB,CAAC;IACH,mBAAC;AAAD,CAAC,AAjKD,CAA2B,MAAM,GAiKhC;AAED,eAAe,YAAY,CAAC", "sourcesContent": ["import { each, head, isEqual, last, get, flatten, isArray, uniq, isNil } from '@antv/util';\nimport View from '../../chart/view';\nimport { findItemsFromViewRecurisive } from '../../util/tooltip';\nimport { IShape, Point, ShapeAttrs } from '../../dependents';\nimport Element from '../../geometry/element';\nimport { LooseObject, TooltipCfg } from '../../interface';\nimport { getAngle, getSectorPath } from '../../util/graphics';\nimport Action from './base';\n\nconst DEFAULT_REGION_PATH_STYLE = {\n  fill: '#CCD6EC',\n  opacity: 0.3,\n};\n\nexport function getItemsOfView(view: View, point: Point, tooltipCfg: TooltipCfg) {\n  let items = findItemsFromViewRecurisive(view, point, tooltipCfg);\n  if (items.length) {\n    // 三层\n    items = flatten(items);\n    for (const itemArr of items) {\n      for (const item of itemArr) {\n        const { x, y } = item.mappingData;\n        item.x = isArray(x) ? x[x.length - 1] : x;\n        item.y = isArray(y) ? y[y.length - 1] : y;\n      }\n    }\n\n    const { shared } = tooltipCfg;\n    // shared: false 代表只显示当前拾取到的 shape 的数据，但是一个 view 会有多个 Geometry，所以有可能会拾取到多个 shape\n    if (shared === false && items.length > 1) {\n      let snapItem = items[0];\n      let min = Math.abs(point.y - snapItem[0].y);\n      for (const aItem of items) {\n        const yDistance = Math.abs(point.y - aItem[0].y);\n        if (yDistance <= min) {\n          snapItem = aItem;\n          min = yDistance;\n        }\n      }\n      items = [snapItem];\n    }\n\n    return uniq(flatten(items));\n  }\n\n  return [];\n}\n\n/**\n * 背景框的 Action. 只作用于 interval 和 schema geometry\n * @ignore\n */\nclass ActiveRegion extends Action {\n  private items: any[];\n  private regionPath: IShape;\n  /**\n   * 显示\n   * @param {ShapeAttrs} style region-path 的样式\n   * @param {number} appendRatio 适用于笛卡尔坐标系. 对于 x 轴非 linear 类型: 默认：0.25, x 轴 linear 类型: 默认 0\n   * @param {number} appendWidth  适用于笛卡尔坐标系. 像素级别，优先级 > appendRatio\n   */\n  public show(args?: { style: ShapeAttrs; appendRatio?: number; appendWidth?: number }) {\n    const view = this.context.view;\n    const ev = this.context.event;\n\n    const tooltipCfg = view.getController('tooltip').getTooltipCfg();\n    const tooltipItems = getItemsOfView(\n      view,\n      {\n        x: ev.x,\n        y: ev.y,\n      },\n      tooltipCfg\n    );\n\n    if (isEqual(tooltipItems, this.items)) {\n      // 如果拾取数据同上次相同，则不重复绘制\n      return;\n    }\n    this.items = tooltipItems;\n    if (tooltipItems.length) {\n      const xField = view.getXScale().field;\n      const xValue = tooltipItems[0].data[xField];\n      // 根据 x 对应的值查找 elements\n      let elements: Element[] = [];\n      const geometries = view.geometries;\n      each(geometries, (geometry) => {\n        if (geometry.type === 'interval' || geometry.type === 'schema') {\n          const result = geometry.getElementsBy((ele) => {\n            const eleData = ele.getData();\n            return eleData[xField] === xValue;\n          });\n\n          elements = elements.concat(result);\n        }\n      });\n\n      // 根据 bbox 计算背景框的面积区域\n      if (elements.length) {\n        const coordinate = view.getCoordinate();\n\n        let firstBBox = elements[0].shape.getCanvasBBox();\n        let lastBBox = elements[0].shape.getCanvasBBox();\n        const groupBBox: LooseObject = firstBBox;\n\n        each(elements, (ele: Element) => {\n          const bbox = ele.shape.getCanvasBBox();\n          if (coordinate.isTransposed) {\n            if (bbox.minY < firstBBox.minY) {\n              firstBBox = bbox;\n            }\n            if (bbox.maxY > lastBBox.maxY) {\n              lastBBox = bbox;\n            }\n          } else {\n            if (bbox.minX < firstBBox.minX) {\n              firstBBox = bbox;\n            }\n            if (bbox.maxX > lastBBox.maxX) {\n              lastBBox = bbox;\n            }\n          }\n\n          groupBBox.x = Math.min(bbox.minX, groupBBox.minX);\n          groupBBox.y = Math.min(bbox.minY, groupBBox.minY);\n          groupBBox.width = Math.max(bbox.maxX, groupBBox.maxX) - groupBBox.x;\n          groupBBox.height = Math.max(bbox.maxY, groupBBox.maxY) - groupBBox.y;\n        });\n\n        const { backgroundGroup, coordinateBBox } = view;\n        let path;\n        if (coordinate.isRect) {\n          const xScale = view.getXScale();\n\n          let { appendRatio, appendWidth } = args || {};\n          if (isNil(appendWidth)) {\n            appendRatio = isNil(appendRatio) ? (xScale.isLinear ? 0 : 0.25) : appendRatio; // 如果 x 轴是数值类型，如直方图，默认不需要加额外的宽度\n            appendWidth = coordinate.isTransposed ? appendRatio * lastBBox.height : appendRatio * firstBBox.width;\n          }\n\n          let minX: number;\n          let minY: number;\n          let width: number;\n          let height: number;\n          if (coordinate.isTransposed) {\n            minX = coordinateBBox.minX;\n            minY = Math.min(lastBBox.minY, firstBBox.minY) - appendWidth;\n            width = coordinateBBox.width;\n            height = groupBBox.height + appendWidth * 2;\n          } else {\n            minX = Math.min(firstBBox.minX, lastBBox.minX) - appendWidth;\n            // 直角坐标系 非转置：最小值直接取 坐标系 minY\n            minY = coordinateBBox.minY;\n            width = groupBBox.width + appendWidth * 2;\n            height = coordinateBBox.height;\n          }\n\n          path = [\n            ['M', minX, minY],\n            ['L', minX + width, minY],\n            ['L', minX + width, minY + height],\n            ['L', minX, minY + height],\n            ['Z'],\n          ];\n        } else {\n          const firstElement = head(elements);\n          const lastElement = last(elements);\n          const { startAngle } = getAngle(firstElement.getModel(), coordinate);\n          const { endAngle } = getAngle(lastElement.getModel(), coordinate);\n          const center = coordinate.getCenter();\n          const radius = coordinate.getRadius();\n          const innterRadius = coordinate.innerRadius * radius;\n          path = getSectorPath(center.x, center.y, radius, startAngle, endAngle, innterRadius);\n        }\n\n        if (this.regionPath) {\n          this.regionPath.attr('path', path);\n          this.regionPath.show();\n        } else {\n          const style = get(args, 'style', DEFAULT_REGION_PATH_STYLE);\n          this.regionPath = backgroundGroup.addShape({\n            type: 'path',\n            name: 'active-region',\n            capture: false,\n            attrs: {\n              ...style,\n              path,\n            },\n          });\n        }\n      }\n    }\n  }\n  /**\n   * 隐藏\n   */\n  public hide() {\n    if (this.regionPath) {\n      this.regionPath.hide();\n    }\n    // this.regionPath = null;\n    this.items = null;\n  }\n  /**\n   * 销毁\n   */\n  public destroy() {\n    this.hide();\n    if (this.regionPath) {\n      this.regionPath.remove(true);\n    }\n    super.destroy();\n  }\n}\n\nexport default ActiveRegion;\n"]}