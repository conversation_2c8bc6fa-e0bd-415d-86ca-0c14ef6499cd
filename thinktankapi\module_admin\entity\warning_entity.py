from datetime import datetime
from sqlalchemy import Column, <PERSON>te<PERSON>, BigInteger, String, Text, DateTime, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from config.database import Base


class WarningScheme(Base):
    """
    预警方案表
    """
    __tablename__ = 'warning_scheme'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='方案ID')
    scheme_name = Column(String(100), nullable=False, comment='方案名称')
    scheme_type = Column(String(50), default='default', comment='方案类型')
    description = Column(Text, comment='方案描述')
    is_active = Column(Boolean, default=True, comment='是否启用')
    create_by = Column(String(64), comment='创建者')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(String(64), comment='更新者')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 关联关系
    settings = relationship("WarningSettings", back_populates="scheme", cascade="all, delete-orphan")
    records = relationship("WarningRecord", back_populates="scheme", cascade="all, delete-orphan")


class WarningSettings(Base):
    """
    预警设置表
    """
    __tablename__ = 'warning_settings'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='设置ID')
    scheme_id = Column(BigInteger, ForeignKey('warning_scheme.id'), nullable=False, comment='方案ID')
    platform_types = Column(JSON, comment='平台类型设置')
    content_property = Column(String(50), comment='内容属性')
    info_type = Column(String(50), comment='信息类型')
    match_objects = Column(JSON, comment='匹配对象')
    match_method = Column(String(50), comment='匹配方式')
    publish_regions = Column(JSON, comment='发布地区')
    ip_areas = Column(JSON, comment='IP属地')
    media_categories = Column(JSON, comment='媒体类别')
    article_categories = Column(JSON, comment='文章类别')
    create_by = Column(String(64), comment='创建者')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_by = Column(String(64), comment='更新者')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    # 关联关系
    scheme = relationship("WarningScheme", back_populates="settings")


class WarningRecord(Base):
    """
    预警记录表
    """
    __tablename__ = 'warning_record'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='记录ID')
    scheme_id = Column(Integer, ForeignKey('warning_scheme.id'), nullable=False, comment='方案ID')
    warning_type = Column(String(50), nullable=False, comment='预警类型')
    content = Column(Text, comment='预警内容')
    keywords = Column(Text, comment='关键词')
    status = Column(Integer, default=0, comment='状态：0-未处理，1-已处理，2-已忽略')
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    create_by = Column(String(64), default='', comment='创建者')
    update_by = Column(String(64), default='', comment='更新者')
    remark = Column(String(500), comment='备注')

    # 关联关系
    scheme = relationship("WarningScheme", back_populates="records")
