{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/column/adaptor.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EACL,SAAS,EACT,UAAU,EACV,WAAW,EACX,WAAW,EACX,KAAK,EACL,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,eAAe,GAChB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAGhD,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AACtG,OAAO,EAAE,wBAAwB,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAGzF;;;GAGG;AACH,SAAS,cAAc,CAAC,MAA6B;IAC3C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IAC3B,eAAe;IACT,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IACjB,IAAA,WAAW,GAAc,OAAO,YAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAEzC,IAAI,WAAW,EAAE;QACf,IAAI,MAAM,KAAK,KAAK,EAAE;YACpB,MAAM,cACJ,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,IACzC,MAAM,CACV,CAAC;SACH;KACF;SAAM;QACL,MAAM,GAAG,KAAK,CAAC;KAChB;IAED,kBAAkB;IAClB,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAC/B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAEhC,IAAA,IAAI,GAaF,OAAO,KAbL,EACJ,WAAW,GAYT,OAAO,YAZE,EACX,KAAK,GAWH,OAAO,MAXJ,EACL,gBAAgB,GAUd,OAAO,iBAVO,EAChB,SAAS,GASP,OAAO,UATA,EACT,OAAO,GAQL,OAAO,QARF,EACP,OAAO,GAOL,OAAO,QAPF,EACP,MAAM,GAMJ,OAAO,OANH,EACN,MAAM,GAKJ,OAAO,OALH,EACN,WAAW,GAIT,OAAO,YAJE,EACX,UAAU,GAGR,OAAO,WAHC,EACV,OAAO,GAEL,OAAO,QAFF,EACP,KAAK,GACH,OAAO,MADJ,CACK;IAEZ,IAAM,WAAW,GACf,SAAS,IAAI,OAAO,IAAI,OAAO;QAC7B,CAAC,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC;QAC5D,CAAC,CAAC,wBAAwB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;IAExE,IAAI,SAAS,GAAG,EAAE,CAAC;IAEnB,2CAA2C;IAC3C,IAAI,OAAO,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;QACtC,WAAW,CAAC,OAAO,CAAC,UAAC,IAAI;YACvB,IAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,EAAlE,CAAkE,CAAC,CAAC;YAC9G,IAAI,WAAW,EAAE;gBACf,WAAW,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aAC1C;iBAAM;gBACL,SAAS,CAAC,IAAI,cAAM,IAAI,EAAG,CAAC;aAC7B;QACH,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,SAAS,GAAG,WAAW,CAAC;KACzB;IAED,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAEtB,gCAAgC;IAChC,IAAM,cAAc,GAAG,SAAS;QAC9B,CAAC,YACG,SAAS,EAAE,UAAC,KAAY;;gBAAK,OAAA,CAAC;oBAC5B,IAAI,EACF,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,UAAG,KAAK,CAAC,WAAW,CAAC,gBAAM,KAAK,CAAC,UAAU,CAAC,CAAE,CAAC,CAAC,CAAC,MAAA,KAAK,CAAC,WAAW,CAAC,mCAAI,KAAK,CAAC,MAAM,CAAC;oBAC3G,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;iBACtD,CAAC,CAAA;aAAA,IACC,OAAO,EAEd,CAAC,CAAC,OAAO,CAAC;IAEZ,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,gBAAgB;YAC5B,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE;gBACR,KAAK,OAAA;gBACL,KAAK,EAAE,WAAW;gBAClB,KAAK,OAAA;aACN;SACF;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEZ,OAAO,CAAC,CAAC;AACX,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA6B;;IACxC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA6C,OAAO,MAApD,EAAE,KAAK,GAAsC,OAAO,MAA7C,EAAE,MAAM,GAA8B,OAAO,OAArC,EAAE,MAAM,GAAsB,OAAO,OAA7B,EAAE,IAAI,GAAgB,OAAO,KAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAElE,IAAM,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAEnF,OAAO,IAAI,CACT,KAAK;QAED,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;;QAGf,GAAC,MAAM,IAAG;YACR,IAAI,EAAE,KAAK;SACZ;QACD,GAAC,MAAM,0BACF,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,GAC/B,YAAY,CAChB;YAEJ,CACF,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA6B;IACjC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,iBAAiB;IACjB,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;SAAM;QACL,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC3B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,MAA6B;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAkB,OAAO,OAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAExC,IAAI,MAAM,IAAI,WAAW,EAAE;QACzB,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;KACnC;SAAM,IAAI,MAAM,KAAK,KAAK,EAAE;QAC3B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA6B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAsB,OAAO,MAA7B,EAAE,MAAM,GAAc,OAAO,OAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE3C,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;IAEjD,IAAI,CAAC,KAAK,EAAE;QACV,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KACvB;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,QAAQ,CAAC,KAAK,CAAC;YACb,MAAM,EAAE,CAAC,MAAM,CAAC;YAChB,QAAQ,UAAA;YACR,GAAG;gBACD,+DAA+D;gBAC/D,MAAM,EAAE,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,QAAQ;oBACnB,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC;wBACE,EAAE,IAAI,EAAE,0BAA0B,EAAE;wBACpC,EAAE,IAAI,EAAE,uBAAuB,EAAE;wBACjC,EAAE,IAAI,EAAE,cAAc,EAAE;wBACxB,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;qBACnD,IACF,cAAc,CACf,OAAO;gBACL,CAAC,YACG,OAAO,EAAE,UAAC,IAAY;;wBACpB,OAAO,MAAA,IAAI,CAAC,MAAM,CAAC,0CAAE,IAAI,CAAC,GAAG,CAAC,CAAC;oBACjC,CAAC,IACE,GAAG,EAEV,CAAC,CAAC,GAAG,CACR,CACF;SACF,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,MAA6B;IAC1C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAsE,OAAO,QAA7E,EAAE,OAAO,GAA6D,OAAO,QAApE,EAAE,OAAO,GAAoD,OAAO,QAA3D,EAAE,UAAU,GAAwC,OAAO,WAA/C,EAAE,IAAI,GAAkC,OAAO,KAAzC,EAAE,MAAM,GAA0B,OAAO,OAAjC,EAAE,MAAM,GAAkB,OAAO,OAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAE7F,IAAI,OAAO,KAAK,KAAK,EAAE;QACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM;QACL,IAAI,cAAc,GAAG,OAAO,CAAC;QAC7B,oDAAoD;QACpD,IAAI,OAAO,IAAI,OAAO,EAAE;YACd,IAAA,aAAW,GAAK,cAAc,YAAnB,CAAoB;YACvC,IAAM,kBAAgB,GACpB,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS;gBACzB,CAAC,UAAC,KAAY,IAAK,OAAA,CAAC,EAAE,IAAI,EAAE,UAAG,KAAK,CAAC,WAAW,CAAC,gBAAM,KAAK,CAAC,UAAU,CAAC,CAAE,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAhF,CAAgF,CAAC,CAAC;YACvG,cAAc,yBACT,cAAc,KACjB,WAAW,EAAE,UAAC,aAAkC;oBAC9C,IAAM,KAAK,GAAwB,EAAE,CAAC;oBACtC,IAAI,CAAC,aAAa,EAAE,UAAC,IAAuB;wBAC1C,6BAA6B;wBAC7B,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,UAAC,CAAC,IAAK,OAAA,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAlD,CAAkD,CAAC,CAAC;wBACtF,KAAK,CAAC,OAAO,CAAC,UAAC,KAAK;4BAClB,KAAK,CAAC,IAAI,gCACL,IAAI,KACP,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,EACpB,IAAI,EAAE,KAAK,EACX,WAAW,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,KAC5B,kBAAgB,CAAC,KAAK,CAAC,EAC1B,CAAC;wBACL,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBACH,mDAAmD;oBACnD,OAAO,aAAW,CAAC,CAAC,CAAC,aAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAClD,CAAC,GACF,CAAC;SACH;QACD,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;KAC/B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B,EAAE,KAAa;IAAb,sBAAA,EAAA,aAAa;IAC1D,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAa;IAChC,OAAO,IAAI,CACT,cAAc,EAAE,SAAS;IACzB,KAAK,EAAE,wBAAwB;IAC/B,OAAO,CAAC,aAAa,CAAC,EACtB,KAAK,EACL,eAAe,CAAC,MAAM,CAAC,EACvB,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,aAAa,EACb,MAAM,EACN,SAAS,EACT,KAAK,EACL,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,UAAU,EAAE,EACZ,aAAa,CAAgB,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,WAAW,CAAC,EAAE,cAAc;IACnF,aAAa,CAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,EAC9C,WAAW,CACZ,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { Types } from '@antv/g2';\nimport { each, filter, isMatch } from '@antv/util';\nimport { brushInteraction } from '../../adaptor/brush';\nimport {\n  animation,\n  annotation,\n  interaction,\n  limitInPlot,\n  scale,\n  scrollbar,\n  slider,\n  state,\n  theme,\n  transformations,\n} from '../../adaptor/common';\nimport { connectedArea } from '../../adaptor/connected-area';\nimport { conversionTag } from '../../adaptor/conversion-tag';\nimport { interval } from '../../adaptor/geometries';\nimport { pattern } from '../../adaptor/pattern';\nimport { Params } from '../../core/adaptor';\nimport { Datum } from '../../types';\nimport { adjustYMetaByZero, deepAssign, findGeometry, flow, pick, transformLabel } from '../../utils';\nimport { getDataWhetherPercentage, getDeepPercent } from '../../utils/transform/percent';\nimport { ColumnOptions } from './types';\n\n/**\n * defaultOptions\n * @param params\n */\nfunction defaultOptions(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { options } = params;\n  // 默认 legend 位置\n  let { legend } = options;\n  const { seriesField, isStack } = options;\n\n  if (seriesField) {\n    if (legend !== false) {\n      legend = {\n        position: isStack ? 'right-top' : 'top-left',\n        ...legend,\n      };\n    }\n  } else {\n    legend = false;\n  }\n\n  // @ts-ignore 直接改值\n  params.options.legend = legend;\n  return params;\n}\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { chart, options } = params;\n  const {\n    data,\n    columnStyle,\n    color,\n    columnWidthRatio,\n    isPercent,\n    isGroup,\n    isStack,\n    xField,\n    yField,\n    seriesField,\n    groupField,\n    tooltip,\n    shape,\n  } = options;\n\n  const percentData =\n    isPercent && isGroup && isStack\n      ? getDeepPercent(data, yField, [xField, groupField], yField)\n      : getDataWhetherPercentage(data, yField, xField, yField, isPercent);\n\n  let chartData = [];\n\n  // 存在堆叠,并且存在堆叠seriesField分类，并且不存在分组的时候 进行堆叠\n  if (isStack && seriesField && !isGroup) {\n    percentData.forEach((item) => {\n      const stackedItem = chartData.find((v) => v[xField] === item[xField] && v[seriesField] === item[seriesField]);\n      if (stackedItem) {\n        stackedItem[yField] += item[yField] || 0;\n      } else {\n        chartData.push({ ...item });\n      }\n    });\n  } else {\n    chartData = percentData;\n  }\n\n  chart.data(chartData);\n\n  // 百分比堆积图，默认会给一个 % 格式化逻辑, 用户可自定义\n  const tooltipOptions = isPercent\n    ? {\n        formatter: (datum: Datum) => ({\n          name:\n            isGroup && isStack ? `${datum[seriesField]} - ${datum[groupField]}` : datum[seriesField] ?? datum[xField],\n          value: (Number(datum[yField]) * 100).toFixed(2) + '%',\n        }),\n        ...tooltip,\n      }\n    : tooltip;\n\n  const p = deepAssign({}, params, {\n    options: {\n      data: chartData,\n      widthRatio: columnWidthRatio,\n      tooltip: tooltipOptions,\n      interval: {\n        shape,\n        style: columnStyle,\n        color,\n      },\n    },\n  });\n  interval(p);\n\n  return p;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField, data, isPercent } = options;\n\n  const percentYMeta = isPercent ? { max: 1, min: 0, minLimit: 0, maxLimit: 1 } : {};\n\n  return flow(\n    scale(\n      {\n        [xField]: xAxis,\n        [yField]: yAxis,\n      },\n      {\n        [xField]: {\n          type: 'cat',\n        },\n        [yField]: {\n          ...adjustYMetaByZero(data, yField),\n          ...percentYMeta,\n        },\n      }\n    )\n  )(params);\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  // 为 false 则是不显示轴\n  if (xAxis === false) {\n    chart.axis(xField, false);\n  } else {\n    chart.axis(xField, xAxis);\n  }\n\n  if (yAxis === false) {\n    chart.axis(yField, false);\n  } else {\n    chart.axis(yField, yAxis);\n  }\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nexport function legend(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { chart, options } = params;\n  const { legend, seriesField } = options;\n\n  if (legend && seriesField) {\n    chart.legend(seriesField, legend);\n  } else if (legend === false) {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { chart, options } = params;\n  const { label, yField, isRange } = options;\n\n  const geometry = findGeometry(chart, 'interval');\n\n  if (!label) {\n    geometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    geometry.label({\n      fields: [yField],\n      callback,\n      cfg: {\n        // 配置默认的 label layout： 如果用户没有指定 layout 和 position， 则自动配置 layout\n        layout: cfg?.position\n          ? undefined\n          : [\n              { type: 'interval-adjust-position' },\n              { type: 'interval-hide-overlap' },\n              { type: 'adjust-color' },\n              { type: 'limit-in-plot', cfg: { action: 'hide' } },\n            ],\n        ...transformLabel(\n          isRange\n            ? {\n                content: (item: object) => {\n                  return item[yField]?.join('-');\n                },\n                ...cfg,\n              }\n            : cfg\n        ),\n      },\n    });\n  }\n\n  return params;\n}\n\n/**\n * 柱形图 tooltip 配置 (对堆叠、分组做特殊处理)\n * @param params\n */\nfunction columnTooltip(params: Params<ColumnOptions>): Params<ColumnOptions> {\n  const { chart, options } = params;\n  const { tooltip, isGroup, isStack, groupField, data, xField, yField, seriesField } = options;\n\n  if (tooltip === false) {\n    chart.tooltip(false);\n  } else {\n    let tooltipOptions = tooltip;\n    // fix: https://github.com/antvis/G2Plot/issues/2572\n    if (isGroup && isStack) {\n      const { customItems } = tooltipOptions;\n      const tooltipFormatter =\n        tooltipOptions?.formatter ||\n        ((datum: Datum) => ({ name: `${datum[seriesField]} - ${datum[groupField]}`, value: datum[yField] }));\n      tooltipOptions = {\n        ...tooltipOptions,\n        customItems: (originalItems: Types.TooltipItem[]) => {\n          const items: Types.TooltipItem[] = [];\n          each(originalItems, (item: Types.TooltipItem) => {\n            // Find datas in same cluster\n            const datas = filter(data, (d) => isMatch(d, pick(item.data, [xField, seriesField])));\n            datas.forEach((datum) => {\n              items.push({\n                ...item,\n                value: datum[yField],\n                data: datum,\n                mappingData: { _origin: datum },\n                ...tooltipFormatter(datum),\n              });\n            });\n          });\n          // fix https://github.com/antvis/G2Plot/issues/3367\n          return customItems ? customItems(items) : items;\n        },\n      };\n    }\n    chart.tooltip(tooltipOptions);\n  }\n\n  return params;\n}\n\n/**\n * 柱形图适配器\n * @param params\n */\nexport function adaptor(params: Params<ColumnOptions>, isBar = false) {\n  const { options } = params;\n  const { seriesField } = options;\n  return flow(\n    defaultOptions, // 处理默认配置\n    theme, // theme 需要在 geometry 之前\n    pattern('columnStyle'),\n    state,\n    transformations('rect'),\n    geometry,\n    meta,\n    axis,\n    legend,\n    columnTooltip,\n    slider,\n    scrollbar,\n    label,\n    brushInteraction,\n    interaction,\n    animation,\n    annotation(),\n    conversionTag<ColumnOptions>(options.yField, !isBar, !!seriesField), // 有拆分的时候禁用转化率\n    connectedArea<ColumnOptions>(!options.isStack),\n    limitInPlot\n  )(params);\n}\n"]}