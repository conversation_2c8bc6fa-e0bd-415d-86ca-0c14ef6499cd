{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\warning-center\\index.vue", "mtime": 1749112543312}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQovLyDlr7zlhaXliIbpobXnu4Tku7bvvIjlpoLmnpzpnIDopoHvvIkNCi8vIGltcG9ydCBQYWdpbmF0aW9uIGZyb20gIkAvY29tcG9uZW50cy9QYWdpbmF0aW9uIjsNCmltcG9ydCB7DQogIGxpc3RXYXJuaW5nUmVjb3JkLA0KICBzYXZlV2FybmluZ1NldHRpbmdzLA0KICBzYXZlS2V5d29yZFNldHRpbmdzLA0KICBzYXZlQXV0b1dhcm5pbmdTZXR0aW5ncywNCiAgYWRkV2FybmluZ1NjaGVtZSwNCiAgbGlzdFdhcm5pbmdTY2hlbWUsDQogIGdldFdhcm5pbmdTdGF0aXN0aWNzDQp9IGZyb20gJ0AvYXBpL3dhcm5pbmcvaW5kZXgnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0luZm9TdW1tYXJ5JywNCiAgLy8g5rOo5YaM57uE5Lu277yI5aaC5p6c6ZyA6KaB77yJDQogIC8vIGNvbXBvbmVudHM6IHsNCiAgLy8gICBQYWdpbmF0aW9uDQogIC8vIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIG9yaWdpbmFsVG9wTmF2OiB1bmRlZmluZWQsIC8vIOWtmOWCqOWOn+Wni+eahHRvcE5hdueKtuaAgQ0KICAgICAgYXV0b1JlZnJlc2g6IHRydWUsDQogICAgICBzZWFyY2hUZXh0OiAnJywNCiAgICAgIGN1cnJlbnRQYWdlOiAxLA0KICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgdG90YWw6IDE1NiwNCiAgICAgIC8vIOmihOitpuiuvue9ruW8ueeql+ebuOWFs+aVsOaNrg0KICAgICAgcHVibGlzaFJlZ2lvbklucHV0OiAnJywNCiAgICAgIGlwQXJlYUlucHV0OiAnJywNCiAgICAgIHdhcm5pbmdEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOWFs+mUruivjeiuvue9ruaKveWxieebuOWFs+aVsOaNrg0KICAgICAga2V5d29yZERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAga2V5d29yZFNldHRpbmdzOiB7DQogICAgICAgIGFsbG93V29yZHM6ICflhYHorrjor43vvJrlr7nmlofmnKzov5vooYznrZvpgInvvIzlkb3kuK3mlofmnKznmoTlhoXlrrnkvJrooqvlhYHorrjpgJrov4cnLA0KICAgICAgICByZWplY3RXb3JkczogJ+aLkue7neivje+8muWvueaWh+acrOi/m+ihjOetm+mAie+8jOWRveS4reaWh+acrOeahOWGheWuueS8muiiq+aLkue7nemAmui/hycNCiAgICAgIH0sDQogICAgICAvLyDoh6rliqjpooToraborr7nva7mir3lsYnnm7jlhbPmlbDmja4NCiAgICAgIGF1dG9XYXJuaW5nRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBpbXBvcnRhbnRBY2NvdW50T25seTogdHJ1ZSwNCiAgICAgIGF1dG9XYXJuaW5nU2V0dGluZ3M6IHsNCiAgICAgICAgdGltZVJhbmdlOiB7DQogICAgICAgICAgc3RhcnRIb3VyOiAnMDYnLA0KICAgICAgICAgIHN0YXJ0TWludXRlOiAnMDAnLA0KICAgICAgICAgIGVuZEhvdXI6ICcxOCcsDQogICAgICAgICAgZW5kTWludXRlOiAnMDAnDQogICAgICAgIH0sDQogICAgICAgIHBsYXRmb3Jtczogew0KICAgICAgICAgIHdlaWJvOiB0cnVlLA0KICAgICAgICAgIHdlY2hhdDogdHJ1ZSwNCiAgICAgICAgICB3ZWJzaXRlOiB0cnVlLA0KICAgICAgICAgIGRvdXlpbjogdHJ1ZSwNCiAgICAgICAgICByZWRib29rOiB0cnVlLA0KICAgICAgICAgIGJpbGliaWxpOiBmYWxzZSwNCiAgICAgICAgICB6aGlodTogZmFsc2UNCiAgICAgICAgfSwNCiAgICAgICAgd2FybmluZ1R5cGU6ICduZWdhdGl2ZScsIC8vIG5lZ2F0aXZlLCBwb3NpdGl2ZSwgYWxsDQogICAgICAgIHByb2Nlc3NNZXRob2Q6ICdhbGwnLCAvLyBhbGwsIG9ubHlBbGVydA0KICAgICAgICBwcmlvcml0eTogJ25vcm1hbCcsIC8vIG5vcm1hbCwgdXJnZW50DQogICAgICAgIGhhbmRsZU1ldGhvZDogJ2F1dG8nLCAvLyBhdXRvLCBtYW51YWwNCiAgICAgICAgbm90aWZ5TWV0aG9kczogew0KICAgICAgICAgIHNtczogdHJ1ZSwNCiAgICAgICAgICBlbWFpbDogZmFsc2UsDQogICAgICAgICAgd2VjaGF0Tm90aWZ5OiB0cnVlDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICB3YXJuaW5nU2V0dGluZ3M6IHsNCiAgICAgICAgcGxhdGZvcm1UeXBlOiB7DQogICAgICAgICAgdGl0bGU6ICflubPlj7DnsbvlnosnLA0KICAgICAgICAgIG9wdGlvbnM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICflhajpg6gnLCB2YWx1ZTogJ2FsbCcsIGNoZWNrZWQ6IHRydWUgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfnvZHpobUnLCB2YWx1ZTogJ3dlYnBhZ2UnLCBjaGVja2VkOiB0cnVlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6u5L+hJywgdmFsdWU6ICd3ZWNoYXQnLCBjaGVja2VkOiB0cnVlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5b6u5Y2aJywgdmFsdWU6ICd3ZWlibycsIGNoZWNrZWQ6IHRydWUgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICflpLTmnaHlj7cnLCB2YWx1ZTogJ3RvdXRpYW8nLCBjaGVja2VkOiB0cnVlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAnQVBQJywgdmFsdWU6ICdhcHAnLCBjaGVja2VkOiB0cnVlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6KeG6aKRJywgdmFsdWU6ICd2aWRlbycsIGNoZWNrZWQ6IHRydWUgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICforrrlnZsnLCB2YWx1ZTogJ2ZvcnVtJywgY2hlY2tlZDogdHJ1ZSB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+aKpeWIiicsIHZhbHVlOiAnbmV3c3BhcGVyJywgY2hlY2tlZDogdHJ1ZSB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+mXruetlCcsIHZhbHVlOiAncWEnLCBjaGVja2VkOiB0cnVlIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIGNvbnRlbnRQcm9wZXJ0eTogew0KICAgICAgICAgIHRpdGxlOiAn5YaF5a655bGe5oCnJywNCiAgICAgICAgICB2YWx1ZTogJ2FsbCcsIC8vIGFsbCwgeWVzLCBubw0KICAgICAgICAgIG9wdGlvbnM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICflhajpg6gnLCB2YWx1ZTogJ2FsbCcgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmmK8nLCB2YWx1ZTogJ3llcycgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfkuI3mmK8nLCB2YWx1ZTogJ25vJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICBpbmZvVHlwZTogew0KICAgICAgICAgIHRpdGxlOiAn5L+h5oGv57G75Z6LJywNCiAgICAgICAgICB2YWx1ZTogJ25vbmNvbW1lbnQnLCAvLyBhbGwsIG5vbmNvbW1lbnQsIGNvbW1lbnQNCiAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5YWo6YOoJywgdmFsdWU6ICdhbGwnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6Z2e6K+E6K66JywgdmFsdWU6ICdub25jb21tZW50JyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+ivhOiuuicsIHZhbHVlOiAnY29tbWVudCcgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgbWF0Y2hPYmplY3Q6IHsNCiAgICAgICAgICB0aXRsZTogJ+WMuemFjeWvueixoScsDQogICAgICAgICAgYWxsQ2hlY2tlZDogdHJ1ZSwNCiAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5qCH6aKY5Yy56YWNJywgdmFsdWU6ICd0aXRsZScsIGNoZWNrZWQ6IGZhbHNlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5q2j5paH5Yy56YWNJywgdmFsdWU6ICdjb250ZW50JywgY2hlY2tlZDogZmFsc2UgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfpn7PpopEv5Zu+54mH5Yy56YWNJywgdmFsdWU6ICdtZWRpYScsIGNoZWNrZWQ6IGZhbHNlIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5Y6f5paH5Yy56YWNJywgdmFsdWU6ICdvcmlnaW5hbCcsIGNoZWNrZWQ6IGZhbHNlIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIG1hdGNoTWV0aG9kOiB7DQogICAgICAgICAgdGl0bGU6ICfljLnphY3mlrnlvI8nLA0KICAgICAgICAgIHZhbHVlOiAnZXhhY3QnLCAvLyBleGFjdCwgZnV6enkNCiAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn57K+5YeGJywgdmFsdWU6ICdleGFjdCcgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmqKHns4onLCB2YWx1ZTogJ2Z1enp5JyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICBwdWJsaXNoUmVnaW9uOiB7DQogICAgICAgICAgdGl0bGU6ICflj5HluIPlnLDljLonLA0KICAgICAgICAgIHJlZ2lvbnM6IFsNCiAgICAgICAgICAgIHsgbmFtZTogJ+WFqOmDqCcsIHZhbHVlOiAnYWxsJyB9DQogICAgICAgICAgXQ0KICAgICAgICB9LA0KICAgICAgICBpcEFyZWE6IHsNCiAgICAgICAgICB0aXRsZTogJ0lQ5bGe5ZywJywNCiAgICAgICAgICBhcmVhczogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5YWo6YOoJywgdmFsdWU6ICdhbGwnIH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIG1lZGlhQ2F0ZWdvcnk6IHsNCiAgICAgICAgICB0aXRsZTogJ+WqkuS9k+exu+WIqycsDQogICAgICAgICAgY291bnQ6IDANCiAgICAgICAgfSwNCiAgICAgICAgYXJ0aWNsZUNhdGVnb3J5OiB7DQogICAgICAgICAgdGl0bGU6ICfmlofnq6DnsbvliKsnLA0KICAgICAgICAgIGNvdW50OiAwDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICAvLyDkvqfovrnmoI/mlbDmja4NCiAgICAgIHNpZGViYXJDb2xsYXBzZWQ6IGZhbHNlLA0KICAgICAgc2lkZWJhclNlYXJjaFRleHQ6ICcnLA0KICAgICAgYWN0aXZlTWVudUl0ZW06ICfmlrnlpKonLA0KICAgICAgbWVudUNhdGVnb3JpZXM6IFsNCiAgICAgICAgeyBuYW1lOiAn5oC755uRJywgY291bnQ6IDEsIGNoaWxkcmVuOiBbXSwgaWNvbjogJ2VsLWljb24tdmlldycgfSwNCiAgICAgICAgeyBuYW1lOiAn5ZOB54mMJywgY291bnQ6IDEsIGNoaWxkcmVuOiBbXSwgaWNvbjogJ2VsLWljb24tc3Rhci1vbicgfSwNCiAgICAgICAgeyBuYW1lOiAn5pa55aSqJywgY291bnQ6IDAsIGlzSXRlbTogdHJ1ZSwgaWNvbjogJ2VsLWljb24tb2ZmaWNlLWJ1aWxkaW5nJyB9LA0KICAgICAgICB7IG5hbWU6ICfkurrniaknLCBjb3VudDogMCwgY2hpbGRyZW46IFtdLCBpY29uOiAnZWwtaWNvbi11c2VyJyB9LA0KICAgICAgICB7IG5hbWU6ICfmnLrmnoQnLCBjb3VudDogMCwgY2hpbGRyZW46IFtdLCBpY29uOiAnZWwtaWNvbi1vZmZpY2UtYnVpbGRpbmcnIH0sDQogICAgICAgIHsgbmFtZTogJ+S6p+WTgScsIGNvdW50OiAwLCBjaGlsZHJlbjogW10sIGljb246ICdlbC1pY29uLWdvb2RzJyB9LA0KICAgICAgICB7IG5hbWU6ICfkuovku7YnLCBjb3VudDogMCwgY2hpbGRyZW46IFtdLCBpY29uOiAnZWwtaWNvbi1iZWxsJyB9LA0KICAgICAgICB7IG5hbWU6ICfor53popgnLCBjb3VudDogMCwgY2hpbGRyZW46IFtdLCBpY29uOiAnZWwtaWNvbi1jaGF0LWRvdC1zcXVhcmUnIH0NCiAgICAgIF0sDQogICAgICB0YWJsZURhdGE6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq6ZuG5oiQ54G25paw5ZOB5LiK5biC5Y+R5biD5LyaJywNCiAgICAgICAgICBzb3VyY2U6ICfotJ/pnaInLA0KICAgICAgICAgIHBsYXRmb3JtOiAnQVBQJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yNCAxOTowMTowMCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq6ZuG5oiQ54G25paw5ZOB5LiK5biC5Y+R5biD5LyaJywNCiAgICAgICAgICBzb3VyY2U6ICfotJ/pnaInLA0KICAgICAgICAgIHBsYXRmb3JtOiAnQVBQJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yNCAxOTowNzo0NicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5Zyo6ZuG5oiQ54G26aKG5Z+f5Lit55qENSXvvIzmjIHnu63otoXov4fkuoY35Liq5ZOB54mM55u45Yqg77yM5pa55aSq6ZuG5oiQ54G2JywNCiAgICAgICAgICBzb3VyY2U6ICfotJ/pnaInLA0KICAgICAgICAgIHBsYXRmb3JtOiAn5aqS5L2TJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yNCAxODoyMjo0NScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5Lul56m65rCU6LSo6YeP5o+Q5Ye65Y+y5LiK5pyA5Lil6Iub5qCH5YeG77yM5pa55aSq5Y+R5biDIuiHtOWHgOWOqOaIvyLnkIblv7UnLA0KICAgICAgICAgIHNvdXJjZTogJ+i0n+mdoicsDQogICAgICAgICAgcGxhdGZvcm06ICflpLTmnaHlj7cnLA0KICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTI0IDE3OjQ5OjQ1Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfljqjnlLXooYzkuJoi5Y2B5bm05LiA6YGHIueahOWIm+aWsOS6p+WTge+8jOaWueWkquWPkeW4g+WFqOeQg+mmluasviLokrjng6TkuIDkvZPmnLoiJywNCiAgICAgICAgICBzb3VyY2U6ICfotJ/pnaInLA0KICAgICAgICAgIHBsYXRmb3JtOiAn5aqS5L2TJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yNCAxNzoxMjoxMCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq5oiQ56uLMjDlkajlubTkuYvpmYXvvIzmjqjlh7rnrKzkuozku6Mi5pm65YeA5rSX56KX5py6Iu+8jOWFqOeQg+mmluasoeS4ieS7o+WQjOWPsOS6ruebuCcsDQogICAgICAgICAgc291cmNlOiAn6LSf6Z2iJywNCiAgICAgICAgICBwbGF0Zm9ybTogJ+WqkuS9kycsDQogICAgICAgICAgdGltZTogJzIwMjMtMDQtMjQgMTU6MTU6MTYnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+WOqOaIv+eUteWZqOWNgeW5tOadpeWPmOmdqeS4juWIm+aWsO+8jOaWueaWsOS4gOS7o+mbhuaIkOeBtuWPkeW4g++8jOWNh+e6p+iSuOOAgeeDpOS4gOS9k+WKn+iDvScsDQogICAgICAgICAgc291cmNlOiAn6LSf6Z2iJywNCiAgICAgICAgICBwbGF0Zm9ybTogJ+WqkuS9kycsDQogICAgICAgICAgdGltZTogJzIwMjMtMDQtMjQgMTQ6Mjk6MDknDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+OAkOaWueWkquOAkeWFqOeQg+mmluasviLokrjng6TkuIDkvZPmnLoi5Y+R5biD77yM5pa55aSq5YaN5qyh5byV6aKG5Y6o55S16KGM5Lia5Yib5pawJywNCiAgICAgICAgICBzb3VyY2U6ICfotJ/pnaInLA0KICAgICAgICAgIHBsYXRmb3JtOiAn5aqS5L2TJywNCiAgICAgICAgICB0aW1lOiAnMjAyMy0wNC0yNCAxNDoxOToyMScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5pa55aSq5Y6o5oi/55S15Zmo5Y+R5biD5YWo55CD6aaW5qy+IuiSuOeDpOS4gOS9k+acuiLvvIzmlrnlpKrljqjnlLXlho3mrKHlvJXpoobljqjnlLXooYzkuJrliJvmlrAnLA0KICAgICAgICAgIHNvdXJjZTogJ+i0n+mdoicsDQogICAgICAgICAgcGxhdGZvcm06ICflqpLkvZMnLA0KICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTI0IDEyOjQ4OjA0Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfjgJDmlrnlpKrjgJHlrrbnlKjljqjnlLXluILlnLrlop7plb/mlL7nvJPvvIzmlrnlpKrlj5Hlipvpq5jnq6/luILlnLrvvIxBSS9JT1TmioDmnK/miJDmlrDlop7plb/ngrknLA0KICAgICAgICAgIHNvdXJjZTogJ+i0n+mdoicsDQogICAgICAgICAgcGxhdGZvcm06ICflqpLkvZMnLA0KICAgICAgICAgIHRpbWU6ICcyMDIzLTA0LTI0IDEyOjM0OjU0Jw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDpmpDol4/pobbpg6jlr7zoiKrmoI8NCiAgICB0aGlzLm9yaWdpbmFsVG9wTmF2ID0gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MudG9wTmF2DQogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3NldHRpbmdzL2NoYW5nZVNldHRpbmcnLCB7DQogICAgICBrZXk6ICd0b3BOYXYnLA0KICAgICAgdmFsdWU6IGZhbHNlDQogICAgfSkNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmgaLlpI3pobbpg6jlr7zoiKrmoI/orr7nva4NCiAgICBpZiAodGhpcy5vcmlnaW5hbFRvcE5hdiAhPT0gdW5kZWZpbmVkKSB7DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgICAga2V5OiAndG9wTmF2JywNCiAgICAgICAgdmFsdWU6IHRoaXMub3JpZ2luYWxUb3BOYXYNCiAgICAgIH0pDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5tdWx0aXBsZVNlbGVjdGlvbiA9IHZhbDsNCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICAvLyDmnoTlu7rmn6Xor6Llj4LmlbANCiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBwYWdlX251bTogdGhpcy5jdXJyZW50UGFnZSwNCiAgICAgICAgcGFnZV9zaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICBzY2hlbWVfaWQ6IG51bGwsDQogICAgICAgIHdhcm5pbmdfdHlwZTogbnVsbCwNCiAgICAgICAgY29udGVudDogdGhpcy5zZWFyY2hUZXh0IHx8IG51bGwsDQogICAgICAgIGtleXdvcmRzOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGJlZ2luX3RpbWU6IG51bGwsDQogICAgICAgIGVuZF90aW1lOiBudWxsDQogICAgICB9Ow0KDQogICAgICAvLyDosIPnlKhBUEnojrflj5bmlbDmja4NCiAgICAgIGxpc3RXYXJuaW5nUmVjb3JkKHF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzcG9uc2UuZGF0YS5yb3dzIHx8IFtdOw0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsIHx8IDA7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+iOt+WPluaVsOaNruWksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDkvqfovrnmoI/nm7jlhbPmlrnms5UNCiAgICB0b2dnbGVTaWRlYmFyKCkgew0KICAgICAgdGhpcy5zaWRlYmFyQ29sbGFwc2VkID0gIXRoaXMuc2lkZWJhckNvbGxhcHNlZDsNCiAgICB9LA0KICAgIGhhbmRsZU1lbnVTZWxlY3QoaW5kZXgpIHsNCiAgICAgIHRoaXMuYWN0aXZlTWVudUl0ZW0gPSBpbmRleDsNCiAgICAgIC8vIOi/memHjOWPr+S7pea3u+WKoOWIh+aNouiPnOWNlemhueWQjueahOmAu+i+ke+8jOWmgumHjeaWsOiOt+WPluaVsOaNruetiQ0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBjcmVhdGVOZXdTY2hlbWUoKSB7DQogICAgICAvLyDmlrDlu7rmlrnmoYjnmoTpgLvovpENCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAn5paw5bu65pa55qGI5Yqf6IO95b6F5a6e546wJywNCiAgICAgICAgdHlwZTogJ2luZm8nDQogICAgICB9KTsNCiAgICB9LA0KICAgIHNlYXJjaFNpZGViYXIoKSB7DQogICAgICAvLyDkvqfovrnmoI/mkJzntKLpgLvovpENCiAgICAgIGNvbnNvbGUubG9nKCfmkJzntKLlhbPplK7or43vvJonLCB0aGlzLnNpZGViYXJTZWFyY2hUZXh0KTsNCiAgICAgIC8vIOWunueOsOaQnOe0oumAu+i+kQ0KICAgIH0sDQogICAgLy8g5omT5byA6aKE6K2m6K6+572u5by556qXDQogICAgb3Blbldhcm5pbmdEaWFsb2coKSB7DQogICAgICB0aGlzLndhcm5pbmdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOWFs+mXremihOitpuiuvue9ruW8ueeqlw0KICAgIGNsb3NlV2FybmluZ0RpYWxvZygpIHsNCiAgICAgIHRoaXMud2FybmluZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOaJk+W8gOWFs+mUruivjeiuvue9ruaKveWxiQ0KICAgIG9wZW5LZXl3b3JkRGlhbG9nKCkgew0KICAgICAgdGhpcy5rZXl3b3JkRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDlhbPpl63lhbPplK7or43orr7nva7mir3lsYkNCiAgICBjbG9zZUtleXdvcmREaWFsb2coKSB7DQogICAgICB0aGlzLmtleXdvcmREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCiAgICAvLyDkv53lrZjpooToraborr7nva4NCiAgICBzYXZlV2FybmluZ1NldHRpbmdzKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5L+d5a2Y6aKE6K2m6K6+572u55qE6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5L+d5a2Y6aKE6K2m6K6+572uOicsIHRoaXMud2FybmluZ1NldHRpbmdzKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAn6aKE6K2m6K6+572u5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICB9KTsNCiAgICAgIHRoaXMuY2xvc2VXYXJuaW5nRGlhbG9nKCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWFqOmDqOWkjemAieahhg0KICAgIGhhbmRsZUFsbENoZWNrYm94KHNlY3Rpb24pIHsNCiAgICAgIGNvbnN0IGFsbE9wdGlvbiA9IHNlY3Rpb24ub3B0aW9ucy5maW5kKG9wdCA9PiBvcHQudmFsdWUgPT09ICdhbGwnKTsNCiAgICAgIGlmIChhbGxPcHRpb24gJiYgYWxsT3B0aW9uLmNoZWNrZWQpIHsNCiAgICAgICAgLy8g5aaC5p6c5YWo6YOo6KKr6YCJ5Lit77yM5YiZ6YCJ5Lit5omA5pyJ6YCJ6aG5DQogICAgICAgIHNlY3Rpb24ub3B0aW9ucy5mb3JFYWNoKG9wdCA9PiB7DQogICAgICAgICAgb3B0LmNoZWNrZWQgPSB0cnVlOw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CG5Yy56YWN5a+56LGh5YWo6YOo5aSN6YCJ5qGGDQogICAgaGFuZGxlTWF0Y2hPYmplY3RBbGwoY2hlY2tlZCkgew0KICAgICAgdGhpcy53YXJuaW5nU2V0dGluZ3MubWF0Y2hPYmplY3QuYWxsQ2hlY2tlZCA9IGNoZWNrZWQ7DQogICAgICBpZiAoY2hlY2tlZCkgew0KICAgICAgICAvLyDlpoLmnpzlhajpg6jooqvpgInkuK3vvIzliJnlj5bmtojpgInkuK3lhbbku5bpgInpobkNCiAgICAgICAgdGhpcy53YXJuaW5nU2V0dGluZ3MubWF0Y2hPYmplY3Qub3B0aW9ucy5mb3JFYWNoKG9wdCA9PiB7DQogICAgICAgICAgb3B0LmNoZWNrZWQgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoOWPkeW4g+WcsOWMug0KICAgIGFkZFB1Ymxpc2hSZWdpb24ocmVnaW9uKSB7DQogICAgICBpZiAocmVnaW9uICYmICF0aGlzLndhcm5pbmdTZXR0aW5ncy5wdWJsaXNoUmVnaW9uLnJlZ2lvbnMuc29tZShyID0+IHIubmFtZSA9PT0gcmVnaW9uKSkgew0KICAgICAgICB0aGlzLndhcm5pbmdTZXR0aW5ncy5wdWJsaXNoUmVnaW9uLnJlZ2lvbnMucHVzaCh7IG5hbWU6IHJlZ2lvbiwgdmFsdWU6IHJlZ2lvbiB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yig6Zmk5Y+R5biD5Zyw5Yy6DQogICAgcmVtb3ZlUHVibGlzaFJlZ2lvbihyZWdpb24pIHsNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy53YXJuaW5nU2V0dGluZ3MucHVibGlzaFJlZ2lvbi5yZWdpb25zLmZpbmRJbmRleChyID0+IHIubmFtZSA9PT0gcmVnaW9uKTsNCiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy53YXJuaW5nU2V0dGluZ3MucHVibGlzaFJlZ2lvbi5yZWdpb25zLnNwbGljZShpbmRleCwgMSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOa3u+WKoElQ5bGe5ZywDQogICAgYWRkSXBBcmVhKGFyZWEpIHsNCiAgICAgIGlmIChhcmVhICYmICF0aGlzLndhcm5pbmdTZXR0aW5ncy5pcEFyZWEuYXJlYXMuc29tZShhID0+IGEubmFtZSA9PT0gYXJlYSkpIHsNCiAgICAgICAgdGhpcy53YXJuaW5nU2V0dGluZ3MuaXBBcmVhLmFyZWFzLnB1c2goeyBuYW1lOiBhcmVhLCB2YWx1ZTogYXJlYSB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yig6ZmkSVDlsZ7lnLANCiAgICByZW1vdmVJcEFyZWEoYXJlYSkgew0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLndhcm5pbmdTZXR0aW5ncy5pcEFyZWEuYXJlYXMuZmluZEluZGV4KGEgPT4gYS5uYW1lID09PSBhcmVhKTsNCiAgICAgIGlmIChpbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy53YXJuaW5nU2V0dGluZ3MuaXBBcmVhLmFyZWFzLnNwbGljZShpbmRleCwgMSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaJk+W8gOWqkuS9k+exu+WIq+Wvueivneahhg0KICAgIG9wZW5NZWRpYUNhdGVnb3J5RGlhbG9nKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICflqpLkvZPnsbvliKvlip/og73lvoXlrp7njrAnLA0KICAgICAgICB0eXBlOiAnaW5mbycNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmiZPlvIDmlofnq6DnsbvliKvlr7nor53moYYNCiAgICBvcGVuQXJ0aWNsZUNhdGVnb3J5RGlhbG9nKCkgew0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICfmlofnq6DnsbvliKvlip/og73lvoXlrp7njrAnLA0KICAgICAgICB0eXBlOiAnaW5mbycNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjlhbPplK7or43orr7nva4NCiAgICBzYXZlS2V5d29yZFNldHRpbmdzKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5L+d5a2Y5YWz6ZSu6K+N6K6+572u55qE6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5L+d5a2Y5YWz6ZSu6K+N6K6+572uOicsIHRoaXMua2V5d29yZFNldHRpbmdzKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAn5YWz6ZSu6K+N6K6+572u5L+d5a2Y5oiQ5YqfJywNCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICB9KTsNCiAgICAgIHRoaXMuY2xvc2VLZXl3b3JkRGlhbG9nKCk7DQogICAgfSwNCg0KICAgIC8vIOaJk+W8gOiHquWKqOmihOitpuiuvue9ruaKveWxiQ0KICAgIG9wZW5BdXRvV2FybmluZ0RpYWxvZygpIHsNCiAgICAgIHRoaXMuYXV0b1dhcm5pbmdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgLy8g5YWz6Zet6Ieq5Yqo6aKE6K2m6K6+572u5oq95bGJDQogICAgY2xvc2VBdXRvV2FybmluZ0RpYWxvZygpIHsNCiAgICAgIHRoaXMuYXV0b1dhcm5pbmdEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOS/neWtmOiHquWKqOmihOitpuiuvue9rg0KICAgIHNhdmVBdXRvV2FybmluZ1NldHRpbmdzKCkgew0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5L+d5a2Y6Ieq5Yqo6aKE6K2m6K6+572u55qE6YC76L6RDQogICAgICBjb25zb2xlLmxvZygn5L+d5a2Y6Ieq5Yqo6aKE6K2m6K6+572uOicsIHRoaXMuYXV0b1dhcm5pbmdTZXR0aW5ncyk7DQogICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgbWVzc2FnZTogJ+iHquWKqOmihOitpuiuvue9ruS/neWtmOaIkOWKnycsDQogICAgICAgIHR5cGU6ICdzdWNjZXNzJw0KICAgICAgfSk7DQogICAgICB0aGlzLmNsb3NlQXV0b1dhcm5pbmdEaWFsb2coKTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+f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file": "index.vue", "sourceRoot": "src/views/warning-center", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"page-container\">\r\n      <!-- 左侧导航栏 -->\r\n      <div class=\"left-sidebar\" :class=\"{ 'collapsed': sidebarCollapsed }\">\r\n        <div class=\"sidebar-header\">\r\n          <el-button type=\"warning\" class=\"new-scheme-btn\" @click=\"createNewScheme\">\r\n            <i class=\"el-icon-plus\"></i> 新建方案\r\n          </el-button>\r\n          <div class=\"sidebar-btn\" @click=\"toggleSidebar\">\r\n            <i class=\"el-icon-s-fold\"></i>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"sidebar-search\">\r\n          <el-input\r\n            v-model=\"sidebarSearchText\"\r\n            placeholder=\"搜索\"\r\n            prefix-icon=\"el-icon-search\"\r\n            size=\"small\"\r\n            @input=\"searchSidebar\"\r\n          ></el-input>\r\n        </div>\r\n\r\n        <div class=\"sidebar-menu\">\r\n          <el-menu\r\n            :default-active=\"activeMenuItem\"\r\n            class=\"sidebar-menu-list\"\r\n            @select=\"handleMenuSelect\"\r\n          >\r\n            <template v-for=\"(item, index) in menuCategories\">\r\n              <!-- 使用唯一的key -->\r\n              <el-menu-item\r\n                v-if=\"item.isItem\"\r\n                :key=\"'item-' + item.name\"\r\n                :index=\"item.name\"\r\n                :class=\"{ 'active-menu-item': activeMenuItem === item.name }\"\r\n              >\r\n                <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                <span>{{ item.name }}</span>\r\n              </el-menu-item>\r\n\r\n              <!-- 如果是子菜单 -->\r\n              <el-submenu\r\n                v-else\r\n                :key=\"'submenu-' + item.name\"\r\n                :index=\"item.name\"\r\n              >\r\n                <template slot=\"title\">\r\n                  <i :class=\"item.icon\" v-if=\"item.icon\"></i>\r\n                  <span>{{ item.name }}({{ item.count }})</span>\r\n                </template>\r\n                <!-- 子菜单项 -->\r\n                <el-menu-item\r\n                  v-for=\"child in item.children\"\r\n                  :key=\"child.name\"\r\n                  :index=\"child.name\"\r\n                >\r\n                  {{ child.name }}\r\n                </el-menu-item>\r\n              </el-submenu>\r\n            </template>\r\n          </el-menu>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧内容区 -->\r\n      <div class=\"right-content\">\r\n        <!-- 主体内容 -->\r\n        <div class=\"main-content\">\r\n          <!-- 标题和操作区 -->\r\n          <div class=\"title-area\">\r\n            <div class=\"title\">\r\n              <div style=\"width: 100%; text-align: left;\">\r\n                <h2><i class=\"el-icon-warning-outline\" style=\"color: #E6A23C; margin-right: 8px;\"></i>方太<i class=\"el-icon-edit-outline\"></i></h2>\r\n                <div class=\"tabs\" style=\"text-align: left; margin-top: 10px; margin-left: 0;\">\r\n                  <el-button type=\"text\" icon=\"el-icon-user\">接收人设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-bell\" @click=\"openWarningDialog\">预警设置</el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-data-analysis\" @click=\"openKeywordDialog\">关键词设置</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"actions\">\r\n              <el-switch v-model=\"autoRefresh\" active-text=\"预警开关\"></el-switch>\r\n              <div>\r\n                <el-button type=\"primary\" size=\"small\">人工预警</el-button>\r\n                <el-button type=\"primary\" size=\"small\" @click=\"openAutoWarningDialog\">自动预警</el-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 表格区域 -->\r\n          <div class=\"table-area\">\r\n            <div class=\"table-toolbar\">\r\n              <div class=\"left-tools\">\r\n                <el-checkbox></el-checkbox>\r\n                <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n              </div>\r\n              <div class=\"right-tools\">\r\n                <span>共计{{total}}条</span>\r\n                <el-button type=\"text\" icon=\"el-icon-download\">导出下载</el-button>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    字段<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <div class=\"date-range\">\r\n                  <span>2023/04/23 08:00:00 - 2023/04/25 01:00:00</span>\r\n                </div>\r\n                <el-dropdown>\r\n                  <span class=\"el-dropdown-link\">\r\n                    全部<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                  </span>\r\n                </el-dropdown>\r\n                <el-input\r\n                  placeholder=\"搜索\"\r\n                  prefix-icon=\"el-icon-search\"\r\n                  v-model=\"searchText\"\r\n                  style=\"width: 200px;\"\r\n                  clearable\r\n                ></el-input>\r\n              </div>\r\n            </div>\r\n\r\n            <el-table\r\n              :data=\"tableData\"\r\n              style=\"width: 100%\"\r\n              @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"\"\r\n                width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" icon=\"el-icon-star-off\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-message\"></el-button>\r\n                  <el-button type=\"text\" icon=\"el-icon-download\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"title\"\r\n                label=\"标题/摘要\"\r\n                show-overflow-tooltip>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"source\"\r\n                label=\"来源类型\"\r\n                width=\"100\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag size=\"mini\" type=\"danger\">{{ scope.row.source }}</el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"platform\"\r\n                label=\"平台类型\"\r\n                width=\"100\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"time\"\r\n                label=\"发布时间\"\r\n                width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"操作\"\r\n                width=\"150\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-view\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-edit\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-delete\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-share\"></el-button>\r\n                  <el-button type=\"text\" size=\"mini\" icon=\"el-icon-more\"></el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"currentPage\"\r\n              :limit.sync=\"pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"warningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeWarningDialog\"\r\n      custom-class=\"warning-drawer\">\r\n      <div class=\"warning-drawer-content\">\r\n        <!-- 平台类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.platformType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.platformType.options\"\r\n              :key=\"'platform-' + index\"\r\n              v-model=\"option.checked\"\r\n              @change=\"option.value === 'all' && handleAllCheckbox(warningSettings.platformType)\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 内容属性 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.contentProperty.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.contentProperty.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.contentProperty.options\"\r\n                :key=\"'content-property-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 信息类型 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.infoType.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.infoType.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.infoType.options\"\r\n                :key=\"'info-type-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配对象 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchObject.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-checkbox\r\n              v-model=\"warningSettings.matchObject.allChecked\"\r\n              @change=\"handleMatchObjectAll\">\r\n              全部\r\n            </el-checkbox>\r\n            <el-checkbox\r\n              v-for=\"(option, index) in warningSettings.matchObject.options\"\r\n              :key=\"'match-object-' + index\"\r\n              v-model=\"option.checked\"\r\n              :disabled=\"warningSettings.matchObject.allChecked\">\r\n              {{ option.label }}\r\n            </el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 匹配方式 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.matchMethod.title }}</h3>\r\n          <div class=\"warning-options\">\r\n            <el-radio-group v-model=\"warningSettings.matchMethod.value\">\r\n              <el-radio\r\n                v-for=\"(option, index) in warningSettings.matchMethod.options\"\r\n                :key=\"'match-method-' + index\"\r\n                :label=\"option.value\">\r\n                {{ option.label }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 发布地区 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.publishRegion.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加发布地区\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"publishRegionInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.publishRegion.regions.length > 0\">\r\n              <el-tag\r\n                v-for=\"(region, index) in warningSettings.publishRegion.regions\"\r\n                :key=\"'region-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removePublishRegion(region.name)\">\r\n                {{ region.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- IP属地 -->\r\n        <div class=\"warning-section\">\r\n          <h3>{{ warningSettings.ipArea.title }}</h3>\r\n          <div class=\"region-section\">\r\n            <div class=\"region-input\">\r\n              <el-input\r\n                placeholder=\"添加IP属地\"\r\n                size=\"small\"\r\n                style=\"width: 200px;\"\r\n                v-model=\"ipAreaInput\">\r\n                <i slot=\"suffix\" class=\"el-icon-location\"></i>\r\n              </el-input>\r\n            </div>\r\n            <div class=\"region-tags\" v-if=\"warningSettings.ipArea.areas.length > 0\">\r\n              <el-tag\r\n                v-for=\"(area, index) in warningSettings.ipArea.areas\"\r\n                :key=\"'ip-area-' + index\"\r\n                size=\"small\"\r\n                closable\r\n                @close=\"removeIpArea(area.name)\">\r\n                {{ area.name }}\r\n              </el-tag>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 媒体类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openMediaCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.mediaCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.mediaCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 文章类别 -->\r\n        <div class=\"warning-section category-section\" @click=\"openArticleCategoryDialog\">\r\n          <div class=\"category-header\">\r\n            <h3>{{ warningSettings.articleCategory.title }}</h3>\r\n            <div class=\"category-count\">\r\n              <span>(已选{{ warningSettings.articleCategory.count }}个)</span>\r\n              <i class=\"el-icon-arrow-right\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 关键词设置抽屉 -->\r\n    <el-drawer\r\n      title=\"文本词设置\"\r\n      :visible.sync=\"keywordDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeKeywordDialog\"\r\n      custom-class=\"keyword-drawer\">\r\n      <div class=\"keyword-drawer-content\">\r\n        <!-- 允许词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>允许词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"允许词：对文本进行筛选，命中文本的内容会被允许通过\"\r\n            v-model=\"keywordSettings.allowWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 拒绝词 -->\r\n        <div class=\"keyword-section\">\r\n          <h3>拒绝词</h3>\r\n          <el-input\r\n            type=\"textarea\"\r\n            :rows=\"8\"\r\n            placeholder=\"拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过\"\r\n            v-model=\"keywordSettings.rejectWords\">\r\n          </el-input>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeKeywordDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveKeywordSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <!-- 自动预警设置抽屉 -->\r\n    <el-drawer\r\n      title=\"预警设置\"\r\n      :visible.sync=\"autoWarningDialogVisible\"\r\n      direction=\"rtl\"\r\n      size=\"30%\"\r\n      :before-close=\"closeAutoWarningDialog\"\r\n      custom-class=\"auto-warning-drawer\">\r\n      <div class=\"auto-warning-drawer-content\">\r\n        <h3 class=\"auto-warning-title\">自动预警设置</h3>\r\n\r\n        <!-- 预警时间 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警时间</div>\r\n          <div class=\"time-range-selector\">\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startHour\" placeholder=\"小时\" size=\"small\">\r\n              <el-option\r\n                v-for=\"h in 24\"\r\n                :key=\"'start-hour-' + h\"\r\n                :label=\"(h - 1).toString().padStart(2, '0')\"\r\n                :value=\"(h - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n            <span class=\"time-separator\">:</span>\r\n            <el-select v-model=\"autoWarningSettings.timeRange.startMinute\" placeholder=\"分钟\" size=\"small\">\r\n              <el-option\r\n                v-for=\"m in 60\"\r\n                :key=\"'start-minute-' + m\"\r\n                :label=\"(m - 1).toString().padStart(2, '0')\"\r\n                :value=\"(m - 1).toString().padStart(2, '0')\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"time-range-note\">\r\n            <span class=\"note-text\">预警时间段开始时间到结束时间</span>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警平台 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警平台</div>\r\n          <div class=\"platform-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.weibo\">微博</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.wechat\">微信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.website\">网站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.douyin\">抖音</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.redbook\">小红书</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.bilibili\">B站</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.platforms.zhihu\">知乎</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 预警类型 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">预警类型</div>\r\n          <div class=\"warning-type-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.warningType\" label=\"negative\">负面</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"process-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"all\">全部预警</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.processMethod\" label=\"onlyAlert\">仅告警 <span class=\"note-text\">(只对符合条件的)</span></el-radio>\r\n          </div>\r\n          <div class=\"process-switch\">\r\n            <span class=\"switch-label\">只对重要账号 <span class=\"note-text\">(对方粉丝大于10万或认证账号)</span></span>\r\n            <el-switch v-model=\"importantAccountOnly\"></el-switch>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 优先级别 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">优先级别</div>\r\n          <div class=\"priority-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"normal\">正常</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.priority\" label=\"urgent\">紧急</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 处理方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">处理方式</div>\r\n          <div class=\"handle-method-selector\">\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"auto\">自动</el-radio>\r\n            <el-radio v-model=\"autoWarningSettings.handleMethod\" label=\"manual\">人工</el-radio>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 告知方式 -->\r\n        <div class=\"auto-warning-section\">\r\n          <div class=\"section-label\">告知方式</div>\r\n          <div class=\"notify-method-checkboxes\">\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.sms\">短信</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.email\">邮件</el-checkbox>\r\n            <el-checkbox v-model=\"autoWarningSettings.notifyMethods.wechatNotify\">微信通知</el-checkbox>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 底部按钮 -->\r\n        <div class=\"drawer-footer\">\r\n          <el-button @click=\"closeAutoWarningDialog\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"saveAutoWarningSettings\">确定</el-button>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// 导入分页组件（如果需要）\r\n// import Pagination from \"@/components/Pagination\";\r\nimport {\r\n  listWarningRecord,\r\n  saveWarningSettings,\r\n  saveKeywordSettings,\r\n  saveAutoWarningSettings,\r\n  addWarningScheme,\r\n  listWarningScheme,\r\n  getWarningStatistics\r\n} from '@/api/warning/index'\r\n\r\nexport default {\r\n  name: 'InfoSummary',\r\n  // 注册组件（如果需要）\r\n  // components: {\r\n  //   Pagination\r\n  // },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      autoRefresh: true,\r\n      searchText: '',\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      total: 156,\r\n      // 预警设置弹窗相关数据\r\n      publishRegionInput: '',\r\n      ipAreaInput: '',\r\n      warningDialogVisible: false,\r\n      // 关键词设置抽屉相关数据\r\n      keywordDialogVisible: false,\r\n      keywordSettings: {\r\n        allowWords: '允许词：对文本进行筛选，命中文本的内容会被允许通过',\r\n        rejectWords: '拒绝词：对文本进行筛选，命中文本的内容会被拒绝通过'\r\n      },\r\n      // 自动预警设置抽屉相关数据\r\n      autoWarningDialogVisible: false,\r\n      importantAccountOnly: true,\r\n      autoWarningSettings: {\r\n        timeRange: {\r\n          startHour: '06',\r\n          startMinute: '00',\r\n          endHour: '18',\r\n          endMinute: '00'\r\n        },\r\n        platforms: {\r\n          weibo: true,\r\n          wechat: true,\r\n          website: true,\r\n          douyin: true,\r\n          redbook: true,\r\n          bilibili: false,\r\n          zhihu: false\r\n        },\r\n        warningType: 'negative', // negative, positive, all\r\n        processMethod: 'all', // all, onlyAlert\r\n        priority: 'normal', // normal, urgent\r\n        handleMethod: 'auto', // auto, manual\r\n        notifyMethods: {\r\n          sms: true,\r\n          email: false,\r\n          wechatNotify: true\r\n        }\r\n      },\r\n      warningSettings: {\r\n        platformType: {\r\n          title: '平台类型',\r\n          options: [\r\n            { label: '全部', value: 'all', checked: true },\r\n            { label: '网页', value: 'webpage', checked: true },\r\n            { label: '微信', value: 'wechat', checked: true },\r\n            { label: '微博', value: 'weibo', checked: true },\r\n            { label: '头条号', value: 'toutiao', checked: true },\r\n            { label: 'APP', value: 'app', checked: true },\r\n            { label: '视频', value: 'video', checked: true },\r\n            { label: '论坛', value: 'forum', checked: true },\r\n            { label: '报刊', value: 'newspaper', checked: true },\r\n            { label: '问答', value: 'qa', checked: true }\r\n          ]\r\n        },\r\n        contentProperty: {\r\n          title: '内容属性',\r\n          value: 'all', // all, yes, no\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '是', value: 'yes' },\r\n            { label: '不是', value: 'no' }\r\n          ]\r\n        },\r\n        infoType: {\r\n          title: '信息类型',\r\n          value: 'noncomment', // all, noncomment, comment\r\n          options: [\r\n            { label: '全部', value: 'all' },\r\n            { label: '非评论', value: 'noncomment' },\r\n            { label: '评论', value: 'comment' }\r\n          ]\r\n        },\r\n        matchObject: {\r\n          title: '匹配对象',\r\n          allChecked: true,\r\n          options: [\r\n            { label: '标题匹配', value: 'title', checked: false },\r\n            { label: '正文匹配', value: 'content', checked: false },\r\n            { label: '音频/图片匹配', value: 'media', checked: false },\r\n            { label: '原文匹配', value: 'original', checked: false }\r\n          ]\r\n        },\r\n        matchMethod: {\r\n          title: '匹配方式',\r\n          value: 'exact', // exact, fuzzy\r\n          options: [\r\n            { label: '精准', value: 'exact' },\r\n            { label: '模糊', value: 'fuzzy' }\r\n          ]\r\n        },\r\n        publishRegion: {\r\n          title: '发布地区',\r\n          regions: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        ipArea: {\r\n          title: 'IP属地',\r\n          areas: [\r\n            { name: '全部', value: 'all' }\r\n          ]\r\n        },\r\n        mediaCategory: {\r\n          title: '媒体类别',\r\n          count: 0\r\n        },\r\n        articleCategory: {\r\n          title: '文章类别',\r\n          count: 0\r\n        }\r\n      },\r\n      // 侧边栏数据\r\n      sidebarCollapsed: false,\r\n      sidebarSearchText: '',\r\n      activeMenuItem: '方太',\r\n      menuCategories: [\r\n        { name: '总监', count: 1, children: [], icon: 'el-icon-view' },\r\n        { name: '品牌', count: 1, children: [], icon: 'el-icon-star-on' },\r\n        { name: '方太', count: 0, isItem: true, icon: 'el-icon-office-building' },\r\n        { name: '人物', count: 0, children: [], icon: 'el-icon-user' },\r\n        { name: '机构', count: 0, children: [], icon: 'el-icon-office-building' },\r\n        { name: '产品', count: 0, children: [], icon: 'el-icon-goods' },\r\n        { name: '事件', count: 0, children: [], icon: 'el-icon-bell' },\r\n        { name: '话题', count: 0, children: [], icon: 'el-icon-chat-dot-square' }\r\n      ],\r\n      tableData: [\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:01:00'\r\n        },\r\n        {\r\n          title: '方太集成灶新品上市发布会',\r\n          source: '负面',\r\n          platform: 'APP',\r\n          time: '2023-04-24 19:07:46'\r\n        },\r\n        {\r\n          title: '在集成灶领域中的5%，持续超过了7个品牌相加，方太集成灶',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 18:22:45'\r\n        },\r\n        {\r\n          title: '以空气质量提出史上最严苛标准，方太发布\"致净厨房\"理念',\r\n          source: '负面',\r\n          platform: '头条号',\r\n          time: '2023-04-24 17:49:45'\r\n        },\r\n        {\r\n          title: '厨电行业\"十年一遇\"的创新产品，方太发布全球首款\"蒸烤一体机\"',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 17:12:10'\r\n        },\r\n        {\r\n          title: '方太成立20周年之际，推出第二代\"智净洗碗机\"，全球首次三代同台亮相',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 15:15:16'\r\n        },\r\n        {\r\n          title: '厨房电器十年来变革与创新，方新一代集成灶发布，升级蒸、烤一体功能',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:29:09'\r\n        },\r\n        {\r\n          title: '【方太】全球首款\"蒸烤一体机\"发布，方太再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 14:19:21'\r\n        },\r\n        {\r\n          title: '方太厨房电器发布全球首款\"蒸烤一体机\"，方太厨电再次引领厨电行业创新',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:48:04'\r\n        },\r\n        {\r\n          title: '【方太】家用厨电市场增长放缓，方太发力高端市场，AI/IOT技术成新增长点',\r\n          source: '负面',\r\n          platform: '媒体',\r\n          time: '2023-04-24 12:34:54'\r\n        }\r\n      ],\r\n      multipleSelection: []\r\n    };\r\n  },\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    handleSelectionChange(val) {\r\n      this.multipleSelection = val;\r\n    },\r\n    getList() {\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        page_num: this.currentPage,\r\n        page_size: this.pageSize,\r\n        scheme_id: null,\r\n        warning_type: null,\r\n        content: this.searchText || null,\r\n        keywords: null,\r\n        status: null,\r\n        begin_time: null,\r\n        end_time: null\r\n      };\r\n\r\n      // 调用API获取数据\r\n      listWarningRecord(queryParams).then(response => {\r\n        if (response.code === 200) {\r\n          this.tableData = response.data.rows || [];\r\n          this.total = response.data.total || 0;\r\n        } else {\r\n          this.$message.error(response.msg || '获取数据失败');\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取数据失败:', error);\r\n        this.$message.error('获取数据失败');\r\n      });\r\n    },\r\n    // 侧边栏相关方法\r\n    toggleSidebar() {\r\n      this.sidebarCollapsed = !this.sidebarCollapsed;\r\n    },\r\n    handleMenuSelect(index) {\r\n      this.activeMenuItem = index;\r\n      // 这里可以添加切换菜单项后的逻辑，如重新获取数据等\r\n      this.getList();\r\n    },\r\n    createNewScheme() {\r\n      // 新建方案的逻辑\r\n      this.$message({\r\n        message: '新建方案功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n    searchSidebar() {\r\n      // 侧边栏搜索逻辑\r\n      console.log('搜索关键词：', this.sidebarSearchText);\r\n      // 实现搜索逻辑\r\n    },\r\n    // 打开预警设置弹窗\r\n    openWarningDialog() {\r\n      this.warningDialogVisible = true;\r\n    },\r\n    // 关闭预警设置弹窗\r\n    closeWarningDialog() {\r\n      this.warningDialogVisible = false;\r\n    },\r\n    // 打开关键词设置抽屉\r\n    openKeywordDialog() {\r\n      this.keywordDialogVisible = true;\r\n    },\r\n    // 关闭关键词设置抽屉\r\n    closeKeywordDialog() {\r\n      this.keywordDialogVisible = false;\r\n    },\r\n    // 保存预警设置\r\n    saveWarningSettings() {\r\n      // 这里可以添加保存预警设置的逻辑\r\n      console.log('保存预警设置:', this.warningSettings);\r\n      this.$message({\r\n        message: '预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeWarningDialog();\r\n    },\r\n\r\n    // 处理全部复选框\r\n    handleAllCheckbox(section) {\r\n      const allOption = section.options.find(opt => opt.value === 'all');\r\n      if (allOption && allOption.checked) {\r\n        // 如果全部被选中，则选中所有选项\r\n        section.options.forEach(opt => {\r\n          opt.checked = true;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 处理匹配对象全部复选框\r\n    handleMatchObjectAll(checked) {\r\n      this.warningSettings.matchObject.allChecked = checked;\r\n      if (checked) {\r\n        // 如果全部被选中，则取消选中其他选项\r\n        this.warningSettings.matchObject.options.forEach(opt => {\r\n          opt.checked = false;\r\n        });\r\n      }\r\n    },\r\n\r\n    // 添加发布地区\r\n    addPublishRegion(region) {\r\n      if (region && !this.warningSettings.publishRegion.regions.some(r => r.name === region)) {\r\n        this.warningSettings.publishRegion.regions.push({ name: region, value: region });\r\n      }\r\n    },\r\n\r\n    // 删除发布地区\r\n    removePublishRegion(region) {\r\n      const index = this.warningSettings.publishRegion.regions.findIndex(r => r.name === region);\r\n      if (index !== -1) {\r\n        this.warningSettings.publishRegion.regions.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 添加IP属地\r\n    addIpArea(area) {\r\n      if (area && !this.warningSettings.ipArea.areas.some(a => a.name === area)) {\r\n        this.warningSettings.ipArea.areas.push({ name: area, value: area });\r\n      }\r\n    },\r\n\r\n    // 删除IP属地\r\n    removeIpArea(area) {\r\n      const index = this.warningSettings.ipArea.areas.findIndex(a => a.name === area);\r\n      if (index !== -1) {\r\n        this.warningSettings.ipArea.areas.splice(index, 1);\r\n      }\r\n    },\r\n\r\n    // 打开媒体类别对话框\r\n    openMediaCategoryDialog() {\r\n      this.$message({\r\n        message: '媒体类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 打开文章类别对话框\r\n    openArticleCategoryDialog() {\r\n      this.$message({\r\n        message: '文章类别功能待实现',\r\n        type: 'info'\r\n      });\r\n    },\r\n\r\n    // 保存关键词设置\r\n    saveKeywordSettings() {\r\n      // 这里可以添加保存关键词设置的逻辑\r\n      console.log('保存关键词设置:', this.keywordSettings);\r\n      this.$message({\r\n        message: '关键词设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeKeywordDialog();\r\n    },\r\n\r\n    // 打开自动预警设置抽屉\r\n    openAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = true;\r\n    },\r\n\r\n    // 关闭自动预警设置抽屉\r\n    closeAutoWarningDialog() {\r\n      this.autoWarningDialogVisible = false;\r\n    },\r\n\r\n    // 保存自动预警设置\r\n    saveAutoWarningSettings() {\r\n      // 这里可以添加保存自动预警设置的逻辑\r\n      console.log('保存自动预警设置:', this.autoWarningSettings);\r\n      this.$message({\r\n        message: '自动预警设置保存成功',\r\n        type: 'success'\r\n      });\r\n      this.closeAutoWarningDialog();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.page-container {\r\n  display: flex;\r\n  height: 100%;\r\n}\r\n\r\n/* 左侧导航栏样式 */\r\n.left-sidebar {\r\n  width: 200px;\r\n  background-color: #fff;\r\n  border-right: 1px solid #e6e6e6;\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex-shrink: 0;\r\n  transition: width 0.3s;\r\n}\r\n\r\n/* 折叠状态的侧边栏 */\r\n.left-sidebar.collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.left-sidebar.collapsed .sidebar-search,\r\n.left-sidebar.collapsed .el-menu-item span,\r\n.left-sidebar.collapsed .el-submenu__title span {\r\n  display: none;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn {\r\n  padding: 8px 0;\r\n  font-size: 0;\r\n}\r\n\r\n.left-sidebar.collapsed .new-scheme-btn i {\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.sidebar-header {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.new-scheme-btn {\r\n  flex: 1;\r\n  font-size: 12px;\r\n  padding: 8px 10px;\r\n}\r\n\r\n.sidebar-btn {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-left: 5px;\r\n  cursor: pointer;\r\n  color: #909399;\r\n}\r\n\r\n.sidebar-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.sidebar-menu {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.sidebar-menu-list {\r\n  border-right: none;\r\n}\r\n\r\n.active-menu-item {\r\n  background-color: #ecf5ff !important;\r\n  color: #409EFF !important;\r\n}\r\n\r\n/* 菜单图标样式 */\r\n::v-deep .el-menu-item i,\r\n::v-deep .el-submenu__title i {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  width: 16px;\r\n  text-align: center;\r\n}\r\n\r\n::v-deep .el-menu-item i {\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-submenu__title i {\r\n  color: #909399;\r\n}\r\n\r\n::v-deep .el-menu-item.is-active i,\r\n::v-deep .active-menu-item i {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 右侧内容区样式 */\r\n.right-content {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n.top-nav {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  height: 50px;\r\n  border-bottom: 1px solid #eee;\r\n  background-color: #fff;\r\n}\r\n\r\n.nav-items {\r\n  display: flex;\r\n}\r\n\r\n.nav-item {\r\n  padding: 0 15px;\r\n  line-height: 50px;\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.nav-item.active {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n\r\n.nav-item.active::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  background-color: #409EFF;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info span {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.main-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  overflow-y: auto;\r\n}\r\n\r\n.title-area {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.title {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  margin-right: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  text-align: left;\r\n}\r\n\r\n.title h2 i {\r\n  margin-left: 5px;\r\n  font-size: 16px;\r\n  color: #909399;\r\n  cursor: pointer;\r\n}\r\n\r\n.tabs {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.tabs .el-button {\r\n  margin-right: 15px;\r\n  padding-left: 0;\r\n}\r\n\r\n.actions {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.actions .el-button {\r\n  margin-left: 15px;\r\n}\r\n\r\n.table-area {\r\n  background-color: #fff;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.left-tools, .right-tools {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.left-tools > * {\r\n  margin-right: 10px;\r\n}\r\n\r\n.right-tools > * {\r\n  margin-left: 15px;\r\n}\r\n\r\n.date-range {\r\n  font-size: 12px;\r\n  color: #606266;\r\n}\r\n\r\n.el-dropdown-link {\r\n  cursor: pointer;\r\n  color: #606266;\r\n}\r\n\r\n.el-table {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 覆盖Element UI的一些默认样式 */\r\n::v-deep .el-menu-item, ::v-deep .el-submenu__title {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-size: 14px;\r\n}\r\n\r\n::v-deep .el-submenu .el-menu-item {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  padding: 0 20px 0 40px;\r\n}\r\n\r\n/* 预警设置抽屉样式 */\r\n.warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.warning-section {\r\n  margin-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.warning-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.warning-options {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-options .el-checkbox {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.warning-options .el-radio {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n  font-size: 13px;\r\n}\r\n\r\n.region-section {\r\n  padding: 5px 0;\r\n}\r\n\r\n.region-input {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.region-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.region-tags .el-tag {\r\n  margin-right: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.category-section {\r\n  cursor: pointer;\r\n}\r\n\r\n.category-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.category-count {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #999;\r\n  font-size: 13px;\r\n}\r\n\r\n.category-count i {\r\n  margin-left: 5px;\r\n}\r\n\r\n.drawer-footer {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 15px 20px;\r\n  background-color: #fff;\r\n  border-top: 1px solid #eee;\r\n  text-align: right;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n/* 关键词设置抽屉样式 */\r\n.keyword-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.keyword-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.keyword-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.keyword-section h3 {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 自动预警设置抽屉样式 */\r\n.auto-warning-drawer {\r\n  .el-drawer__header {\r\n    margin-bottom: 0;\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid #eee;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n  }\r\n\r\n  .el-drawer__body {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.auto-warning-drawer-content {\r\n  height: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  position: relative;\r\n  padding-bottom: 70px; /* 为底部按钮留出空间 */\r\n}\r\n\r\n.auto-warning-title {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.auto-warning-section {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.section-label {\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  color: #666;\r\n}\r\n\r\n.time-range-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.time-separator {\r\n  margin: 0 5px;\r\n}\r\n\r\n.time-range-note {\r\n  font-size: 12px;\r\n  color: #999;\r\n  margin-top: 5px;\r\n}\r\n\r\n.platform-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.platform-checkboxes .el-checkbox {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.warning-type-selector,\r\n.process-method-selector,\r\n.priority-selector,\r\n.handle-method-selector {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.warning-type-selector .el-radio,\r\n.process-method-selector .el-radio,\r\n.priority-selector .el-radio,\r\n.handle-method-selector .el-radio {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.process-switch {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 10px;\r\n  padding: 5px 0;\r\n}\r\n\r\n.switch-label {\r\n  font-size: 13px;\r\n  color: #666;\r\n}\r\n\r\n.note-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.notify-method-checkboxes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.notify-method-checkboxes .el-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}