{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1749109381324}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\r\n  <div :class=\"{'show':show}\" class=\"header-search\">\r\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\r\n    <el-select\r\n      ref=\"headerSearchSelect\"\r\n      v-model=\"search\"\r\n      :remote-method=\"querySearch\"\r\n      filterable\r\n      default-first-option\r\n      remote\r\n      placeholder=\"Search\"\r\n      class=\"header-search-select\"\r\n      @change=\"change\"\r\n    >\r\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :value=\"option.item\" :label=\"option.item.title.join(' > ')\" />\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// fuse is a lightweight fuzzy-search module\r\n// make search results more in line with expectations\r\nimport Fuse from 'fuse.js/dist/fuse.min.js'\r\nimport path from 'path'\r\nimport { isHttp } from '@/utils/validate'\r\n\r\nexport default {\r\n  name: 'HeaderSearch',\r\n  data() {\r\n    return {\r\n      search: '',\r\n      options: [],\r\n      searchPool: [],\r\n      show: false,\r\n      fuse: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    routes() {\r\n      return this.$store.getters.permission_routes\r\n    }\r\n  },\r\n  watch: {\r\n    routes() {\r\n      this.searchPool = this.generateRoutes(this.routes)\r\n    },\r\n    searchPool(list) {\r\n      this.initFuse(list)\r\n    },\r\n    show(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.close)\r\n      } else {\r\n        document.body.removeEventListener('click', this.close)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.searchPool = this.generateRoutes(this.routes)\r\n  },\r\n  methods: {\r\n    click() {\r\n      this.show = !this.show\r\n      if (this.show) {\r\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\r\n      }\r\n    },\r\n    close() {\r\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\r\n      this.options = []\r\n      this.show = false\r\n    },\r\n    change(val) {\r\n      const path = val.path;\r\n      const query = val.query;\r\n      if(isHttp(val.path)) {\r\n        // http(s):// 路径新窗口打开\r\n        const pindex = path.indexOf(\"http\");\r\n        window.open(path.substr(pindex, path.length), \"_blank\");\r\n      } else {\r\n        if (query) {\r\n          this.$router.push({ path: path, query: JSON.parse(query) });\r\n        } else {\r\n          this.$router.push(path)\r\n        }\r\n      }\r\n      this.search = ''\r\n      this.options = []\r\n      this.$nextTick(() => {\r\n        this.show = false\r\n      })\r\n    },\r\n    initFuse(list) {\r\n      this.fuse = new Fuse(list, {\r\n        shouldSort: true,\r\n        threshold: 0.4,\r\n        location: 0,\r\n        distance: 100,\r\n        minMatchCharLength: 1,\r\n        keys: [{\r\n          name: 'title',\r\n          weight: 0.7\r\n        }, {\r\n          name: 'path',\r\n          weight: 0.3\r\n        }]\r\n      })\r\n    },\r\n    // Filter out the routes that can be displayed in the sidebar\r\n    // And generate the internationalized title\r\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\r\n      let res = []\r\n\r\n      for (const router of routes) {\r\n        // skip hidden router\r\n        if (router.hidden) { continue }\r\n\r\n        const data = {\r\n          path: !isHttp(router.path) ? path.resolve(basePath, router.path) : router.path,\r\n          title: [...prefixTitle]\r\n        }\r\n\r\n        if (router.meta && router.meta.title) {\r\n          data.title = [...data.title, router.meta.title]\r\n\r\n          if (router.redirect !== 'noRedirect') {\r\n            // only push the routes with title\r\n            // special case: need to exclude parent router without redirect\r\n            res.push(data)\r\n          }\r\n        }\r\n\r\n        if (router.query) {\r\n          data.query = router.query\r\n        }\r\n\r\n        // recursive child routes\r\n        if (router.children) {\r\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\r\n          if (tempRoutes.length >= 1) {\r\n            res = [...res, ...tempRoutes]\r\n          }\r\n        }\r\n      }\r\n      return res\r\n    },\r\n    querySearch(query) {\r\n      if (query !== '') {\r\n        this.options = this.fuse.search(query)\r\n      } else {\r\n        this.options = []\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-search {\r\n  font-size: 0 !important;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .header-search-select {\r\n    font-size: 18px;\r\n    transition: width 0.2s;\r\n    width: 0;\r\n    overflow: hidden;\r\n    background: transparent;\r\n    border-radius: 0;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    ::v-deep .el-input__inner {\r\n      border-radius: 0;\r\n      border: 0;\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n      box-shadow: none !important;\r\n      border-bottom: 1px solid #d9d9d9;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.show {\r\n    .header-search-select {\r\n      width: 210px;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}