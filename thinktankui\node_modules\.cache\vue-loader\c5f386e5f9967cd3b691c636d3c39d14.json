{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&scoped=true&lang=css", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\App.vue", "mtime": 1749109381292}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCiNhcHAgLnRoZW1lLXBpY2tlciB7DQogIGRpc3BsYXk6IG5vbmU7DQp9DQo="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n    <theme-picker />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from \"@/components/ThemePicker\";\r\n\r\nexport default {\r\n  name: \"App\",\r\n  components: { ThemePicker },\r\n  metaInfo() {\r\n    return {\r\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\r\n      titleTemplate: title => {\r\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n#app .theme-picker {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}