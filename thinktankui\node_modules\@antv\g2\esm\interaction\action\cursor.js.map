{"version": 3, "file": "cursor.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/cursor.ts"], "names": [], "mappings": ";AAAA;;;GAGG;AACH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B;;;GAGG;AACH;IAA2B,gCAAM;IAAjC;;IAoHA,CAAC;IAnHS,gCAAS,GAAjB,UAAkB,MAAM;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5B,CAAC;IAED,wBAAwB;IACjB,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5B,CAAC;IACD,oBAAoB;IACb,2BAAI,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IACD,gBAAgB;IACT,gCAAS,GAAhB;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD,4BAA4B;IACrB,2BAAI,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAED,gCAAgC;IACzB,2BAAI,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAED,eAAe;IACR,2BAAI,GAAX;QACE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IACD;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,+BAAQ,GAAf;QACE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IACD;;OAEG;IACI,6BAAM,GAAb;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5B,CAAC;IACD;;OAEG;IACI,8BAAO,GAAd;QACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC7B,CAAC;IACH,mBAAC;AAAD,CAAC,AApHD,CAA2B,MAAM,GAoHhC;AAED,eAAe,YAAY,CAAC", "sourcesContent": ["/**\n * @fileoverview 设置画布的箭头，参看：https://www.w3school.com.cn/jsref/prop_style_cursor.asp\n * <AUTHOR>\n */\nimport Action from './base';\n\n/**\n * 鼠标形状的 Action\n * @ignore\n */\nclass CursorAction extends Action {\n  private setCursor(cursor) {\n    const view = this.context.view;\n    view.getCanvas().setCursor(cursor);\n  }\n\n  /**\n   * 默认光标（通常是一个箭头）\n   */\n  public default() {\n    this.setCursor('default');\n  }\n\n  /** 光标呈现为指示链接的指针（一只手） */\n  public pointer() {\n    this.setCursor('pointer');\n  }\n  /** 此光标指示某对象可被移动。 */\n  public move() {\n    this.setCursor('move');\n  }\n  /** 光标呈现为十字线。 */\n  public crosshair() {\n    this.setCursor('crosshair');\n  }\n  /** 此光标指示程序正忙（通常是一只表或沙漏）。 */\n  public wait() {\n    this.setCursor('wait');\n  }\n\n  /** 此光标指示可用的帮助（通常是一个问号或一个气球）。 */\n  public help() {\n    this.setCursor('help');\n  }\n\n  /** 此光标指示文本。 */\n  public text() {\n    this.setCursor('text');\n  }\n\n  /**\n   * 此光标指示矩形框的边缘可被向右（东）移动。\n   */\n  public eResize() {\n    this.setCursor('e-resize');\n  }\n\n  /**\n   * 此光标指示矩形框的边缘可被向左（西）移动。\n   */\n  public wResize() {\n    this.setCursor('w-resize');\n  }\n\n  /**\n   * 此光标指示矩形框的边缘可被向上（北）移动。\n   */\n  public nResize() {\n    this.setCursor('n-resize');\n  }\n\n  /**\n   * 此光标指示矩形框的边缘可被向下（南）移动。\n   */\n  public sResize() {\n    this.setCursor('s-resize');\n  }\n  /**\n   * 光标指示可移动的方向 右上方（东北）\n   */\n  public neResize() {\n    this.setCursor('ne-resize');\n  }\n  /**\n   * 光标指示可移动的方向 左上方（西北）\n   */\n  public nwResize() {\n    this.setCursor('nw-resize');\n  }\n  /**\n   * 光标指示可移动的方向右下方（东南）\n   */\n  public seResize() {\n    this.setCursor('se-resize');\n  }\n  /**\n   * 光标指示可移动的方向左下方（西南）\n   */\n  public swResize() {\n    this.setCursor('sw-resize');\n  }\n\n  /**\n   * 光标指示可以在上下方向移动\n   */\n  public nsResize() {\n    this.setCursor('ns-resize');\n  }\n  /**\n   * 光标指示可以在左右方向移动\n   */\n  public ewResize() {\n    this.setCursor('ew-resize');\n  }\n  /**\n   * 光标显示可以被放大\n   */\n  public zoomIn() {\n    this.setCursor('zoom-in');\n  }\n  /**\n   * 光标显示可以缩小尺寸\n   */\n  public zoomOut() {\n    this.setCursor('zoom-out');\n  }\n}\n\nexport default CursorAction;\n"]}