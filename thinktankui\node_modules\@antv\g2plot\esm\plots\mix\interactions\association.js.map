{"version": 3, "file": "association.js", "sourceRoot": "", "sources": ["../../../../src/plots/mix/interactions/association.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAW,cAAc,EAAE,mBAAmB,EAAQ,MAAM,UAAU,CAAC;AACtF,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC3E,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAI1D;;;;;;;;;;;;GAYG;AACH;IAA0B,+BAAM;IAAhC;;IA2HA,CAAC;IA1HC;;;;;;;OAOG;IACK,yCAAmB,GAA3B,UAA4B,KAAa,EAAE,MAAqB;;QACtD,IAAA,KAAK,GAAK,IAAI,CAAC,OAAO,MAAjB,CAAkB;QACzB,IAAA,KAAqB,MAAM,IAAI,EAAE,EAA/B,SAAS,eAAA,EAAE,GAAG,SAAiB,CAAC;QAExC,IAAM,KAAK,GAAG,EAAE,CAAC;QAEjB,IAAI,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE;YACZ,IAAA,MAAI,GAAK,KAAK,CAAC,IAAI,KAAf,CAAgB;YAC5B,IAAI,CAAC,KAAK,EAAE,UAAC,CAAO;;gBAClB,IAAI,KAAK,GAAG,SAAS,CAAC;gBACtB,IAAI,GAAG,KAAK,GAAG,EAAE;oBACf,KAAK,GAAG,CAAC,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC;iBAC7B;qBAAM,IAAI,GAAG,KAAK,GAAG,EAAE;oBACtB,KAAK,GAAG,MAAA,CAAC,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,KAAK,KAAK,EAAjB,CAAiB,CAAC,0CAAE,KAAK,CAAC;iBAC9D;qBAAM,IAAI,CAAC,KAAK,EAAE;oBACjB,KAAK,GAAG,MAAA,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,0CAAE,KAAK,CAAC;iBACtC;gBACD,IAAI,CAAC,KAAK,EAAE;oBACV,OAAO;iBACR;gBACD,IAAM,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,UAAC,GAAG;oBAC1C,IAAI,MAAM,GAAG,KAAK,CAAC;oBACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;oBACrB,IAAM,SAAS,GAAG,OAAO,CAAC,MAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAI,EAAE,KAAK,CAAC,CAAC;oBACzE,IAAI,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,SAAS,EAAE;wBAC7C,MAAM,GAAG,IAAI,CAAC;qBACf;yBAAM;wBACL,QAAQ,GAAG,IAAI,CAAC;qBACjB;oBACD,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;gBACrD,CAAC,CAAC,CAAC;gBACH,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,QAAQ,EAAE;YAC1B,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,iCAAW,GAAlB,UAAmB,MAAqB;QACtC,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE5D,IAAI,CAAC,QAAQ,EAAE,UAAC,GAAG;YACjB,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,IAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBAC9C,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;aACrF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,iCAAW,GAAlB;QACE,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,EAAE,UAAC,OAAO;YACrB,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,4BAAM,GAAb,UAAc,MAAqB;QACjC,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEtD,IAAI,CAAC,KAAK,EAAE,UAAC,IAAe;YAClB,IAAA,MAAM,GAAc,IAAI,OAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAU;YACjC,IAAI,MAAM,EAAE;gBACV,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,8BAAQ,GAAf,UAAgB,MAAqB;QACnC,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEtD,IAAI,CAAC,KAAK,EAAE,UAAC,IAAe;YAClB,IAAA,MAAM,GAAc,IAAI,OAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAU;YACjC,IAAI,MAAM,EAAE;gBACV,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aACpC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,+BAAS,GAAhB,UAAiB,MAAqB;QACpC,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAEtD,IAAI,CAAC,KAAK,EAAE,UAAC,IAAe;YAClB,IAAA,QAAQ,GAAc,IAAI,SAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAU;YACnC,IAAI,QAAQ,EAAE;gBACZ,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aACpC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,2BAAK,GAAZ;QACE,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE,UAAC,CAAC;YACZ,cAAc,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IACH,kBAAC;AAAD,CAAC,AA3HD,CAA0B,MAAM,GA2H/B;AAED,cAAc,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AAE3C;;GAEG;AACH,mBAAmB,CAAC,oBAAoB,EAAE;IACxC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC;IACxE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC;CACtE,CAAC,CAAC;AAEH;;GAEG;AACH,mBAAmB,CAAC,sBAAsB,EAAE;IAC1C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,sBAAsB,EAAE,CAAC;IAC1E,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC;CACtE,CAAC,CAAC;AAEH;;GAEG;AACH,mBAAmB,CAAC,uBAAuB,EAAE;IAC3C,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,uBAAuB,EAAE,CAAC;IAC3E,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,mBAAmB,EAAE,CAAC;CACtE,CAAC,CAAC;AAEH;;GAEG;AACH,mBAAmB,CAAC,qBAAqB,EAAE;IACzC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC;IAC5E,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC;CAC5E,CAAC,CAAC", "sourcesContent": ["import { Action, Element, registerAction, registerInteraction, View } from '@antv/g2';\nimport { each, get, isArray, map } from '@antv/util';\nimport { getAllElements, getSiblingViews, getViews } from '../../../utils';\nimport { clearHighlight, getElementValue } from './utils';\n\ntype EventItem = { element: Element; view: View; active: boolean; inactive: boolean };\ntype ActionParams = { linkField?: string; dim?: 'x' | 'y' };\n/**\n * 存在多个 view 时，view 之间的联动交互\n *\n * 提供四个反馈 action，均接受参数：linkField 关联字段，dim 维度\n * 1. showTooltip\n * 2. active\n * 3. highlight\n * 4. selected\n *\n * 附加，两个结束反馈 action：\n * 1. hidetooltip\n * 2. reset 清除激活和高亮状态\n */\nclass Association extends Action {\n  /**\n   * 获取关联的 elements\n   *\n   * - 如果 dim 参数存在，根据 dim 获取相应的 field。与 linkField 不匹配则 return\n   * - 否则 dim 参数不存在，且 linkField 存在，则作为关联字段\n   * - 否则若 linkField 不存在，则获取第一个分类字段\n   * @returns EventItem[]\n   */\n  private getAssociationItems(views: View[], params?: ActionParams): EventItem[] {\n    const { event } = this.context;\n    const { linkField, dim } = params || {};\n\n    const items = [];\n\n    if (event.data?.data) {\n      const { data } = event.data;\n      each(views, (v: View) => {\n        let field = linkField;\n        if (dim === 'x') {\n          field = v.getXScale().field;\n        } else if (dim === 'y') {\n          field = v.getYScales().find((s) => s.field === field)?.field;\n        } else if (!field) {\n          field = v.getGroupScales()[0]?.field;\n        }\n        if (!field) {\n          return;\n        }\n        const elements = map(getAllElements(v), (ele) => {\n          let active = false;\n          let inactive = false;\n          const dataValue = isArray(data) ? get(data[0], field) : get(data, field);\n          if (getElementValue(ele, field) === dataValue) {\n            active = true;\n          } else {\n            inactive = true;\n          }\n          return { element: ele, view: v, active, inactive };\n        });\n        items.push(...elements);\n      });\n    }\n\n    return items;\n  }\n\n  /**\n   * 所有同一层级的 tooltip 显示\n   */\n  public showTooltip(params?: ActionParams) {\n    const siblings = getSiblingViews(this.context.view);\n    const elements = this.getAssociationItems(siblings, params);\n\n    each(elements, (ele) => {\n      if (ele.active) {\n        const box = ele.element.shape.getCanvasBBox();\n        ele.view.showTooltip({ x: box.minX + box.width / 2, y: box.minY + box.height / 2 });\n      }\n    });\n  }\n\n  /**\n   * 隐藏同一层级的 tooltip\n   */\n  public hideTooltip() {\n    const siblings = getSiblingViews(this.context.view);\n    each(siblings, (sibling) => {\n      sibling.hideTooltip();\n    });\n  }\n\n  /**\n   * 设置 active 状态\n   */\n  public active(params?: ActionParams) {\n    const views = getViews(this.context.view);\n    const items = this.getAssociationItems(views, params);\n\n    each(items, (item: EventItem) => {\n      const { active, element } = item;\n      if (active) {\n        element.setState('active', true);\n      }\n    });\n  }\n\n  /**\n   * 设置 selected 状态\n   */\n  public selected(params?: ActionParams) {\n    const views = getViews(this.context.view);\n    const items = this.getAssociationItems(views, params);\n\n    each(items, (item: EventItem) => {\n      const { active, element } = item;\n      if (active) {\n        element.setState('selected', true);\n      }\n    });\n  }\n\n  /**\n   * 进行高亮 => 设置 inactive 状态\n   */\n  public highlight(params?: ActionParams) {\n    const views = getViews(this.context.view);\n    const items = this.getAssociationItems(views, params);\n\n    each(items, (item: EventItem) => {\n      const { inactive, element } = item;\n      if (inactive) {\n        element.setState('inactive', true);\n      }\n    });\n  }\n\n  public reset() {\n    const views = getViews(this.context.view);\n    each(views, (v) => {\n      clearHighlight(v);\n    });\n  }\n}\n\nregisterAction('association', Association);\n\n/**\n * 相邻 view 的 active 联动（相同维值的 tooltip 联动）\n */\nregisterInteraction('association-active', {\n  start: [{ trigger: 'element:mouseenter', action: 'association:active' }],\n  end: [{ trigger: 'element:mouseleave', action: 'association:reset' }],\n});\n\n/**\n * 相邻 view 的 active 联动（相同维值的 tooltip 联动）\n */\nregisterInteraction('association-selected', {\n  start: [{ trigger: 'element:mouseenter', action: 'association:selected' }],\n  end: [{ trigger: 'element:mouseleave', action: 'association:reset' }],\n});\n\n/**\n * 相邻 view 的 highlight 联动, 突出当前 element\n */\nregisterInteraction('association-highlight', {\n  start: [{ trigger: 'element:mouseenter', action: 'association:highlight' }],\n  end: [{ trigger: 'element:mouseleave', action: 'association:reset' }],\n});\n\n/**\n * 相邻 view 的 tooltip 联动，根据 groupField 进行关联（相同维值的 tooltip 联动）\n */\nregisterInteraction('association-tooltip', {\n  start: [{ trigger: 'element:mousemove', action: 'association:showTooltip' }],\n  end: [{ trigger: 'element:mouseleave', action: 'association:hideTooltip' }],\n});\n"]}