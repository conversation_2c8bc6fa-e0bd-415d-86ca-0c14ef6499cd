from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.dao.warning_dao import WarningRecordDao, WarningSchemeDao, WarningSettingsDao
from module_admin.entity.vo.warning_vo import (
    WarningRecordPageQueryModel, WarningSchemePageQueryModel, WarningRecordModel, 
    WarningSchemeModel, WarningSettingsModel, DeleteWarningRecordModel, DeleteWarningSchemeModel
)
from utils.response_util import ResponseUtil


class WarningRecordService:
    """
    预警记录服务层
    """

    @staticmethod
    async def get_warning_record_list_services(db: AsyncSession, query_object: WarningRecordPageQueryModel, is_page: bool = False):
        """
        获取预警记录列表信息service

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警记录列表信息对象
        """
        try:
            record_list_result = await WarningRecordDao.get_warning_record_list(db, query_object, is_page)
            return record_list_result
        except Exception as e:
            raise e

    @staticmethod
    async def warning_record_detail_services(db: AsyncSession, record_id: int):
        """
        获取预警记录详细信息service

        :param db: orm对象
        :param record_id: 记录ID
        :return: 预警记录详细信息对象
        """
        try:
            record = await WarningRecordDao.get_warning_record_by_id(db, record_id)
            if record:
                return record.__dict__
            return None
        except Exception as e:
            raise e

    @staticmethod
    async def add_warning_record_services(db: AsyncSession, record: WarningRecordModel):
        """
        新增预警记录service

        :param db: orm对象
        :param record: 预警记录对象
        :return: 新增预警记录结果
        """
        try:
            record_data = record.model_dump(exclude_unset=True)
            record_data['create_time'] = datetime.now()
            record_data['update_time'] = datetime.now()
            
            new_record = await WarningRecordDao.add_warning_record(db, record_data)
            if new_record:
                return ResponseUtil.success(msg="新增成功")
            else:
                return ResponseUtil.failure(msg="新增失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"新增失败: {str(e)}")

    @staticmethod
    async def edit_warning_record_services(db: AsyncSession, record: WarningRecordModel):
        """
        编辑预警记录service

        :param db: orm对象
        :param record: 预警记录对象
        :return: 编辑预警记录结果
        """
        try:
            record_data = record.model_dump(exclude_unset=True)
            record_data['update_time'] = datetime.now()
            
            result = await WarningRecordDao.edit_warning_record(db, record_data)
            if result > 0:
                return ResponseUtil.success(msg="更新成功")
            else:
                return ResponseUtil.failure(msg="更新失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"更新失败: {str(e)}")

    @staticmethod
    async def delete_warning_record_services(db: AsyncSession, delete_record: DeleteWarningRecordModel):
        """
        删除预警记录service

        :param db: orm对象
        :param delete_record: 删除预警记录对象
        :return: 删除预警记录结果
        """
        try:
            record_ids = [int(id_str.strip()) for id_str in delete_record.record_ids.split(',') if id_str.strip()]
            
            result = await WarningRecordDao.delete_warning_records(db, record_ids)
            if result > 0:
                return ResponseUtil.success(msg=f"删除成功，共删除{result}条记录")
            else:
                return ResponseUtil.failure(msg="删除失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"删除失败: {str(e)}")

    @staticmethod
    async def get_warning_statistics_services(db: AsyncSession):
        """
        获取预警统计信息service

        :param db: orm对象
        :return: 统计信息
        """
        try:
            record_stats = await WarningRecordDao.get_warning_statistics(db)
            scheme_stats = await WarningSchemeDao.get_scheme_statistics(db)
            
            # 合并统计信息
            statistics = {**record_stats, **scheme_stats}
            return statistics
        except Exception as e:
            return {
                'total_records': 0,
                'unprocessed_count': 0,
                'processed_count': 0,
                'ignored_count': 0,
                'today_count': 0,
                'scheme_count': 0,
                'active_scheme_count': 0
            }


class WarningSchemeService:
    """
    预警方案服务层
    """

    @staticmethod
    async def get_warning_scheme_list_services(db: AsyncSession, query_object: WarningSchemePageQueryModel, is_page: bool = False):
        """
        获取预警方案列表信息service

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 预警方案列表信息对象
        """
        try:
            scheme_list_result = await WarningSchemeDao.get_warning_scheme_list(db, query_object, is_page)
            return scheme_list_result
        except Exception as e:
            raise e

    @staticmethod
    async def warning_scheme_detail_services(db: AsyncSession, scheme_id: int):
        """
        获取预警方案详细信息service

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 预警方案详细信息对象
        """
        try:
            scheme = await WarningSchemeDao.get_warning_scheme_by_id(db, scheme_id)
            if scheme:
                return scheme.__dict__
            return None
        except Exception as e:
            raise e

    @staticmethod
    async def add_warning_scheme_services(db: AsyncSession, scheme: WarningSchemeModel):
        """
        新增预警方案service

        :param db: orm对象
        :param scheme: 预警方案对象
        :return: 新增预警方案结果
        """
        try:
            scheme_data = scheme.model_dump(exclude_unset=True)
            scheme_data['create_time'] = datetime.now()
            scheme_data['update_time'] = datetime.now()
            
            new_scheme = await WarningSchemeDao.add_warning_scheme(db, scheme_data)
            if new_scheme:
                return ResponseUtil.success(msg="新增成功", data={"id": new_scheme.id})
            else:
                return ResponseUtil.failure(msg="新增失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"新增失败: {str(e)}")

    @staticmethod
    async def edit_warning_scheme_services(db: AsyncSession, scheme: WarningSchemeModel):
        """
        编辑预警方案service

        :param db: orm对象
        :param scheme: 预警方案对象
        :return: 编辑预警方案结果
        """
        try:
            scheme_data = scheme.model_dump(exclude_unset=True)
            scheme_data['update_time'] = datetime.now()
            
            result = await WarningSchemeDao.edit_warning_scheme(db, scheme_data)
            if result > 0:
                return ResponseUtil.success(msg="更新成功")
            else:
                return ResponseUtil.failure(msg="更新失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"更新失败: {str(e)}")

    @staticmethod
    async def delete_warning_scheme_services(db: AsyncSession, delete_scheme: DeleteWarningSchemeModel):
        """
        删除预警方案service

        :param db: orm对象
        :param delete_scheme: 删除预警方案对象
        :return: 删除预警方案结果
        """
        try:
            scheme_ids = [int(id_str.strip()) for id_str in delete_scheme.scheme_ids.split(',') if id_str.strip()]
            
            result = await WarningSchemeDao.delete_warning_schemes(db, scheme_ids)
            if result > 0:
                return ResponseUtil.success(msg=f"删除成功，共删除{result}条记录")
            else:
                return ResponseUtil.failure(msg="删除失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"删除失败: {str(e)}")


class WarningSettingsService:
    """
    预警设置服务层
    """

    @staticmethod
    async def get_warning_settings_services(db: AsyncSession, scheme_id: int):
        """
        获取预警设置service

        :param db: orm对象
        :param scheme_id: 方案ID
        :return: 预警设置信息
        """
        try:
            settings = await WarningSettingsDao.get_warning_settings_by_scheme_id(db, scheme_id)
            if settings:
                return settings.__dict__
            else:
                # 返回默认设置
                return {
                    "scheme_id": scheme_id,
                    "platform_types": {
                        "all": True,
                        "webpage": True,
                        "wechat": True,
                        "weibo": True,
                        "toutiao": True,
                        "app": True,
                        "video": True,
                        "forum": True,
                        "newspaper": True,
                        "qa": True
                    },
                    "content_property": "all",
                    "info_type": "noncomment",
                    "match_objects": {
                        "all_checked": True,
                        "title": False,
                        "content": False,
                        "media": False,
                        "original": False
                    },
                    "match_method": "exact",
                    "publish_regions": ["全部"],
                    "ip_areas": ["全部"],
                    "media_categories": [],
                    "article_categories": []
                }
        except Exception as e:
            return None

    @staticmethod
    async def save_warning_settings_services(db: AsyncSession, settings: WarningSettingsModel):
        """
        保存预警设置service

        :param db: orm对象
        :param settings: 预警设置对象
        :return: 保存结果
        """
        try:
            settings_data = settings.model_dump(exclude_unset=True)
            settings_data['update_time'] = datetime.now()
            
            result = await WarningSettingsDao.save_warning_settings(db, settings_data)
            if result:
                return ResponseUtil.success(msg="预警设置保存成功")
            else:
                return ResponseUtil.failure(msg="保存失败")
        except Exception as e:
            return ResponseUtil.error(msg=f"保存失败: {str(e)}")
