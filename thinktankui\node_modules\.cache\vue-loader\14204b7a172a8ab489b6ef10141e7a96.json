{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\RightPanel\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\RightPanel\\index.vue", "mtime": 1749109381326}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdSaWdodFBhbmVsJywNCiAgcHJvcHM6IHsNCiAgICBjbGlja05vdENsb3NlOiB7DQogICAgICBkZWZhdWx0OiBmYWxzZSwNCiAgICAgIHR5cGU6IEJvb2xlYW4NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgc2hvdzogew0KICAgICAgZ2V0KCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2hvd1NldHRpbmdzDQogICAgICB9LA0KICAgICAgc2V0KHZhbCkgew0KICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnc2V0dGluZ3MvY2hhbmdlU2V0dGluZycsIHsNCiAgICAgICAgICBrZXk6ICdzaG93U2V0dGluZ3MnLA0KICAgICAgICAgIHZhbHVlOiB2YWwNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvdyh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlICYmICF0aGlzLmNsaWNrTm90Q2xvc2UpIHsNCiAgICAgICAgdGhpcy5hZGRFdmVudENsaWNrKCkNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5hZGRFdmVudENsaWNrKCkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICBjb25zdCBlbHggPSB0aGlzLiRyZWZzLnJpZ2h0UGFuZWwNCiAgICBlbHgucmVtb3ZlKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFkZEV2ZW50Q2xpY2soKSB7DQogICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcikNCiAgICB9LA0KICAgIGNsb3NlU2lkZWJhcihldnQpIHsNCiAgICAgIGNvbnN0IHBhcmVudCA9IGV2dC50YXJnZXQuY2xvc2VzdCgnLmVsLWRyYXdlcl9fYm9keScpDQogICAgICBpZiAoIXBhcmVudCkgew0KICAgICAgICB0aGlzLnNob3cgPSBmYWxzZQ0KICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCB0aGlzLmNsb3NlU2lkZWJhcikNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    }\r\n  },\r\n  computed: {\r\n    show: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.addEventClick()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.el-drawer__body')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 260px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"]}]}