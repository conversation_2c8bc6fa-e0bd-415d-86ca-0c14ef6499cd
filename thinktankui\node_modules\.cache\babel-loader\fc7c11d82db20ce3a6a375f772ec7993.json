{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\App.vue", "mtime": 1749109381292}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKdmFyIF9UaGVtZVBpY2tlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9jb21wb25lbnRzL1RoZW1lUGlja2VyIikpOwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIkFwcCIsCiAgY29tcG9uZW50czogewogICAgVGhlbWVQaWNrZXI6IF9UaGVtZVBpY2tlci5kZWZhdWx0CiAgfSwKICBtZXRhSW5mbzogZnVuY3Rpb24gbWV0YUluZm8oKSB7CiAgICByZXR1cm4gewogICAgICB0aXRsZTogdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3MuZHluYW1pY1RpdGxlICYmIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnRpdGxlLAogICAgICB0aXRsZVRlbXBsYXRlOiBmdW5jdGlvbiB0aXRsZVRlbXBsYXRlKHRpdGxlKSB7CiAgICAgICAgcmV0dXJuIHRpdGxlID8gIiIuY29uY2F0KHRpdGxlLCAiIC0gIikuY29uY2F0KHByb2Nlc3MuZW52LlZVRV9BUFBfVElUTEUpIDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9USVRMRTsKICAgICAgfQogICAgfTsKICB9Cn07"}, {"version": 3, "names": ["_ThemePicker", "_interopRequireDefault", "require", "name", "components", "ThemePicker", "metaInfo", "title", "$store", "state", "settings", "dynamicTitle", "titleTemplate", "concat", "process", "env", "VUE_APP_TITLE"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app\">\r\n    <router-view />\r\n    <theme-picker />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from \"@/components/ThemePicker\";\r\n\r\nexport default {\r\n  name: \"App\",\r\n  components: { ThemePicker },\r\n  metaInfo() {\r\n    return {\r\n      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,\r\n      titleTemplate: title => {\r\n        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped>\r\n#app .theme-picker {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAQA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,QAAA,WAAAA,SAAA;IACA;MACAC,KAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA,SAAAH,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;MACAK,aAAA,WAAAA,cAAAL,KAAA;QACA,OAAAA,KAAA,MAAAM,MAAA,CAAAN,KAAA,SAAAM,MAAA,CAAAC,OAAA,CAAAC,GAAA,CAAAC,aAAA,IAAAF,OAAA,CAAAC,GAAA,CAAAC,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}