{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\operlog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\operlog\\index.vue", "mtime": 1749109381347}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0LCBkZWxPcGVybG9nLCBjbGVhbk9wZXJsb2cgfSBmcm9tICJAL2FwaS9tb25pdG9yL29wZXJsb2ciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJPcGVybG9nIiwNCiAgZGljdHM6IFsnc3lzX29wZXJfdHlwZScsICdzeXNfY29tbW9uX3N0YXR1cyddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOihqOagvOaVsOaNrg0KICAgICAgbGlzdDogW10sDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g6buY6K6k5o6S5bqPDQogICAgICBkZWZhdWx0U29ydDoge3Byb3A6ICdvcGVyVGltZScsIG9yZGVyOiAnZGVzY2VuZGluZyd9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBvcGVySXA6IHVuZGVmaW5lZCwNCiAgICAgICAgdGl0bGU6IHVuZGVmaW5lZCwNCiAgICAgICAgb3Blck5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgYnVzaW5lc3NUeXBlOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LnmbvlvZXml6Xlv5cgKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3QodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKCByZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICk7DQogICAgfSwNCiAgICAvLyDmk43kvZzml6Xlv5fnsbvlnovlrZflhbjnv7vor5ENCiAgICB0eXBlRm9ybWF0KHJvdywgY29sdW1uKSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5kaWN0LnR5cGUuc3lzX29wZXJfdHlwZSwgcm93LmJ1c2luZXNzVHlwZSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy4kcmVmcy50YWJsZXMuc29ydCh0aGlzLmRlZmF1bHRTb3J0LnByb3AsIHRoaXMuZGVmYXVsdFNvcnQub3JkZXIpDQogICAgfSwNCiAgICAvKiog5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uICovDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vcGVySWQpDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmjpLluo/op6blj5Hkuovku7YgKi8NCiAgICBoYW5kbGVTb3J0Q2hhbmdlKGNvbHVtbiwgcHJvcCwgb3JkZXIpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMub3JkZXJCeUNvbHVtbiA9IGNvbHVtbi5wcm9wOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc0FzYyA9IGNvbHVtbi5vcmRlcjsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOivpue7huaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5mb3JtID0gcm93Ow0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IG9wZXJJZHMgPSByb3cub3BlcklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5pel5b+X57yW5Y+35Li6IicgKyBvcGVySWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsT3BlcmxvZyhvcGVySWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5riF56m65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQ2xlYW4oKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTmuIXnqbrmiYDmnInmk43kvZzml6Xlv5fmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gY2xlYW5PcGVybG9nKCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21vbml0b3Ivb3BlcmxvZy9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBvcGVybG9nXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/operlog", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"操作地址\" prop=\"operIp\">\r\n        <el-input\r\n          v-model=\"queryParams.operIp\"\r\n          placeholder=\"请输入操作地址\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"系统模块\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入系统模块\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"操作人员\" prop=\"operName\">\r\n        <el-input\r\n          v-model=\"queryParams.operName\"\r\n          placeholder=\"请输入操作人员\"\r\n          clearable\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"类型\" prop=\"businessType\">\r\n        <el-select\r\n          v-model=\"queryParams.businessType\"\r\n          placeholder=\"操作类型\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_oper_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"操作状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_common_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"操作时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :default-time=\"['00:00:00', '23:59:59']\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['monitor:operlog:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          @click=\"handleClean\"\r\n          v-hasPermi=\"['monitor:operlog:remove']\"\r\n        >清空</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['monitor:operlog:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" @selection-change=\"handleSelectionChange\" :default-sort=\"defaultSort\" @sort-change=\"handleSortChange\">\r\n      <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n      <el-table-column label=\"日志编号\" align=\"center\" prop=\"operId\" />\r\n      <el-table-column label=\"系统模块\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作类型\" align=\"center\" prop=\"businessType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_oper_type\" :value=\"scope.row.businessType\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作人员\" align=\"center\" prop=\"operName\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\" />\r\n      <el-table-column label=\"操作地址\" align=\"center\" prop=\"operIp\" width=\"130\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作地点\" align=\"center\" prop=\"operLocation\" :show-overflow-tooltip=\"true\" />\r\n      <el-table-column label=\"操作状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作日期\" align=\"center\" prop=\"operTime\" width=\"160\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.operTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"消耗时间\" align=\"center\" prop=\"costTime\" width=\"110\" :show-overflow-tooltip=\"true\" sortable=\"custom\" :sort-orders=\"['descending', 'ascending']\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ scope.row.costTime }}毫秒</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row,scope.index)\"\r\n            v-hasPermi=\"['monitor:operlog:query']\"\r\n          >详细</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 操作日志详细 -->\r\n    <el-dialog title=\"操作日志详细\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"100px\" size=\"mini\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"操作模块：\">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>\r\n            <el-form-item\r\n              label=\"登录信息：\"\r\n            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"请求地址：\">{{ form.operUrl }}</el-form-item>\r\n            <el-form-item label=\"请求方式：\">{{ form.requestMethod }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"操作方法：\">{{ form.method }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"请求参数：\">{{ form.operParam }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"返回参数：\">{{ form.jsonResult }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"操作状态：\">\r\n              <div v-if=\"form.status === 0\">正常</div>\r\n              <div v-else-if=\"form.status === 1\">失败</div>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"消耗时间：\">{{ form.costTime }}毫秒</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"操作时间：\">{{ parseTime(form.operTime) }}</el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"异常信息：\" v-if=\"form.status === 1\">{{ form.errorMsg }}</el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"open = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { list, delOperlog, cleanOperlog } from \"@/api/monitor/operlog\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: ['sys_oper_type', 'sys_common_status'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      list: [],\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 默认排序\r\n      defaultSort: {prop: 'operTime', order: 'descending'},\r\n      // 表单参数\r\n      form: {},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        operIp: undefined,\r\n        title: undefined,\r\n        operName: undefined,\r\n        businessType: undefined,\r\n        status: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询登录日志 */\r\n    getList() {\r\n      this.loading = true;\r\n      list(this.addDateRange(this.queryParams, this.dateRange)).then( response => {\r\n          this.list = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(this.dict.type.sys_oper_type, row.businessType);\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.pageNum = 1;\r\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 排序触发事件 */\r\n    handleSortChange(column, prop, order) {\r\n      this.queryParams.orderByColumn = column.prop;\r\n      this.queryParams.isAsc = column.order;\r\n      this.getList();\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operIds = row.operId || this.ids;\r\n      this.$modal.confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项？').then(function() {\r\n        return delOperlog(operIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$modal.confirm('是否确认清空所有操作日志数据项？').then(function() {\r\n        return cleanOperlog();\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"清空成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('monitor/operlog/export', {\r\n        ...this.queryParams\r\n      }, `operlog_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n"]}]}