{"version": 3, "file": "dim-rect.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/mask/dim-rect.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAE/C,OAAO,QAAQ,MAAM,QAAQ,CAAC;AAE9B,SAAS,UAAU,CAAC,KAAK;IACvB,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAClD,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,GAAG,GAAG,IAAI,CAAC;IACf,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC/C,IAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7C,IAAI,MAAM,EAAE;QACV,gBAAgB;QAChB,UAAU,CAAC,WAAW,CAAC,CAAC;QACxB,UAAU,CAAC,SAAS,CAAC,CAAC;KACvB;IACD,IAAI,GAAG,KAAK,GAAG,EAAE;QACf,qBAAqB;QACrB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;YACpB,CAAC,EAAE,WAAW,CAAC,CAAC;YAChB,CAAC,EAAE,CAAC;SACL,CAAC,CAAC;QACH,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC;YAClB,CAAC,EAAE,SAAS,CAAC,CAAC;YACd,CAAC,EAAE,CAAC;SACL,CAAC,CAAC;KACJ;SAAM;QACL,qBAAqB;QACrB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC;YACpB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,WAAW,CAAC,CAAC;SACjB,CAAC,CAAC;QACH,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC;YAClB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,SAAS,CAAC,CAAC;SACf,CAAC,CAAC;KACJ;IACD,OAAO;QACL,KAAK,OAAA;QACL,GAAG,KAAA;KACJ,CAAC;AACJ,CAAC;AAED;;GAEG;AACH;IAAsB,2BAAQ;IAA9B;QAAA,qEAOC;QANW,SAAG,GAAG,GAAG,CAAC;QACV,YAAM,GAAG,IAAI,CAAC;;IAK1B,CAAC;IAJW,2BAAS,GAAnB;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAChD,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;IACH,cAAC;AAAD,CAAC,AAPD,CAAsB,QAAQ,GAO7B;AAED,eAAe,OAAO,CAAC", "sourcesContent": ["import { clamp, head, last } from '@antv/util';\nimport { Region } from '../../../interface';\nimport RectMask from './rect';\n\nfunction clampPoint(point) {\n  point.x = clamp(point.x, 0, 1);\n  point.y = clamp(point.y, 0, 1);\n}\n\nexport function getRegion(points, dim, inPlot, coord): Region {\n  let start = null;\n  let end = null;\n  const normalStart = coord.invert(head(points));\n  const normalEnd = coord.invert(last(points));\n  if (inPlot) {\n    // 约束到 0 - 1 范围内\n    clampPoint(normalStart);\n    clampPoint(normalEnd);\n  }\n  if (dim === 'x') {\n    // x 轴方向扩展, y 轴方向占满全部\n    start = coord.convert({\n      x: normalStart.x,\n      y: 0,\n    });\n    end = coord.convert({\n      x: normalEnd.x,\n      y: 1,\n    });\n  } else {\n    // y 轴方向扩展, x 轴方向占满全部\n    start = coord.convert({\n      x: 0,\n      y: normalStart.y,\n    });\n    end = coord.convert({\n      x: 1,\n      y: normalEnd.y,\n    });\n  }\n  return {\n    start,\n    end,\n  };\n}\n\n/**\n * @ignore\n */\nclass DimRect extends RectMask {\n  protected dim = 'x';\n  protected inPlot = true;\n  protected getRegion() {\n    const coord = this.context.view.getCoordinate();\n    return getRegion(this.points, this.dim, this.inPlot, coord);\n  }\n}\n\nexport default DimRect;\n"]}