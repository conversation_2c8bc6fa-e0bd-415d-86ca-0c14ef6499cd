{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\monitor\\server\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\monitor\\server\\index.vue", "mtime": 1749109381348}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfc2VydmVyID0gcmVxdWlyZSgiQC9hcGkvbW9uaXRvci9zZXJ2ZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJTZXJ2ZXIiLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmnI3liqHlmajkv6Hmga8KICAgICAgc2VydmVyOiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMub3BlbkxvYWRpbmcoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LmnI3liqHlmajkv6Hmga8gKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAoMCwgX3NlcnZlci5nZXRTZXJ2ZXIpKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5zZXJ2ZXIgPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5omT5byA5Yqg6L295bGCCiAgICBvcGVuTG9hZGluZzogZnVuY3Rpb24gb3BlbkxvYWRpbmcoKSB7CiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWKoOi9veacjeWKoeebkeaOp+aVsOaNru+8jOivt+eojeWAme+8gSIpOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_server", "require", "name", "data", "server", "created", "getList", "openLoading", "methods", "_this", "getServer", "then", "response", "$modal", "closeLoading", "loading"], "sources": ["src/views/monitor/server/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-cpu\"></i> CPU</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n                <tr>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">值</div></th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">核心数</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.cpuNum }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">用户使用率</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.used }}%</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统使用率</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.sys }}%</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">当前空闲率</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.cpu\">{{ server.cpu.free }}%</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-tickets\"></i> 内存</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n                <tr>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">属性</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">内存</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">Python</div></th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">总内存</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.total }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.total }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">已用内存</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.used}}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.used}}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">剩余内存</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\">{{ server.mem.free }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.free }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">使用率</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\">{{ server.mem.usage }}%</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\" :class=\"{'text-danger': server.py.usage > 80}\">{{ server.py.usage }}%</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-monitor\"></i> 服务器信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器名称</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerName }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">操作系统</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osName }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">服务器IP</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.computerIp }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">系统架构</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.osArch }}</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-coffee-cup\"></i> Python解释器信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;table-layout:fixed;\">\r\n              <tbody>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Python名称</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.name }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">Python版本</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.version }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">启动时间</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.startTime }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">运行时长</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.runTime }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">安装路径</div></td>\r\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.py\">{{ server.py.home }}</div></td>\r\n                </tr>\r\n                <tr>\r\n                  <td colspan=\"1\" class=\"el-table__cell is-leaf\"><div class=\"cell\">项目路径</div></td>\r\n                  <td colspan=\"3\" class=\"el-table__cell is-leaf\"><div class=\"cell\" v-if=\"server.sys\">{{ server.sys.userDir }}</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-receiving\"></i> 磁盘状态</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n                <tr>\r\n                  <th class=\"el-table__cell el-table__cell is-leaf\"><div class=\"cell\">盘符路径</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">文件系统</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">盘符名称</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">总大小</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">可用大小</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用大小</div></th>\r\n                  <th class=\"el-table__cell is-leaf\"><div class=\"cell\">已用百分比</div></th>\r\n                </tr>\r\n              </thead>\r\n              <tbody v-if=\"server.sysFiles\">\r\n                <tr v-for=\"(sysFile, index) in server.sysFiles\" :key=\"index\">\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.dirName }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.sysTypeName }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.typeName }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.total }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.free }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\">{{ sysFile.used }}</div></td>\r\n                  <td class=\"el-table__cell is-leaf\"><div class=\"cell\" :class=\"{'text-danger': sysFile.usage > 80}\">{{ sysFile.usage }}%</div></td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getServer } from \"@/api/monitor/server\";\r\n\r\nexport default {\r\n  name: \"Server\",\r\n  data() {\r\n    return {\r\n      // 服务器信息\r\n      server: []\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查询服务器信息 */\r\n    getList() {\r\n      getServer().then(response => {\r\n        this.server = response.data;\r\n        this.$modal.closeLoading();\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载服务监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;AA8KA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,cACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,MAAA,GAAAQ,QAAA,CAAAT,IAAA;QACAM,KAAA,CAAAI,MAAA,CAAAC,YAAA;MACA;IACA;IACA;IACAP,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAAE,OAAA;IACA;EACA;AACA", "ignoreList": []}]}