{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\link.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\link.js", "mtime": 1749109533419}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inline", "_interopRequireDefault", "require", "Link", "exports", "default", "_Inline", "_classCallCheck2", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "statics", "blotName", "_superPropGet2", "domNode", "setAttribute", "constructor", "sanitize", "create", "node", "formats", "getAttribute", "url", "PROTOCOL_WHITELIST", "SANITIZED_URL", "Inline", "_defineProperty2", "protocols", "anchor", "document", "createElement", "href", "protocol", "slice", "indexOf"], "sources": ["../../src/formats/link.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Link extends Inline {\n  static blotName = 'link';\n  static tagName = 'A';\n  static SANITIZED_URL = 'about:blank';\n  static PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel', 'sms'];\n\n  static create(value: string) {\n    const node = super.create(value) as HTMLElement;\n    node.setAttribute('href', this.sanitize(value));\n    node.setAttribute('rel', 'noopener noreferrer');\n    node.setAttribute('target', '_blank');\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    return domNode.getAttribute('href');\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n  }\n\n  format(name: string, value: unknown) {\n    if (name !== this.statics.blotName || !value) {\n      super.format(name, value);\n    } else {\n      // @ts-expect-error\n      this.domNode.setAttribute('href', this.constructor.sanitize(value));\n    }\n  }\n}\n\nfunction sanitize(url: string, protocols: string[]) {\n  const anchor = document.createElement('a');\n  anchor.href = url;\n  const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n  return protocols.indexOf(protocol) > -1;\n}\n\nexport { Link as default, sanitize };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuC,IAEjCC,IAAI,GAAAC,OAAA,CAAAC,OAAA,0BAAAC,OAAA;EAAA,SAAAH,KAAA;IAAA,IAAAI,gBAAA,CAAAF,OAAA,QAAAF,IAAA;IAAA,WAAAK,WAAA,CAAAH,OAAA,QAAAF,IAAA,EAAAM,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAL,OAAA,EAAAF,IAAA,EAAAG,OAAA;EAAA,WAAAK,aAAA,CAAAN,OAAA,EAAAF,IAAA;IAAAS,GAAA;IAAAC,KAAA,EAsBR,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAc,EAAE;MACnC,IAAIE,IAAI,KAAK,IAAI,CAACC,OAAO,CAACC,QAAQ,IAAI,CAACJ,KAAK,EAAE;QAC5C,IAAAK,cAAA,CAAAb,OAAA,EAAAF,IAAA,sBAAaY,IAAI,EAAEF,KAAK;MAC1B,CAAC,MAAM;QACL;QACA,IAAI,CAACM,OAAO,CAACC,YAAY,CAAC,MAAM,EAAE,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACT,KAAK,CAAC,CAAC;MACrE;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAvBA,SAAOU,MAAMA,CAACV,KAAa,EAAE;MAC3B,IAAMW,IAAI,OAAAN,cAAA,CAAAb,OAAA,EAAAF,IAAA,sBAAgBU,KAAK,EAAgB;MAC/CW,IAAI,CAACJ,YAAY,CAAC,MAAM,EAAE,IAAI,CAACE,QAAQ,CAACT,KAAK,CAAC,CAAC;MAC/CW,IAAI,CAACJ,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC;MAC/CI,IAAI,CAACJ,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACrC,OAAOI,IAAI;IACb;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAEA,SAAOY,OAAOA,CAACN,OAAoB,EAAE;MACnC,OAAOA,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC;IACrC;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAOS,QAAQA,CAACK,GAAW,EAAE;MAC3B,OAAOL,SAAQ,CAACK,GAAG,EAAE,IAAI,CAACC,kBAAkB,CAAC,GAAGD,GAAG,GAAG,IAAI,CAACE,aAAa;IAC1E;EAAA;AAAA,EApBiBC,eAAM;AAAA,IAAAC,gBAAA,CAAA1B,OAAA,EAAnBF,IAAI,cACU,MAAM;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EADpBF,IAAI,aAES,GAAG;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EAFhBF,IAAI,mBAGe,aAAa;AAAA,IAAA4B,gBAAA,CAAA1B,OAAA,EAHhCF,IAAI,wBAIoB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;AA4BvE,SAASmB,SAAQA,CAACK,GAAW,EAAEK,SAAmB,EAAE;EAClD,IAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EAC1CF,MAAM,CAACG,IAAI,GAAGT,GAAG;EACjB,IAAMU,QAAQ,GAAGJ,MAAM,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEL,MAAM,CAACG,IAAI,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC/D,OAAOP,SAAS,CAACO,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC", "ignoreList": []}]}