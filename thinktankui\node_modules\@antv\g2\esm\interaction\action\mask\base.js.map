{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/mask/base.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAC3C,OAAO,MAAM,MAAM,SAAS,CAAC;AAG7B;;;GAGG;AACH;IAAgC,4BAAM;IAAtC;QAAA,qEA2KC;QA1KC,UAAU;QACA,eAAS,GAAG,IAAI,CAAC;QAC3B,eAAe;QACL,YAAM,GAAG,EAAE,CAAC;QACtB,cAAc;QACJ,cAAQ,GAAG,KAAK,CAAC;QAC3B,UAAU;QACA,YAAM,GAAG,KAAK,CAAC;QACf,kBAAY,GAAG,IAAI,CAAC;QAEpB,eAAS,GAAG,MAAM,CAAC;;IAgK/B,CAAC;IA/JC,UAAU;IACA,kCAAe,GAAzB;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,OAAO;YACL,CAAC,EAAE,KAAK,CAAC,CAAC;YACV,CAAC,EAAE,KAAK,CAAC,CAAC;SACX,CAAC;IACJ,CAAC;IACD,cAAc;IACJ,4BAAS,GAAnB,UAAoB,IAAI;QACtB,IAAM,SAAS,GAAG,eAAQ,IAAI,CAAE,CAAC;QACjC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,CAAC,EAAE,KAAK,CAAC,CAAC;YACV,CAAC,EAAE,KAAK,CAAC,CAAC;SACX,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACF,6BAAU,GAAlB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC9C,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,IAAI;YACf,KAAK,aACH,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,GAAG,IACT,SAAS,CACb;SACF,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACnB,CAAC;IAID,cAAc;IACJ,8BAAW,GAArB;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,uBAAI,GAAX;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;OAEG;IACI,wBAAK,GAAZ,UAAa,GAAgC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACnC,2CAA2C;YAC3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACtC;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,SAAS,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,4BAAS,GAAhB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IAC7C,CAAC;IACD;;OAEG;IACI,uBAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnC,OAAO;SACR;QACD,IAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAM,EAAE,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAC3C,IAAM,EAAE,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAC3C,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,UAAC,KAAK;YACjB,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YACd,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAES,6BAAU,GAApB,UAAqB,SAAuB;QAC1C,IAAM,KAAK,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,0BAAO,GAAd;QACE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,sBAAG,GAAV;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SACrC;IACH,CAAC;IAED;;OAEG;IACI,uBAAI,GAAX;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SACxB;IACH,CAAC;IAED;;OAEG;IACI,yBAAM,GAAb;QACE,iBAAiB;QACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACI,0BAAO,GAAd;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;SACzB;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,iBAAM,OAAO,WAAE,CAAC;IAClB,CAAC;IACH,eAAC;AAAD,CAAC,AA3KD,CAAgC,MAAM,GA2KrC;AAED,eAAe,QAAQ,CAAC", "sourcesContent": ["import { deepMix, each } from '@antv/util';\nimport Action from '../base';\nimport { LooseObject } from '../../../interface';\n\n/**\n * @ignore\n * 辅助框 Action 的基类\n */\nabstract class MaskBase extends Action {\n  // mask 图形\n  protected maskShape = null;\n  // 组成 mask 的各个点\n  protected points = [];\n  // 开始 mask 的标记\n  protected starting = false;\n  // 开始移动的标记\n  protected moving = false;\n  protected preMovePoint = null;\n\n  protected shapeType = 'path';\n  // 获取当前的位置\n  protected getCurrentPoint() {\n    const event = this.context.event;\n    return {\n      x: event.x,\n      y: event.y,\n    };\n  }\n  // 触发 mask 的事件\n  protected emitEvent(type) {\n    const eventName = `mask:${type}`;\n    const view = this.context.view;\n    const event = this.context.event;\n    view.emit(eventName, {\n      target: this.maskShape,\n      shape: this.maskShape,\n      points: this.points,\n      x: event.x,\n      y: event.y,\n    });\n  }\n\n  // 创建 mask\n  private createMask() {\n    const view = this.context.view;\n    const maskAttrs = this.getMaskAttrs();\n    const maskShape = view.foregroundGroup.addShape({\n      type: this.shapeType,\n      name: 'mask',\n      draggable: true,\n      attrs: {\n        fill: '#C5D4EB',\n        opacity: 0.3,\n        ...maskAttrs,\n      },\n    });\n    return maskShape;\n  }\n\n  protected abstract getMaskAttrs(): LooseObject;\n\n  // 生成 mask 的路径\n  protected getMaskPath() {\n    return [];\n  }\n\n  /**\n   * 显示\n   */\n  public show() {\n    if (this.maskShape) {\n      this.maskShape.show();\n      this.emitEvent('show');\n    }\n  }\n\n  /**\n   * 开始\n   */\n  public start(arg?: { maskStyle: LooseObject }) {\n    this.starting = true;\n    // 开始时，保证移动结束\n    this.moving = false;\n    this.points = [this.getCurrentPoint()];\n    if (!this.maskShape) {\n      this.maskShape = this.createMask();\n      // 开始时设置 capture: false，可以避免创建、resize 时触发事件\n      this.maskShape.set('capture', false);\n    }\n    this.updateMask(arg?.maskStyle);\n    this.emitEvent('start');\n  }\n\n  /**\n   * 开始移动\n   */\n  public moveStart() {\n    this.moving = true;\n    this.preMovePoint = this.getCurrentPoint();\n  }\n  /**\n   * 移动 mask\n   */\n  public move() {\n    if (!this.moving || !this.maskShape) {\n      return;\n    }\n    const currentPoint = this.getCurrentPoint();\n    const preMovePoint = this.preMovePoint;\n    const dx = currentPoint.x - preMovePoint.x;\n    const dy = currentPoint.y - preMovePoint.y;\n    const points = this.points;\n    each(points, (point) => {\n      point.x += dx;\n      point.y += dy;\n    });\n    this.updateMask();\n    this.emitEvent('change');\n    this.preMovePoint = currentPoint;\n  }\n\n  protected updateMask(maskStyle?: LooseObject) {\n    const attrs = deepMix({}, this.getMaskAttrs(), maskStyle);\n    this.maskShape.attr(attrs);\n  }\n\n  /**\n   * 结束移动\n   */\n  public moveEnd() {\n    this.moving = false;\n    this.preMovePoint = null;\n  }\n\n  /**\n   * 结束\n   */\n  public end() {\n    this.starting = false;\n    this.emitEvent('end');\n    if (this.maskShape) {\n      this.maskShape.set('capture', true);\n    }\n  }\n\n  /**\n   * 隐藏\n   */\n  public hide() {\n    if (this.maskShape) {\n      this.maskShape.hide();\n      this.emitEvent('hide');\n    }\n  }\n\n  /**\n   * 大小变化\n   */\n  public resize() {\n    // 只有进行中，才会允许大小变化\n    if (this.starting && this.maskShape) {\n      this.points.push(this.getCurrentPoint());\n      this.updateMask();\n      this.emitEvent('change');\n    }\n  }\n\n  /**\n   * 销毁\n   */\n  public destroy() {\n    this.points = [];\n    if (this.maskShape) {\n      this.maskShape.remove();\n    }\n    this.maskShape = null;\n    this.preMovePoint = null;\n    super.destroy();\n  }\n}\n\nexport default MaskBase;\n"]}