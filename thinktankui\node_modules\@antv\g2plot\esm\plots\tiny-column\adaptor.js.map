{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/tiny-column/adaptor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACtF,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,sBAAsB,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,wBAAwB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAGjD,OAAO,EAAE,IAAI,EAAE,CAAC;AAEhB;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAAiC;IACzC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAA2C,OAAO,KAAlD,EAAE,KAAK,GAAoC,OAAO,MAA3C,EAAE,WAAW,GAAuB,OAAO,YAA9B,EAAE,gBAAgB,GAAK,OAAO,iBAAZ,CAAa;IAE/D,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAErC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvB,IAAM,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QAC/B,OAAO,EAAE;YACP,MAAM,EAAE,OAAO;YACf,MAAM,EAAE,OAAO;YACf,UAAU,EAAE,gBAAgB;YAC5B,QAAQ,EAAE;gBACR,KAAK,EAAE,WAAW;gBAClB,KAAK,OAAA;aACN;SACF;KACF,CAAC,CAAC;IACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEZ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpB,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;IACpC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAAiC;IACvD,OAAO,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;AACvG,CAAC", "sourcesContent": ["import { animation, annotation, pattern, theme, tooltip } from '../../adaptor/common';\nimport { interval } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, flow } from '../../utils';\nimport { meta } from '../tiny-area/adaptor';\nimport { X_FIELD, Y_FIELD } from '../tiny-line/constants';\nimport { getTinyData } from '../tiny-line/utils';\nimport { TinyColumnOptions } from './types';\n\nexport { meta };\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<TinyColumnOptions>): Params<TinyColumnOptions> {\n  const { chart, options } = params;\n  const { data, color, columnStyle, columnWidthRatio } = options;\n\n  const seriesData = getTinyData(data);\n\n  chart.data(seriesData);\n\n  const p = deepAssign({}, params, {\n    options: {\n      xField: X_FIELD,\n      yField: Y_FIELD,\n      widthRatio: columnWidthRatio,\n      interval: {\n        style: columnStyle,\n        color,\n      },\n    },\n  });\n  interval(p);\n\n  chart.axis(false);\n  chart.legend(false);\n  chart.interaction('element-active');\n  return params;\n}\n\n/**\n * 迷你柱形图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<TinyColumnOptions>) {\n  return flow(theme, pattern('columnStyle'), geometry, meta, tooltip, animation, annotation())(params);\n}\n"]}