{"version": 3, "file": "callback.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/callback.ts"], "names": [], "mappings": ";AACA,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE5B,qBAAqB;AACrB;IAA4C,kCAAM;IAAlD;;IAoBA,CAAC;IAfC;;OAEG;IACI,gCAAO,GAAd;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7B;IACH,CAAC;IACD;;OAEG;IACI,gCAAO,GAAd;QACE,iBAAM,OAAO,WAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IACH,qBAAC;AAAD,CAAC,AApBD,CAA4C,MAAM,GAoBjD", "sourcesContent": ["import { ActionCallback } from '../../interface';\nimport Action from './base';\n\n/** 回调函数构建的 Action */\nexport default class CallbackAction extends Action {\n  /**\n   * 回调函数\n   */\n  public callback: ActionCallback;\n  /**\n   * 执行\n   */\n  public execute() {\n    if (this.callback) {\n      this.callback(this.context);\n    }\n  }\n  /**\n   * 销毁\n   */\n  public destroy() {\n    super.destroy();\n    this.callback = null;\n  }\n}\n"]}