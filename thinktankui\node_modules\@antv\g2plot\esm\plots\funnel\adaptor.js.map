{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/funnel/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,YAAY,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEpF,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,sBAAsB,EAAE,MAAM,wBAAwB,CAAC;AAChE,OAAO,EAAE,mBAAmB,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AACjE,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAIxE;;;;;;;;;;;;GAYG;AACH,SAAS,cAAc,CAAC,MAA6B;IAC3C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,YAAY,GAAgD,OAAO,aAAvD,EAAE,MAAM,GAAwC,OAAO,OAA/C,EAAE,MAAM,GAAgC,OAAO,OAAvC,EAAE,MAAM,GAAwB,OAAO,OAA/B,EAAE,WAAW,GAAW,OAAO,YAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;IAC5E,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAM,aAAa,GAAG;QACpB,KAAK,EAAE,YAAY;YACjB,CAAC,CAAC;gBACE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,CAAC;gBAC3E,SAAS,EAAE,UAAC,KAAK,IAAK,OAAA,UAAG,KAAK,CAAC,MAAM,CAAC,CAAE,EAAlB,CAAkB;aACzC;YACH,CAAC,CAAC;gBACE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,mBAAmB,CAAC;gBAC7D,MAAM,EAAE,CAAC;gBACT,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,UAAC,KAAK,IAAK,OAAA,UAAG,KAAK,CAAC,MAAM,CAAC,cAAI,KAAK,CAAC,MAAM,CAAC,CAAE,EAAnC,CAAmC;aAC1D;QACL,OAAO,EAAE;YACP,KAAK,EAAE,MAAM;YACb,SAAS,EAAE,UAAC,KAAK;gBACf,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACvD,CAAC;SACF;QACD,aAAa,EAAE;YACb,+BAA+B;YAC/B,SAAS,EAAE,UAAC,KAAK;gBACf,OAAA,UAAG,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,eAAK,sBAAsB,eAC5D,KAAK,CAAC,mBAAmB,CAAsB,EAClD;YAFH,CAEG;SACN;KACF,CAAC;IAEF,QAAQ;IACR,IAAI,KAAK,CAAC;IACV,IAAI,YAAY,IAAI,WAAW,EAAE;QAC/B,KAAK,GAAG,UAAC,KAAY;YACnB,OAAO,UAAU,CACf,EAAE;YACF,YAAY;YACZ,YAAY,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAChD,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAC3D,CAAC;QACJ,CAAC,CAAC;KACH;IAED,OAAO,UAAU,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AAChH,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA6B;IACrC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,YAAY,GAAiC,OAAO,aAAxC,EAAE,aAAa,GAAkB,OAAO,cAAzB,EAAE,WAAW,GAAK,OAAO,YAAZ,CAAa;IAC7D,IAAI,WAAW,EAAE;QACf,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;KAC5B;IACD,IAAI,YAAY,EAAE;QAChB,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;KAC9B;IACD,IAAI,aAAa,EAAE;QACjB,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;KACpC;IAED,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;AAC7B,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA6B;;IACxC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA6B;IACjC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACzB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA6B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE3B,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM;QACL,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACrB,uCAAuC;KACxC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAgD,MAAiB;IAClF,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAClC,aAAa;IACL,IAAA,YAAY,GAAoB,OAAO,aAA3B,EAAE,aAAa,GAAK,OAAO,cAAZ,CAAa;IAEhD,IAAI,CAAC,YAAY,EAAE,UAAC,CAAc;QAChC,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE;YACtB,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACjC;aAAM;YACL,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;SACxC;IACH,CAAC,CAAC,CAAC;IACH,gBAAgB;IAChB,IAAI,CAAC,aAAa,EAAE;QAClB,KAAK,CAAC,WAAW,CAAC,oBAAoB,EAAE;YACtC,KAAK,EAAE,uBAAM,gBAAgB,KAAE,GAAG,EAAE,OAAO,IAAG;SAC/C,CAAC,CAAC;KACJ;SAAM;QACL,KAAK,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;KAC/C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA6B;IACnD,OAAO,IAAI,CACT,cAAc,EACd,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,KAAK,EACL,UAAU,EAAE,CACb,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { clone, each, isFunction } from '@antv/util';\nimport { animation, annotation, scale, theme, tooltip } from '../../adaptor/common';\nimport { Params } from '../../core/adaptor';\nimport { getLocale } from '../../core/locale';\nimport { Datum } from '../../types';\nimport { deepAssign, flow } from '../../utils';\nimport { conversionTagFormatter } from '../../utils/conversion';\nimport { FUNNEL_CONVERSATION, FUNNEL_PERCENT } from './constant';\nimport { basicFunnel } from './geometries/basic';\nimport { compareFunnel } from './geometries/compare';\nimport { dynamicHeightFunnel } from './geometries/dynamic-height';\nimport { facetFunnel } from './geometries/facet';\nimport { FUNNEL_LEGEND_FILTER, interactionStart } from './interactions';\nimport { FunnelOptions } from './types';\nimport type { Interaction } from './types';\n\n/**\n *\n * 各式漏斗图geometry实现细节有较大不同,\n * 1. 普通漏斗图：interval.shape('funnel')\n * 2. 对比漏斗图：分面\n * 3. 动态高度漏斗图：polypon\n * 4. 分面漏斗图：普通 + list 分面\n* /\n\n/**\n * options 处理\n * @param params\n */\nfunction defaultOptions(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { options } = params;\n  const { compareField, xField, yField, locale, funnelStyle, data } = options;\n  const i18n = getLocale(locale);\n\n  const defaultOption = {\n    label: compareField\n      ? {\n          fields: [xField, yField, compareField, FUNNEL_PERCENT, FUNNEL_CONVERSATION],\n          formatter: (datum) => `${datum[yField]}`,\n        }\n      : {\n          fields: [xField, yField, FUNNEL_PERCENT, FUNNEL_CONVERSATION],\n          offset: 0,\n          position: 'middle',\n          formatter: (datum) => `${datum[xField]} ${datum[yField]}`,\n        },\n    tooltip: {\n      title: xField,\n      formatter: (datum) => {\n        return { name: datum[xField], value: datum[yField] };\n      },\n    },\n    conversionTag: {\n      // conversionTag 的计算和显示逻辑统一保持一致\n      formatter: (datum) =>\n        `${i18n.get(['conversionTag', 'label'])}: ${conversionTagFormatter(\n          ...(datum[FUNNEL_CONVERSATION] as [number, number])\n        )}`,\n    },\n  };\n\n  // 漏斗图样式\n  let style;\n  if (compareField || funnelStyle) {\n    style = (datum: Datum) => {\n      return deepAssign(\n        {},\n        // 对比漏斗图默认描边\n        compareField && { lineWidth: 1, stroke: '#fff' },\n        isFunction(funnelStyle) ? funnelStyle(datum) : funnelStyle\n      );\n    };\n  }\n\n  return deepAssign({ options: defaultOption }, params, { options: { funnelStyle: style, data: clone(data) } });\n}\n\n/**\n * geometry处理\n * @param params\n */\nfunction geometry(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { options } = params;\n  const { compareField, dynamicHeight, seriesField } = options;\n  if (seriesField) {\n    return facetFunnel(params);\n  }\n  if (compareField) {\n    return compareFunnel(params);\n  }\n  if (dynamicHeight) {\n    return dynamicHeightFunnel(params);\n  }\n\n  return basicFunnel(params);\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(params);\n}\n\n/**\n * 坐标轴\n * @param params\n */\nfunction axis(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { chart } = params;\n  chart.axis(false);\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nfunction legend(params: Params<FunnelOptions>): Params<FunnelOptions> {\n  const { chart, options } = params;\n  const { legend } = options;\n\n  if (legend === false) {\n    chart.legend(false);\n  } else {\n    chart.legend(legend);\n    // TODO FIX: legend-click 时间和转化率组件之间的关联\n  }\n\n  return params;\n}\n\n/**\n * Interaction 配置\n * @param params\n */\nexport function interaction<O extends Pick<FunnelOptions, 'interactions'>>(params: Params<O>): Params<O> {\n  const { chart, options } = params;\n  // @ts-ignore\n  const { interactions, dynamicHeight } = options;\n\n  each(interactions, (i: Interaction) => {\n    if (i.enable === false) {\n      chart.removeInteraction(i.type);\n    } else {\n      chart.interaction(i.type, i.cfg || {});\n    }\n  });\n  // 动态高度  不进行交互操作\n  if (!dynamicHeight) {\n    chart.interaction(FUNNEL_LEGEND_FILTER, {\n      start: [{ ...interactionStart, arg: options }],\n    });\n  } else {\n    chart.removeInteraction(FUNNEL_LEGEND_FILTER);\n  }\n\n  return params;\n}\n\n/**\n * 漏斗图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<FunnelOptions>) {\n  return flow(\n    defaultOptions,\n    geometry,\n    meta,\n    axis,\n    tooltip,\n    interaction,\n    legend,\n    animation,\n    theme,\n    annotation()\n  )(params);\n}\n"]}