{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\setPrototypeOf.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\@babel\\runtime\\helpers\\setPrototypeOf.js", "mtime": 1749109535274}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5wcm90by5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnNldC1wcm90b3R5cGUtb2YuanMiKTsKZnVuY3Rpb24gX3NldFByb3RvdHlwZU9mKHQsIGUpIHsKICByZXR1cm4gbW9kdWxlLmV4cG9ydHMgPSBfc2V0UHJvdG90eXBlT2YgPSBPYmplY3Quc2V0UHJvdG90eXBlT2YgPyBPYmplY3Quc2V0UHJvdG90eXBlT2YuYmluZCgpIDogZnVuY3Rpb24gKHQsIGUpIHsKICAgIHJldHVybiB0Ll9fcHJvdG9fXyA9IGUsIHQ7CiAgfSwgbW9kdWxlLmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWUsIG1vZHVsZS5leHBvcnRzWyJkZWZhdWx0Il0gPSBtb2R1bGUuZXhwb3J0cywgX3NldFByb3RvdHlwZU9mKHQsIGUpOwp9Cm1vZHVsZS5leHBvcnRzID0gX3NldFByb3RvdHlwZU9mLCBtb2R1bGUuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZSwgbW9kdWxlLmV4cG9ydHNbImRlZmF1bHQiXSA9IG1vZHVsZS5leHBvcnRzOw=="}, {"version": 3, "names": ["_setPrototypeOf", "t", "e", "module", "exports", "Object", "setPrototypeOf", "bind", "__proto__", "__esModule"], "sources": ["D:/thinktank/thinktankui/node_modules/@babel/runtime/helpers/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _setPrototypeOf(t, e);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "mappings": ";;AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B,OAAOC,MAAM,CAACC,OAAO,GAAGJ,eAAe,GAAGK,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACC,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUN,CAAC,EAAEC,CAAC,EAAE;IAC/G,OAAOD,CAAC,CAACO,SAAS,GAAGN,CAAC,EAAED,CAAC;EAC3B,CAAC,EAAEE,MAAM,CAACC,OAAO,CAACK,UAAU,GAAG,IAAI,EAAEN,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO,EAAEJ,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AACxG;AACAC,MAAM,CAACC,OAAO,GAAGJ,eAAe,EAAEG,MAAM,CAACC,OAAO,CAACK,UAAU,GAAG,IAAI,EAAEN,MAAM,CAACC,OAAO,CAAC,SAAS,CAAC,GAAGD,MAAM,CAACC,OAAO", "ignoreList": []}]}