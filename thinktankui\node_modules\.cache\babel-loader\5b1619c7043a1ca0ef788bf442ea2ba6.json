{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\video.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\node_modules\\quill\\formats\\video.js", "mtime": 1749109533921}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_block", "require", "_link", "_interopRequireDefault", "ATTRIBUTES", "Video", "_BlockEmbed", "_classCallCheck2", "default", "_callSuper2", "arguments", "_inherits2", "_createClass2", "key", "value", "format", "name", "indexOf", "domNode", "setAttribute", "removeAttribute", "_superPropGet2", "html", "_this$value", "video", "concat", "create", "node", "sanitize", "formats", "reduce", "attribute", "hasAttribute", "getAttribute", "url", "Link", "BlockEmbed", "_defineProperty2", "_default", "exports"], "sources": ["../../src/formats/video.ts"], "sourcesContent": ["import { BlockEmbed } from '../blots/block.js';\nimport Link from './link.js';\n\nconst ATTRIBUTES = ['height', 'width'];\n\nclass Video extends BlockEmbed {\n  static blotName = 'video';\n  static className = 'ql-video';\n  static tagName = 'IFRAME';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    node.setAttribute('frameborder', '0');\n    node.setAttribute('allowfullscreen', 'true');\n    node.setAttribute('src', this.sanitize(value));\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static sanitize(url: string) {\n    return Link.sanitize(url);\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLVideoElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  html() {\n    const { video } = this.value();\n    return `<a href=\"${video}\">${video}</a>`;\n  }\n}\n\nexport default Video;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAMG,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;AAAA,IAEhCC,KAAK,0BAAAC,WAAA;EAAA,SAAAD,MAAA;IAAA,IAAAE,gBAAA,CAAAC,OAAA,QAAAH,KAAA;IAAA,WAAAI,WAAA,CAAAD,OAAA,QAAAH,KAAA,EAAAK,SAAA;EAAA;EAAA,IAAAC,UAAA,CAAAH,OAAA,EAAAH,KAAA,EAAAC,WAAA;EAAA,WAAAM,aAAA,CAAAJ,OAAA,EAAAH,KAAA;IAAAQ,GAAA;IAAAC,KAAA,EAmCT,SAAAC,MAAMA,CAACC,IAAY,EAAEF,KAAa,EAAE;MAClC,IAAIV,UAAU,CAACa,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QACjC,IAAIF,KAAK,EAAE;UACT,IAAI,CAACI,OAAO,CAACC,YAAY,CAACH,IAAI,EAAEF,KAAK,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAACI,OAAO,CAACE,eAAe,CAACJ,IAAI,CAAC;QACpC;MACF,CAAC,MAAM;QACL,IAAAK,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAaW,IAAI,EAAEF,KAAK;MAC1B;IACF;EAAA;IAAAD,GAAA;IAAAC,KAAA,EAEA,SAAAQ,IAAIA,CAAA,EAAG;MACL,IAAAC,WAAA,GAAkB,IAAI,CAACT,KAAK,CAAC,CAAC;QAAtBU,KAAA,GAAAD,WAAA,CAAAC,KAAA;MACR,oBAAAC,MAAA,CAAmBD,KAAM,SAAAC,MAAA,CAAID,KAAM;IACrC;EAAA;IAAAX,GAAA;IAAAC,KAAA,EA7CA,SAAOY,MAAMA,CAACZ,KAAa,EAAE;MAC3B,IAAMa,IAAI,OAAAN,cAAA,CAAAb,OAAA,EAAAH,KAAA,sBAAgBS,KAAK,EAAY;MAC3Ca,IAAI,CAACR,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;MACrCQ,IAAI,CAACR,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;MAC5CQ,IAAI,CAACR,YAAY,CAAC,KAAK,EAAE,IAAI,CAACS,QAAQ,CAACd,KAAK,CAAC,CAAC;MAC9C,OAAOa,IAAI;IACb;EAAA;IAAAd,GAAA;IAAAC,KAAA,EAEA,SAAOe,OAAOA,CAACX,OAAgB,EAAE;MAC/B,OAAOd,UAAU,CAAC0B,MAAM,CACtB,UAACD,OAAsC,EAAEE,SAAS,EAAK;QACrD,IAAIb,OAAO,CAACc,YAAY,CAACD,SAAS,CAAC,EAAE;UACnCF,OAAO,CAACE,SAAS,CAAC,GAAGb,OAAO,CAACe,YAAY,CAACF,SAAS,CAAC;QACtD;QACA,OAAOF,OAAO;MAChB,CAAC,EACD,CAAC,CACH,CAAC;IACH;EAAA;IAAAhB,GAAA;IAAAC,KAAA,EAEA,SAAOc,QAAQA,CAACM,GAAW,EAAE;MAC3B,OAAOC,aAAI,CAACP,QAAQ,CAACM,GAAG,CAAC;IAC3B;EAAA;IAAArB,GAAA;IAAAC,KAAA,EAEA,SAAOA,KAAKA,CAACI,OAAgB,EAAE;MAC7B,OAAOA,OAAO,CAACe,YAAY,CAAC,KAAK,CAAC;IACpC;EAAA;AAAA,EA/BkBG,iBAAU;AAAA,IAAAC,gBAAA,CAAA7B,OAAA,EAAxBH,KAAK,cACS,OAAO;AAAA,IAAAgC,gBAAA,CAAA7B,OAAA,EADrBH,KAAK,eAEU,UAAU;AAAA,IAAAgC,gBAAA,CAAA7B,OAAA,EAFzBH,KAAK,aAGQ,QAAQ;AAAA,IAAAiC,QAAA,GAAAC,OAAA,CAAA/B,OAAA,GAkDZH,KAAK", "ignoreList": []}]}