{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../src/interaction/action/base.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AAGpC;;GAEG;AACH;IAUE,gBAAY,OAA4B,EAAE,GAAO;QAC/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACO,yBAAQ,GAAlB,UAAmB,GAAG;QACpB,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,qBAAI,GAAX;QACE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,wBAAO,GAAd;QACE,YAAY;QACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAChC,KAAK;QACL,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IACH,aAAC;AAAD,CAAC,AAxCD,IAwCC;AAED,eAAe,MAAM,CAAC", "sourcesContent": ["import { assign } from '@antv/util';\nimport { IAction, IInteractionContext, LooseObject } from '../../interface';\n\n/**\n * Action 的基类\n */\nabstract class Action<T = LooseObject> implements IAction {\n  /** Action 名字 */\n  public name;\n  /** 上下文对象 */\n  public context: IInteractionContext;\n  /** Action 配置 */\n  protected cfg: T;\n  /** 配置项的字段，自动负值到 this 上 */\n  protected cfgFields: string[];\n\n  constructor(context: IInteractionContext, cfg?: T) {\n    this.context = context;\n    this.cfg = cfg;\n    context.addAction(this);\n  }\n\n  /**\n   * 设置配置项传入的值\n   * @param cfg\n   */\n  protected applyCfg(cfg) {\n    assign(this, cfg);\n  }\n\n  /**\n   * Inits action，提供给子类用于继承\n   */\n  public init() {\n    this.applyCfg(this.cfg);\n  }\n\n  /**\n   * Destroys action\n   */\n  public destroy() {\n    // 移除 action\n    this.context.removeAction(this);\n    // 清空\n    this.context = null;\n  }\n}\n\nexport default Action;\n"]}