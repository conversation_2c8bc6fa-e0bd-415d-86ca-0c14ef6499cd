{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\resetPwd.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\user\\profile\\resetPwd.vue", "mtime": 1749109381355}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["resetPwd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "resetPwd.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <el-form ref=\"form\" :model=\"user\" :rules=\"rules\" label-width=\"80px\">\r\n    <el-form-item label=\"旧密码\" prop=\"oldPassword\">\r\n      <el-input v-model=\"user.oldPassword\" placeholder=\"请输入旧密码\" type=\"password\" show-password/>\r\n    </el-form-item>\r\n    <el-form-item label=\"新密码\" prop=\"newPassword\">\r\n      <el-input v-model=\"user.newPassword\" placeholder=\"请输入新密码\" type=\"password\" show-password/>\r\n    </el-form-item>\r\n    <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\r\n      <el-input v-model=\"user.confirmPassword\" placeholder=\"请确认新密码\" type=\"password\" show-password/>\r\n    </el-form-item>\r\n    <el-form-item>\r\n      <el-button type=\"primary\" size=\"mini\" @click=\"submit\">保存</el-button>\r\n      <el-button type=\"danger\" size=\"mini\" @click=\"close\">关闭</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserPwd } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.user.newPassword !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      user: {\r\n        oldPassword: undefined,\r\n        newPassword: undefined,\r\n        confirmPassword: undefined\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        oldPassword: [\r\n          { required: true, message: \"旧密码不能为空\", trigger: \"blur\" }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: \"新密码不能为空\", trigger: \"blur\" },\r\n          { min: 6, max: 20, message: \"长度在 6 到 20 个字符\", trigger: \"blur\" },\r\n          { pattern: /^[^<>\"'|\\\\]+$/, message: \"不能包含非法字符：< > \\\" ' \\\\\\ |\", trigger: \"blur\" }\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, message: \"确认密码不能为空\", trigger: \"blur\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  methods: {\r\n    submit() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n          });\r\n        }\r\n      });\r\n    },\r\n    close() {\r\n      this.$tab.closePage();\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}