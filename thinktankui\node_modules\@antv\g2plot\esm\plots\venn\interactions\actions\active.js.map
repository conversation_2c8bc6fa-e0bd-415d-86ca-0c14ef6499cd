{"version": 3, "file": "active.js", "sourceRoot": "", "sources": ["../../../../../src/plots/venn/interactions/actions/active.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAC1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,SAAS,CAAC;AAE/C,IAAM,mBAAmB,GAAQ,cAAc,CAAC,gBAAgB,CAAC,CAAC;AAElE;IAAuC,qCAAmB;IAA1D;;IAyBA,CAAC;IAxBC;;OAEG;IACO,2CAAe,GAAzB;QACE,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,aAAa;IACN,kCAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,sBAAsB;IACf,kCAAM,GAAb;QACE,iBAAM,MAAM,WAAE,CAAC;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,SAAS;IACF,iCAAK,GAAZ;QACE,iBAAM,KAAK,WAAE,CAAC;QACd,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IACH,wBAAC;AAAD,CAAC,AAzBD,CAAuC,mBAAmB,GAyBzD", "sourcesContent": ["import { getActionClass } from '@antv/g2';\nimport { placeElementsOrdered } from '../util';\n\nconst ElementActiveAction: any = getActionClass('element-active');\n\nexport class VennElementActive extends ElementActiveAction {\n  /**\n   * 同步所有元素的位置\n   */\n  protected syncElementsPos() {\n    placeElementsOrdered(this.context.view);\n  }\n\n  /** 激活图形元素 */\n  public active() {\n    super.active();\n    this.syncElementsPos();\n  }\n\n  /** toggle 图形元素激活状态 */\n  public toggle() {\n    super.toggle();\n    this.syncElementsPos();\n  }\n\n  /** 重置 */\n  public reset() {\n    super.reset();\n    this.syncElementsPos();\n  }\n}\n"]}