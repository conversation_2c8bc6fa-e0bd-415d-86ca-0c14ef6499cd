{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\editTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\editTable.vue", "mtime": 1749109381357}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRHZW5UYWJsZSwgdXBkYXRlR2VuVGFibGUgfSBmcm9tICJAL2FwaS90b29sL2dlbiI7DQppbXBvcnQgeyBvcHRpb25zZWxlY3QgYXMgZ2V0RGljdE9wdGlvbnNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L3R5cGUiOw0KaW1wb3J0IHsgbGlzdE1lbnUgYXMgZ2V0TWVudVRyZWVzZWxlY3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vbWVudSI7DQppbXBvcnQgYmFzaWNJbmZvRm9ybSBmcm9tICIuL2Jhc2ljSW5mb0Zvcm0iOw0KaW1wb3J0IGdlbkluZm9Gb3JtIGZyb20gIi4vZ2VuSW5mb0Zvcm0iOw0KaW1wb3J0IFNvcnRhYmxlIGZyb20gJ3NvcnRhYmxlanMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkdlbkVkaXQiLA0KICBjb21wb25lbnRzOiB7DQogICAgYmFzaWNJbmZvRm9ybSwNCiAgICBnZW5JbmZvRm9ybQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpgInkuK3pgInpobnljaHnmoQgbmFtZQ0KICAgICAgYWN0aXZlTmFtZTogImNvbHVtbkluZm8iLA0KICAgICAgLy8g6KGo5qC855qE6auY5bqmDQogICAgICB0YWJsZUhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbEhlaWdodCAtIDI0NSArICJweCIsDQogICAgICAvLyDooajkv6Hmga8NCiAgICAgIHRhYmxlczogW10sDQogICAgICAvLyDooajliJfkv6Hmga8NCiAgICAgIGNvbHVtbnM6IFtdLA0KICAgICAgLy8g5a2X5YW45L+h5oGvDQogICAgICBkaWN0T3B0aW9uczogW10sDQogICAgICAvLyDoj5zljZXkv6Hmga8NCiAgICAgIG1lbnVzOiBbXSwNCiAgICAgIC8vIOihqOivpue7huS/oeaBrw0KICAgICAgaW5mbzoge30NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIGNvbnN0IHRhYmxlSWQgPSB0aGlzLiRyb3V0ZS5wYXJhbXMgJiYgdGhpcy4kcm91dGUucGFyYW1zLnRhYmxlSWQ7DQogICAgaWYgKHRhYmxlSWQpIHsNCiAgICAgIC8vIOiOt+WPluihqOivpue7huS/oeaBrw0KICAgICAgZ2V0R2VuVGFibGUodGFibGVJZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmNvbHVtbnMgPSByZXMuZGF0YS5yb3dzOw0KICAgICAgICB0aGlzLmluZm8gPSByZXMuZGF0YS5pbmZvOw0KICAgICAgICB0aGlzLnRhYmxlcyA9IHJlcy5kYXRhLnRhYmxlczsNCiAgICAgIH0pOw0KICAgICAgLyoqIOafpeivouWtl+WFuOS4i+aLieWIl+ihqCAqLw0KICAgICAgZ2V0RGljdE9wdGlvbnNlbGVjdCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRpY3RPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pOw0KICAgICAgLyoqIOafpeivouiPnOWNleS4i+aLieWIl+ihqCAqLw0KICAgICAgZ2V0TWVudVRyZWVzZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5tZW51cyA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAibWVudUlkIik7DQogICAgICB9KTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIGNvbnN0IGJhc2ljRm9ybSA9IHRoaXMuJHJlZnMuYmFzaWNJbmZvLiRyZWZzLmJhc2ljSW5mb0Zvcm07DQogICAgICBjb25zdCBnZW5Gb3JtID0gdGhpcy4kcmVmcy5nZW5JbmZvLiRyZWZzLmdlbkluZm9Gb3JtOw0KICAgICAgUHJvbWlzZS5hbGwoW2Jhc2ljRm9ybSwgZ2VuRm9ybV0ubWFwKHRoaXMuZ2V0Rm9ybVByb21pc2UpKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGNvbnN0IHZhbGlkYXRlUmVzdWx0ID0gcmVzLmV2ZXJ5KGl0ZW0gPT4gISFpdGVtKTsNCiAgICAgICAgaWYgKHZhbGlkYXRlUmVzdWx0KSB7DQogICAgICAgICAgY29uc3QgZ2VuVGFibGUgPSBPYmplY3QuYXNzaWduKHt9LCBiYXNpY0Zvcm0ubW9kZWwsIGdlbkZvcm0ubW9kZWwpOw0KICAgICAgICAgIGdlblRhYmxlLmNvbHVtbnMgPSB0aGlzLmNvbHVtbnM7DQogICAgICAgICAgZ2VuVGFibGUucGFyYW1zID0gew0KICAgICAgICAgICAgdHJlZUNvZGU6IGdlblRhYmxlLnRyZWVDb2RlLA0KICAgICAgICAgICAgdHJlZU5hbWU6IGdlblRhYmxlLnRyZWVOYW1lLA0KICAgICAgICAgICAgdHJlZVBhcmVudENvZGU6IGdlblRhYmxlLnRyZWVQYXJlbnRDb2RlLA0KICAgICAgICAgICAgcGFyZW50TWVudUlkOiBnZW5UYWJsZS5wYXJlbnRNZW51SWQNCiAgICAgICAgICB9Ow0KICAgICAgICAgIHVwZGF0ZUdlblRhYmxlKGdlblRhYmxlKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHJlcy5tc2cpOw0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy5jbG9zZSgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLooajljZXmoKHpqozmnKrpgJrov4fvvIzor7fph43mlrDmo4Dmn6Xmj5DkuqTlhoXlrrkiKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRGb3JtUHJvbWlzZShmb3JtKSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UocmVzb2x2ZSA9PiB7DQogICAgICAgIGZvcm0udmFsaWRhdGUocmVzID0+IHsNCiAgICAgICAgICByZXNvbHZlKHJlcyk7DQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5YWz6Zet5oyJ6ZKuICovDQogICAgY2xvc2UoKSB7DQogICAgICBjb25zdCBvYmogPSB7IHBhdGg6ICIvdG9vbC9nZW4iLCBxdWVyeTogeyB0OiBEYXRlLm5vdygpLCBwYWdlTnVtOiB0aGlzLiRyb3V0ZS5xdWVyeS5wYWdlTnVtIH0gfTsNCiAgICAgIHRoaXMuJHRhYi5jbG9zZU9wZW5QYWdlKG9iaik7DQogICAgfQ0KICB9LA0KICBtb3VudGVkKCkgew0KICAgIGNvbnN0IGVsID0gdGhpcy4kcmVmcy5kcmFnVGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoIi5lbC10YWJsZV9fYm9keS13cmFwcGVyID4gdGFibGUgPiB0Ym9keSIpWzBdOw0KICAgIGNvbnN0IHNvcnRhYmxlID0gU29ydGFibGUuY3JlYXRlKGVsLCB7DQogICAgICBoYW5kbGU6ICIuYWxsb3dEcmFnIiwNCiAgICAgIG9uRW5kOiBldnQgPT4gew0KICAgICAgICBjb25zdCB0YXJnZXRSb3cgPSB0aGlzLmNvbHVtbnMuc3BsaWNlKGV2dC5vbGRJbmRleCwgMSlbMF07DQogICAgICAgIHRoaXMuY29sdW1ucy5zcGxpY2UoZXZ0Lm5ld0luZGV4LCAwLCB0YXJnZXRSb3cpOw0KICAgICAgICBmb3IgKGxldCBpbmRleCBpbiB0aGlzLmNvbHVtbnMpIHsNCiAgICAgICAgICB0aGlzLmNvbHVtbnNbaW5kZXhdLnNvcnQgPSBwYXJzZUludChpbmRleCkgKyAxOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSk7DQogIH0NCn07DQo="}, {"version": 3, "sources": ["editTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editTable.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\r\n  <el-card>\r\n    <el-tabs v-model=\"activeName\">\r\n      <el-tab-pane label=\"基本信息\" name=\"basic\">\r\n        <basic-info-form ref=\"basicInfo\" :info=\"info\" />\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"字段信息\" name=\"columnInfo\">\r\n        <el-table ref=\"dragTable\" :data=\"columns\" row-key=\"columnId\" :max-height=\"tableHeight\">\r\n          <el-table-column label=\"序号\" type=\"index\" min-width=\"5%\" class-name=\"allowDrag\" />\r\n          <el-table-column\r\n            label=\"字段列名\"\r\n            prop=\"columnName\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"字段描述\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.columnComment\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"物理类型\"\r\n            prop=\"columnType\"\r\n            min-width=\"10%\"\r\n            :show-overflow-tooltip=\"true\"\r\n          />\r\n          <el-table-column label=\"Python类型\" min-width=\"11%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.pythonType\">\r\n                <el-option label=\"str\" value=\"str\" />\r\n                <el-option label=\"int\" value=\"int\" />\r\n                <el-option label=\"float\" value=\"float\" />\r\n                <el-option label=\"Decimal\" value=\"Decimal\" />\r\n                <el-option label=\"date\" value=\"date\" />\r\n                <el-option label=\"time\" value=\"time\" />\r\n                <el-option label=\"datetime\" value=\"datetime\" />\r\n                <el-option label=\"bytes\" value=\"bytes\" />\r\n                <el-option label=\"dict\" value=\"dict\" />\r\n                <el-option label=\"list\" value=\"list\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"Python属性\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input v-model=\"scope.row.pythonField\"></el-input>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column label=\"插入\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isInsert\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"编辑\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isEdit\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"列表\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isList\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isQuery\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"查询方式\" min-width=\"10%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.queryType\">\r\n                <el-option label=\"=\" value=\"EQ\" />\r\n                <el-option label=\"!=\" value=\"NE\" />\r\n                <el-option label=\">\" value=\"GT\" />\r\n                <el-option label=\">=\" value=\"GTE\" />\r\n                <el-option label=\"<\" value=\"LT\" />\r\n                <el-option label=\"<=\" value=\"LTE\" />\r\n                <el-option label=\"LIKE\" value=\"LIKE\" />\r\n                <el-option label=\"BETWEEN\" value=\"BETWEEN\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"必填\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isRequired\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"唯一\" min-width=\"5%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-checkbox true-label=\"1\" false-label=\"0\" v-model=\"scope.row.isUnique\"></el-checkbox>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.htmlType\">\r\n                <el-option label=\"文本框\" value=\"input\" />\r\n                <el-option label=\"文本域\" value=\"textarea\" />\r\n                <el-option label=\"下拉框\" value=\"select\" />\r\n                <el-option label=\"单选框\" value=\"radio\" />\r\n                <el-option label=\"复选框\" value=\"checkbox\" />\r\n                <el-option label=\"日期控件\" value=\"datetime\" />\r\n                <el-option label=\"图片上传\" value=\"imageUpload\" />\r\n                <el-option label=\"文件上传\" value=\"fileUpload\" />\r\n                <el-option label=\"富文本控件\" value=\"editor\" />\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"字典类型\" min-width=\"12%\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.dictType\" clearable filterable placeholder=\"请选择\">\r\n                <el-option\r\n                  v-for=\"dict in dictOptions\"\r\n                  :key=\"dict.dictType\"\r\n                  :label=\"dict.dictName\"\r\n                  :value=\"dict.dictType\">\r\n                  <span style=\"float: left\">{{ dict.dictName }}</span>\r\n                  <span style=\"float: right; color: #8492a6; font-size: 13px\">{{ dict.dictType }}</span>\r\n              </el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-tab-pane>\r\n      <el-tab-pane label=\"生成信息\" name=\"genInfo\">\r\n        <gen-info-form ref=\"genInfo\" :info=\"info\" :tables=\"tables\" :menus=\"menus\"/>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-100px;margin-top:10px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport { getGenTable, updateGenTable } from \"@/api/tool/gen\";\r\nimport { optionselect as getDictOptionselect } from \"@/api/system/dict/type\";\r\nimport { listMenu as getMenuTreeselect } from \"@/api/system/menu\";\r\nimport basicInfoForm from \"./basicInfoForm\";\r\nimport genInfoForm from \"./genInfoForm\";\r\nimport Sortable from 'sortablejs'\r\n\r\nexport default {\r\n  name: \"GenEdit\",\r\n  components: {\r\n    basicInfoForm,\r\n    genInfoForm\r\n  },\r\n  data() {\r\n    return {\r\n      // 选中选项卡的 name\r\n      activeName: \"columnInfo\",\r\n      // 表格的高度\r\n      tableHeight: document.documentElement.scrollHeight - 245 + \"px\",\r\n      // 表信息\r\n      tables: [],\r\n      // 表列信息\r\n      columns: [],\r\n      // 字典信息\r\n      dictOptions: [],\r\n      // 菜单信息\r\n      menus: [],\r\n      // 表详细信息\r\n      info: {}\r\n    };\r\n  },\r\n  created() {\r\n    const tableId = this.$route.params && this.$route.params.tableId;\r\n    if (tableId) {\r\n      // 获取表详细信息\r\n      getGenTable(tableId).then(res => {\r\n        this.columns = res.data.rows;\r\n        this.info = res.data.info;\r\n        this.tables = res.data.tables;\r\n      });\r\n      /** 查询字典下拉列表 */\r\n      getDictOptionselect().then(response => {\r\n        this.dictOptions = response.data;\r\n      });\r\n      /** 查询菜单下拉列表 */\r\n      getMenuTreeselect().then(response => {\r\n        this.menus = this.handleTree(response.data, \"menuId\");\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;\r\n      const genForm = this.$refs.genInfo.$refs.genInfoForm;\r\n      Promise.all([basicForm, genForm].map(this.getFormPromise)).then(res => {\r\n        const validateResult = res.every(item => !!item);\r\n        if (validateResult) {\r\n          const genTable = Object.assign({}, basicForm.model, genForm.model);\r\n          genTable.columns = this.columns;\r\n          genTable.params = {\r\n            treeCode: genTable.treeCode,\r\n            treeName: genTable.treeName,\r\n            treeParentCode: genTable.treeParentCode,\r\n            parentMenuId: genTable.parentMenuId\r\n          };\r\n          updateGenTable(genTable).then(res => {\r\n            this.$modal.msgSuccess(res.msg);\r\n            if (res.code === 200) {\r\n              this.close();\r\n            }\r\n          });\r\n        } else {\r\n          this.$modal.msgError(\"表单校验未通过，请重新检查提交内容\");\r\n        }\r\n      });\r\n    },\r\n    getFormPromise(form) {\r\n      return new Promise(resolve => {\r\n        form.validate(res => {\r\n          resolve(res);\r\n        });\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/tool/gen\", query: { t: Date.now(), pageNum: this.$route.query.pageNum } };\r\n      this.$tab.closeOpenPage(obj);\r\n    }\r\n  },\r\n  mounted() {\r\n    const el = this.$refs.dragTable.$el.querySelectorAll(\".el-table__body-wrapper > table > tbody\")[0];\r\n    const sortable = Sortable.create(el, {\r\n      handle: \".allowDrag\",\r\n      onEnd: evt => {\r\n        const targetRow = this.columns.splice(evt.oldIndex, 1)[0];\r\n        this.columns.splice(evt.newIndex, 0, targetRow);\r\n        for (let index in this.columns) {\r\n          this.columns[index].sort = parseInt(index) + 1;\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n</script>\r\n"]}]}