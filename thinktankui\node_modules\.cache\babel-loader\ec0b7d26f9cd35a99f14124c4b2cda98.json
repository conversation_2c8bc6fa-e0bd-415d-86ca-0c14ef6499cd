{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\thinktank\\thinktankui\\src\\layout\\mixin\\ResizeHandler.js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\mixin\\ResizeHandler.js", "mtime": 1749109381333}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1749109530500}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_document", "document", "body", "WIDTH", "_default", "exports", "default", "watch", "$route", "route", "device", "sidebar", "opened", "store", "dispatch", "withoutAnimation", "beforeMount", "window", "addEventListener", "$_resizeHandler", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "isMobile", "$_isMobile", "methods", "rect", "getBoundingClientRect", "width", "hidden"], "sources": ["D:/thinktank/thinktankui/src/layout/mixin/ResizeHandler.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nconst { body } = document\r\nconst WIDTH = 992 // refer to Bootstrap's responsive design\r\n\r\nexport default {\r\n  watch: {\r\n    $route(route) {\r\n      if (this.device === 'mobile' && this.sidebar.opened) {\r\n        store.dispatch('app/closeSideBar', { withoutAnimation: false })\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  mounted() {\r\n    const isMobile = this.$_isMobile()\r\n    if (isMobile) {\r\n      store.dispatch('app/toggleDevice', 'mobile')\r\n      store.dispatch('app/closeSideBar', { withoutAnimation: true })\r\n    }\r\n  },\r\n  methods: {\r\n    // use $_ for mixins properties\r\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\r\n    $_isMobile() {\r\n      const rect = body.getBoundingClientRect()\r\n      return rect.width - 1 < WIDTH\r\n    },\r\n    $_resizeHandler() {\r\n      if (!document.hidden) {\r\n        const isMobile = this.$_isMobile()\r\n        store.dispatch('app/toggleDevice', isMobile ? 'mobile' : 'desktop')\r\n\r\n        if (isMobile) {\r\n          store.dispatch('app/closeSideBar', { withoutAnimation: true })\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAiBC,QAAQ;EAAjBC,IAAI,GAAAF,SAAA,CAAJE,IAAI;AACZ,IAAMC,KAAK,GAAG,GAAG,EAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEH;EACbC,KAAK,EAAE;IACLC,MAAM,WAANA,MAAMA,CAACC,KAAK,EAAE;MACZ,IAAI,IAAI,CAACC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAE;QACnDC,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;UAAEC,gBAAgB,EAAE;QAAM,CAAC,CAAC;MACjE;IACF;EACF,CAAC;EACDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC;EACzD,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;EAC5D,CAAC;EACDG,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAClC,IAAID,QAAQ,EAAE;MACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC5CD,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QAAEC,gBAAgB,EAAE;MAAK,CAAC,CAAC;IAChE;EACF,CAAC;EACDU,OAAO,EAAE;IACP;IACA;IACAD,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAME,IAAI,GAAGxB,IAAI,CAACyB,qBAAqB,CAAC,CAAC;MACzC,OAAOD,IAAI,CAACE,KAAK,GAAG,CAAC,GAAGzB,KAAK;IAC/B,CAAC;IACDgB,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAClB,QAAQ,CAAC4B,MAAM,EAAE;QACpB,IAAMN,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAClCX,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAES,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QAEnE,IAAIA,QAAQ,EAAE;UACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;YAAEC,gBAAgB,EAAE;UAAK,CAAC,CAAC;QAChE;MACF;IACF;EACF;AACF,CAAC", "ignoreList": []}]}