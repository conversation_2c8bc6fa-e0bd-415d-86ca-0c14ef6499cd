{"version": 3, "file": "button.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/button.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,IAAM,aAAa,GAAG,EAAE,CAAC;AACzB,IAAM,WAAW,GAAG,CAAC,CAAC;AAEtB;;;GAGG;AACH;IAA2B,gCAAM;IAAjC;QAAA,qEA6GC;QA5GS,iBAAW,GAAW,IAAI,CAAC;QAC3B,eAAS,GAAG;YAClB,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE;gBACT,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;aAClB;YACD,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;YAChB,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE,SAAS;aAClB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;aAChB;SACF,CAAC;;IAwFJ,CAAC;IAvFC,iBAAiB;IACT,mCAAY,GAApB;QACE,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IACD,iBAAiB;IACT,iCAAU,GAAlB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACnC,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;YACvD,IAAI,EAAE,MAAM,CAAC,IAAI;SAClB,CAAC,CAAC;QACH,OAAO;QACP,IAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC;YAC/B,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,aAAa;YACnB,KAAK,aACH,IAAI,EAAE,MAAM,CAAC,IAAI,IACd,MAAM,CAAC,SAAS,CACpB;SACF,CAAC,CAAC;QACH,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QACrC,IAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC7C,SAAS;QACT,IAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC;YACjC,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,aAAa;YACnB,KAAK,aACH,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC1B,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC1B,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,EAC/C,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,IAC9C,MAAM,CAAC,KAAK,CAChB;SACF,CAAC,CAAC;QACH,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM;QAC5B,cAAc;QACd,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE;YACrB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE;YACrB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,OAAO;IACC,oCAAa,GAArB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACnC,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY;QACzD,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,IAAI,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;QACnC,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;YACjC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,aAAa,EAAE,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC;SACjF,CAAC,CAAC;QACH,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,2BAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,UAAU,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,2BAAI,GAAX;QACE,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;SACzB;IACH,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,MAAM,EAAE,CAAC;SACtB;QACD,iBAAM,OAAO,WAAE,CAAC;IAClB,CAAC;IACH,mBAAC;AAAD,CAAC,AA7GD,CAA2B,MAAM,GA6GhC;AAED,eAAe,YAAY,CAAC", "sourcesContent": ["import { LooseObject } from '../../../interface';\nimport { IGroup } from '@antv/g-base';\nimport { ext } from '@antv/matrix-util';\nimport { deepMix } from '@antv/util';\nimport { parsePadding } from '../../../util/padding';\nimport Action from '../base';\nconst PADDING_RIGHT = 10;\nconst PADDING_TOP = 5;\n\n/**\n * Button action\n * @ignore\n */\nclass ButtonAction extends Action {\n  private buttonGroup: IGroup = null;\n  private buttonCfg = {\n    name: 'button',\n    text: 'button',\n    textStyle: {\n      x: 0,\n      y: 0,\n      fontSize: 12,\n      fill: '#333333',\n      cursor: 'pointer',\n    },\n    padding: [8, 10],\n    style: {\n      fill: '#f7f7f7',\n      stroke: '#cccccc',\n      cursor: 'pointer',\n    },\n    activeStyle: {\n      fill: '#e6e6e6',\n    },\n  };\n  // mix 默认的配置和用户配置\n  private getButtonCfg(): LooseObject {\n    return deepMix(this.buttonCfg, this.cfg);\n  }\n  // 绘制 Button 和 文本\n  private drawButton() {\n    const config = this.getButtonCfg();\n    const group = this.context.view.foregroundGroup.addGroup({\n      name: config.name,\n    });\n    // 添加文本\n    const textShape = group.addShape({\n      type: 'text',\n      name: 'button-text',\n      attrs: {\n        text: config.text,\n        ...config.textStyle,\n      },\n    });\n    const textBBox = textShape.getBBox();\n    const padding = parsePadding(config.padding);\n    // 添加背景按钮\n    const buttonShape = group.addShape({\n      type: 'rect',\n      name: 'button-rect',\n      attrs: {\n        x: textBBox.x - padding[3],\n        y: textBBox.y - padding[0],\n        width: textBBox.width + padding[1] + padding[3],\n        height: textBBox.height + padding[0] + padding[2],\n        ...config.style,\n      },\n    });\n    buttonShape.toBack(); // 在后面\n    // active 效果内置\n    group.on('mouseenter', () => {\n      buttonShape.attr(config.activeStyle);\n    });\n    group.on('mouseleave', () => {\n      buttonShape.attr(config.style);\n    });\n    this.buttonGroup = group;\n  }\n\n  // 重置位置\n  private resetPosition() {\n    const view = this.context.view;\n    const coord = view.getCoordinate();\n    const point = coord.convert({ x: 1, y: 1 }); // 后面直接改成左上角\n    const buttonGroup = this.buttonGroup;\n    const bbox = buttonGroup.getBBox();\n    const matrix = ext.transform(null, [\n      ['t', point.x - bbox.width - PADDING_RIGHT, point.y + bbox.height + PADDING_TOP],\n    ]);\n    buttonGroup.setMatrix(matrix);\n  }\n\n  /**\n   * 显示\n   */\n  public show() {\n    if (!this.buttonGroup) {\n      this.drawButton();\n    }\n    this.resetPosition();\n    this.buttonGroup.show();\n  }\n\n  /**\n   * 隐藏\n   */\n  public hide() {\n    if (this.buttonGroup) {\n      this.buttonGroup.hide();\n    }\n  }\n\n  /**\n   * 销毁\n   */\n  public destroy() {\n    const buttonGroup = this.buttonGroup;\n    if (buttonGroup) {\n      buttonGroup.remove();\n    }\n    super.destroy();\n  }\n}\n\nexport default ButtonAction;\n"]}