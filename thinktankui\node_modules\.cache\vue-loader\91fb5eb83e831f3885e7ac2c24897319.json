{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\gen\\index.vue", "mtime": 1749109381358}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VGFibGUsIHByZXZpZXdUYWJsZSwgZGVsVGFibGUsIGdlbkNvZGUsIHN5bmNoRGIgfSBmcm9tICJAL2FwaS90b29sL2dlbiI7DQppbXBvcnQgaW1wb3J0VGFibGUgZnJvbSAiLi9pbXBvcnRUYWJsZSI7DQppbXBvcnQgY3JlYXRlVGFibGUgZnJvbSAiLi9jcmVhdGVUYWJsZSI7DQppbXBvcnQgaGxqcyBmcm9tICJoaWdobGlnaHQuanMvbGliL2hpZ2hsaWdodCI7DQppbXBvcnQgImhpZ2hsaWdodC5qcy9zdHlsZXMvZ2l0aHViLWdpc3QuY3NzIjsNCmhsanMucmVnaXN0ZXJMYW5ndWFnZSgicHkiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy9weXRob24iKSk7DQpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoImh0bWwiLCByZXF1aXJlKCJoaWdobGlnaHQuanMvbGliL2xhbmd1YWdlcy94bWwiKSk7DQpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoInZ1ZSIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL3htbCIpKTsNCmhsanMucmVnaXN0ZXJMYW5ndWFnZSgiamF2YXNjcmlwdCIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL2phdmFzY3JpcHQiKSk7DQpobGpzLnJlZ2lzdGVyTGFuZ3VhZ2UoInNxbCIsIHJlcXVpcmUoImhpZ2hsaWdodC5qcy9saWIvbGFuZ3VhZ2VzL3NxbCIpKTsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiR2VuIiwNCiAgY29tcG9uZW50czogeyBpbXBvcnRUYWJsZSwgY3JlYXRlVGFibGUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g5ZSv5LiA5qCH6K+G56ymDQogICAgICB1bmlxdWVJZDogIiIsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpgInkuK3ooajmlbDnu4QNCiAgICAgIHRhYmxlTmFtZXM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOihqOaVsOaNrg0KICAgICAgdGFibGVMaXN0OiBbXSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiAiIiwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICB0YWJsZU5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgdGFibGVDb21tZW50OiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICAvLyDpooTop4jlj4LmlbANCiAgICAgIHByZXZpZXc6IHsNCiAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgIHRpdGxlOiAi5Luj56CB6aKE6KeIIiwNCiAgICAgICAgZGF0YToge30sDQogICAgICAgIGFjdGl2ZU5hbWU6ICJkby5weSINCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBhY3RpdmF0ZWQoKSB7DQogICAgY29uc3QgdGltZSA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnQ7DQogICAgaWYgKHRpbWUgIT0gbnVsbCAmJiB0aW1lICE9IHRoaXMudW5pcXVlSWQpIHsNCiAgICAgIHRoaXMudW5pcXVlSWQgPSB0aW1lOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gTnVtYmVyKHRoaXMuJHJvdXRlLnF1ZXJ5LnBhZ2VOdW0pOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouihqOmbhuWQiCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFRhYmxlKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy50YWJsZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDnlJ/miJDku6PnoIHmk43kvZwgKi8NCiAgICBoYW5kbGVHZW5UYWJsZShyb3cpIHsNCiAgICAgIGNvbnN0IHRhYmxlTmFtZXMgPSByb3cudGFibGVOYW1lIHx8IHRoaXMudGFibGVOYW1lczsNCiAgICAgIGlmICh0YWJsZU5hbWVzID09ICIiKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHnlJ/miJDnmoTmlbDmja4iKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYocm93LmdlblR5cGUgPT09ICIxIikgew0KICAgICAgICBnZW5Db2RlKHJvdy50YWJsZU5hbWUpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaIkOWKn+eUn+aIkOWIsOiHquWumuS5iei3r+W+hO+8miIgKyByb3cuZ2VuUGF0aCk7DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kZG93bmxvYWQuemlwKCIvdG9vbC9nZW4vYmF0Y2hHZW5Db2RlP3RhYmxlcz0iICsgdGFibGVOYW1lcywgInZmYWRtaW4uemlwIik7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5ZCM5q2l5pWw5o2u5bqT5pON5L2cICovDQogICAgaGFuZGxlU3luY2hEYihyb3cpIHsNCiAgICAgIGNvbnN0IHRhYmxlTmFtZSA9IHJvdy50YWJsZU5hbWU7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoHlvLrliLblkIzmraUiJyArIHRhYmxlTmFtZSArICci6KGo57uT5p6E5ZCX77yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIHN5bmNoRGIodGFibGVOYW1lKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlkIzmraXmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDmiZPlvIDlr7zlhaXooajlvLnnqpcgKi8NCiAgICBvcGVuSW1wb3J0VGFibGUoKSB7DQogICAgICB0aGlzLiRyZWZzLmltcG9ydC5zaG93KCk7DQogICAgfSwNCiAgICAvKiog5omT5byA5Yib5bu66KGo5by556qXICovDQogICAgb3BlbkNyZWF0ZVRhYmxlKCkgew0KICAgICAgdGhpcy4kcmVmcy5jcmVhdGUuc2hvdygpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOmihOiniOaMiemSriAqLw0KICAgIGhhbmRsZVByZXZpZXcocm93KSB7DQogICAgICBwcmV2aWV3VGFibGUocm93LnRhYmxlSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnByZXZpZXcuZGF0YSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMucHJldmlldy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5wcmV2aWV3LmFjdGl2ZU5hbWUgPSAiZG8ucHkiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6auY5Lqu5pi+56S6ICovDQogICAgaGlnaGxpZ2h0ZWRDb2RlKGNvZGUsIGtleSkgew0KICAgICAgY29uc3Qgdm1OYW1lID0ga2V5LnN1YnN0cmluZyhrZXkubGFzdEluZGV4T2YoIi8iKSArIDEsIGtleS5pbmRleE9mKCIuamluamEyIikpOw0KICAgICAgdmFyIGxhbmd1YWdlID0gdm1OYW1lLnN1YnN0cmluZyh2bU5hbWUuaW5kZXhPZigiLiIpICsgMSwgdm1OYW1lLmxlbmd0aCk7DQogICAgICBjb25zdCByZXN1bHQgPSBobGpzLmhpZ2hsaWdodChsYW5ndWFnZSwgY29kZSB8fCAiIiwgdHJ1ZSk7DQogICAgICByZXR1cm4gcmVzdWx0LnZhbHVlIHx8ICcmbmJzcDsnOw0KICAgIH0sDQogICAgLyoqIOWkjeWItuS7o+eggeaIkOWKnyAqLw0KICAgIGNsaXBib2FyZFN1Y2Nlc3MoKSB7DQogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlpI3liLbmiJDlip8iKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0udGFibGVJZCk7DQogICAgICB0aGlzLnRhYmxlTmFtZXMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS50YWJsZU5hbWUpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRWRpdFRhYmxlKHJvdykgew0KICAgICAgY29uc3QgdGFibGVJZCA9IHJvdy50YWJsZUlkIHx8IHRoaXMuaWRzWzBdOw0KICAgICAgY29uc3QgdGFibGVOYW1lID0gcm93LnRhYmxlTmFtZSB8fCB0aGlzLnRhYmxlTmFtZXNbMF07DQogICAgICBjb25zdCBwYXJhbXMgPSB7IHBhZ2VOdW06IHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSB9Ow0KICAgICAgdGhpcy4kdGFiLm9wZW5QYWdlKCLkv67mlLlbIiArIHRhYmxlTmFtZSArICJd55Sf5oiQ6YWN572uIiwgJy90b29sL2dlbi1lZGl0L2luZGV4LycgKyB0YWJsZUlkLCBwYXJhbXMpOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHRhYmxlSWRzID0gcm93LnRhYmxlSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTooajnvJblj7fkuLoiJyArIHRhYmxlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICByZXR1cm4gZGVsVGFibGUodGFibGVJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/tool/gen", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"表名称\" prop=\"tableName\">\r\n        <el-input\r\n          v-model=\"queryParams.tableName\"\r\n          placeholder=\"请输入表名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"表描述\" prop=\"tableComment\">\r\n        <el-input\r\n          v-model=\"queryParams.tableComment\"\r\n          placeholder=\"请输入表描述\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleGenTable\"\r\n          v-hasPermi=\"['tool:gen:code']\"\r\n        >生成</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"openCreateTable\"\r\n          v-hasRole=\"['admin']\"\r\n        >创建</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-upload\"\r\n          size=\"mini\"\r\n          @click=\"openImportTable\"\r\n          v-hasPermi=\"['tool:gen:import']\"\r\n        >导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleEditTable\"\r\n          v-hasPermi=\"['tool:gen:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['tool:gen:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"tableList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" align=\"center\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"表名称\"\r\n        align=\"center\"\r\n        prop=\"tableName\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"表描述\"\r\n        align=\"center\"\r\n        prop=\"tableComment\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column\r\n        label=\"实体\"\r\n        align=\"center\"\r\n        prop=\"className\"\r\n        :show-overflow-tooltip=\"true\"\r\n        width=\"120\"\r\n      />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\r\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handlePreview(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:preview']\"\r\n          >预览</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleEditTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >编辑</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:remove']\"\r\n          >删除</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-refresh\"\r\n            @click=\"handleSynchDb(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:edit']\"\r\n          >同步</el-button>\r\n          <el-button\r\n            type=\"text\"\r\n            size=\"small\"\r\n            icon=\"el-icon-download\"\r\n            @click=\"handleGenTable(scope.row)\"\r\n            v-hasPermi=\"['tool:gen:code']\"\r\n          >生成代码</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n    <!-- 预览界面 -->\r\n    <el-dialog :title=\"preview.title\" :visible.sync=\"preview.open\" width=\"80%\" top=\"5vh\" append-to-body class=\"scrollbar\">\r\n      <el-tabs v-model=\"preview.activeName\">\r\n        <el-tab-pane\r\n          v-for=\"(value, key) in preview.data\"\r\n          :label=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\r\n          :name=\"key.substring(key.lastIndexOf('/')+1,key.indexOf('.jinja2'))\"\r\n          :key=\"key\"\r\n        >\r\n          <el-link :underline=\"false\" icon=\"el-icon-document-copy\" v-clipboard:copy=\"value\" v-clipboard:success=\"clipboardSuccess\" style=\"float:right\">复制</el-link>\r\n          <pre><code class=\"hljs\" v-html=\"highlightedCode(value, key)\"></code></pre>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n    <import-table ref=\"import\" @ok=\"handleQuery\" />\r\n    <create-table ref=\"create\" @ok=\"handleQuery\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listTable, previewTable, delTable, genCode, synchDb } from \"@/api/tool/gen\";\r\nimport importTable from \"./importTable\";\r\nimport createTable from \"./createTable\";\r\nimport hljs from \"highlight.js/lib/highlight\";\r\nimport \"highlight.js/styles/github-gist.css\";\r\nhljs.registerLanguage(\"py\", require(\"highlight.js/lib/languages/python\"));\r\nhljs.registerLanguage(\"html\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"vue\", require(\"highlight.js/lib/languages/xml\"));\r\nhljs.registerLanguage(\"javascript\", require(\"highlight.js/lib/languages/javascript\"));\r\nhljs.registerLanguage(\"sql\", require(\"highlight.js/lib/languages/sql\"));\r\n\r\nexport default {\r\n  name: \"Gen\",\r\n  components: { importTable, createTable },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 唯一标识符\r\n      uniqueId: \"\",\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中表数组\r\n      tableNames: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表数据\r\n      tableList: [],\r\n      // 日期范围\r\n      dateRange: \"\",\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        tableName: undefined,\r\n        tableComment: undefined\r\n      },\r\n      // 预览参数\r\n      preview: {\r\n        open: false,\r\n        title: \"代码预览\",\r\n        data: {},\r\n        activeName: \"do.py\"\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  activated() {\r\n    const time = this.$route.query.t;\r\n    if (time != null && time != this.uniqueId) {\r\n      this.uniqueId = time;\r\n      this.queryParams.pageNum = Number(this.$route.query.pageNum);\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询表集合 */\r\n    getList() {\r\n      this.loading = true;\r\n      listTable(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.tableList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 生成代码操作 */\r\n    handleGenTable(row) {\r\n      const tableNames = row.tableName || this.tableNames;\r\n      if (tableNames == \"\") {\r\n        this.$modal.msgError(\"请选择要生成的数据\");\r\n        return;\r\n      }\r\n      if(row.genType === \"1\") {\r\n        genCode(row.tableName).then(response => {\r\n          this.$modal.msgSuccess(\"成功生成到自定义路径：\" + row.genPath);\r\n        });\r\n      } else {\r\n        this.$download.zip(\"/tool/gen/batchGenCode?tables=\" + tableNames, \"vfadmin.zip\");\r\n      }\r\n    },\r\n    /** 同步数据库操作 */\r\n    handleSynchDb(row) {\r\n      const tableName = row.tableName;\r\n      this.$modal.confirm('确认要强制同步\"' + tableName + '\"表结构吗？').then(function() {\r\n        return synchDb(tableName);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(\"同步成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 打开导入表弹窗 */\r\n    openImportTable() {\r\n      this.$refs.import.show();\r\n    },\r\n    /** 打开创建表弹窗 */\r\n    openCreateTable() {\r\n      this.$refs.create.show();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 预览按钮 */\r\n    handlePreview(row) {\r\n      previewTable(row.tableId).then(response => {\r\n        this.preview.data = response.data;\r\n        this.preview.open = true;\r\n        this.preview.activeName = \"do.py\";\r\n      });\r\n    },\r\n    /** 高亮显示 */\r\n    highlightedCode(code, key) {\r\n      const vmName = key.substring(key.lastIndexOf(\"/\") + 1, key.indexOf(\".jinja2\"));\r\n      var language = vmName.substring(vmName.indexOf(\".\") + 1, vmName.length);\r\n      const result = hljs.highlight(language, code || \"\", true);\r\n      return result.value || '&nbsp;';\r\n    },\r\n    /** 复制代码成功 */\r\n    clipboardSuccess() {\r\n      this.$modal.msgSuccess(\"复制成功\");\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.tableId);\r\n      this.tableNames = selection.map(item => item.tableName);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleEditTable(row) {\r\n      const tableId = row.tableId || this.ids[0];\r\n      const tableName = row.tableName || this.tableNames[0];\r\n      const params = { pageNum: this.queryParams.pageNum };\r\n      this.$tab.openPage(\"修改[\" + tableName + \"]生成配置\", '/tool/gen-edit/index/' + tableId, params);\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const tableIds = row.tableId || this.ids;\r\n      this.$modal.confirm('是否确认删除表编号为\"' + tableIds + '\"的数据项？').then(function() {\r\n        return delTable(tableIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}