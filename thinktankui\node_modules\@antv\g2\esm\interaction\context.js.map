{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../src/interaction/context.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,YAAY,CAAC;AAIvC,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAEvD;;GAEG;AACH;IAUE,iBAAY,IAAU;QATtB,mBAAmB;QACZ,YAAO,GAAc,EAAE,CAAC;QAG/B,aAAa;QACN,UAAK,GAAgB,IAAI,CAAC;QAEzB,aAAQ,GAAgB,EAAE,CAAC;QAGjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACI,uBAAK,GAAZ;QAAa,gBAAS;aAAT,UAAS,EAAT,qBAAS,EAAT,IAAS;YAAT,2BAAS;;QACpB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACjC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;SACtC;IACH,CAAC;IAED;;;OAGG;IACI,2BAAS,GAAhB,UAAiB,IAAY;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,IAAI,KAAK,IAAI,EAApB,CAAoB,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG;IACI,2BAAS,GAAhB,UAAiB,MAAe;QAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,8BAAY,GAAnB,UAAoB,MAAe;QACjC,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACI,iCAAe,GAAtB;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,EAAE;YACT,IAAI,KAAK,CAAC,MAAM,YAAY,WAAW,EAAE;gBACvC,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACpE,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,OAAO;oBACL,CAAC,EAAE,KAAK,CAAC,CAAC;oBACV,CAAC,EAAE,KAAK,CAAC,CAAC;iBACX,CAAC;aACH;SACF;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,iCAAe,GAAtB;QACE,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,0BAAQ,GAAf;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,IAAI,KAAK,EAAE;YACT,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SACvC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,2BAAS,GAAhB,UAAiB,IAAI;QACnB,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,4BAA4B;QAClE,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;SACnC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,+BAAa,GAApB,UAAqB,IAAa;QAChC,IAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAM,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACrC,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,SAAS;gBACjC,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAU,CAAC;gBACzC,IAAI,IAAI,EAAE;oBACR,OAAO,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBAC/D;qBAAM;oBACL,OAAO,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBAC7B;YACH,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,yBAAO,GAAd;QACE,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,UAAC,MAAM;YAChC,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IACH,cAAC;AAAD,CAAC,AA5ID,IA4IC", "sourcesContent": ["import { each, get } from '@antv/util';\nimport { View } from '../chart';\nimport { BBox, IShape, Point } from '../dependents';\nimport { IAction, IInteractionContext, LooseObject } from '../interface';\nimport { getComponents, isInBox } from './action/util';\n\n/**\n * 交互的上下文\n */\nexport default class Context implements IInteractionContext {\n  /** 当前所有的 Action */\n  public actions: IAction[] = [];\n  /** 当前 View 实例 */\n  public view: View;\n  /** 当前事件对象 */\n  public event: LooseObject = null;\n\n  private cacheMap: LooseObject = {};\n\n  constructor(view: View) {\n    this.view = view;\n  }\n\n  /**\n   * 缓存信息\n   * @param params 缓存的字段\n   *  - 如果一个字段则获取缓存\n   *  - 两个字段则设置缓存\n   */\n  public cache(...params) {\n    if (params.length === 1) {\n      return this.cacheMap[params[0]];\n    } else if (params.length === 2) {\n      this.cacheMap[params[0]] = params[1];\n    }\n  }\n\n  /**\n   * 获取 Action\n   * @param name Action 的名称\n   */\n  public getAction(name: string): IAction {\n    return this.actions.find((action) => action.name === name);\n  }\n\n  /**\n   * 获取 Action\n   * @param action Action 对象\n   */\n  public addAction(action: IAction) {\n    this.actions.push(action);\n  }\n\n  /**\n   * 移除 Action\n   * @param action Action 对象\n   */\n  public removeAction(action: IAction) {\n    const actions = this.actions;\n    const index = this.actions.indexOf(action);\n    if (index >= 0) {\n      actions.splice(index, 1);\n    }\n  }\n\n  /**\n   * 获取当前的点\n   */\n  public getCurrentPoint(): Point {\n    const event = this.event;\n    if (event) {\n      if (event.target instanceof HTMLElement) {\n        const canvas = this.view.getCanvas();\n        const point = canvas.getPointByClient(event.clientX, event.clientY);\n        return point;\n      } else {\n        return {\n          x: event.x,\n          y: event.y,\n        };\n      }\n    }\n    return null;\n  }\n\n  /**\n   * 获取当前 shape\n   * @returns current shape\n   */\n  public getCurrentShape(): IShape {\n    return get(this.event, ['gEvent', 'shape']);\n  }\n\n  /**\n   * 当前的触发是否在 View 内\n   */\n  public isInPlot() {\n    const point = this.getCurrentPoint();\n    if (point) {\n      return this.view.isPointInPlot(point);\n    }\n    return false;\n  }\n\n  /**\n   * 是否在指定的图形内\n   * @param name shape 的 name\n   */\n  public isInShape(name) {\n    const shape = this.getCurrentShape(); // 不再考虑在 shape 的 parent 内的情况\n    if (shape) {\n      return shape.get('name') === name;\n    }\n    return false;\n  }\n\n  /**\n   * 当前的触发是组件内部\n   * @param name 组件名，可以为空\n   */\n  public isInComponent(name?: string) {\n    const components = getComponents(this.view);\n    const point = this.getCurrentPoint();\n    if (point) {\n      return !!components.find((component) => {\n        const bbox = component.getBBox() as BBox;\n        if (name) {\n          return component.get('name') === name && isInBox(bbox, point);\n        } else {\n          return isInBox(bbox, point);\n        }\n      });\n    }\n    return false;\n  }\n\n  /**\n   * 销毁\n   */\n  public destroy() {\n    // 先销毁 action 再清空，一边遍历，一边删除，所以数组需要更新引用\n    each(this.actions.slice(), (action) => {\n      action.destroy();\n    });\n    this.view = null;\n    this.event = null;\n    this.actions = null;\n    this.cacheMap = null;\n  }\n}\n"]}