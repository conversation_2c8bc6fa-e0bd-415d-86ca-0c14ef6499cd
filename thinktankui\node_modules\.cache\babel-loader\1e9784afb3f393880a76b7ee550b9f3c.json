{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\system\\dept\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\system\\dept\\index.vue", "mtime": 1749109381351}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_dept", "require", "_vueTreeselect", "_interopRequireDefault", "name", "dicts", "components", "Treeselect", "data", "loading", "showSearch", "deptList", "deptOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "deptName", "undefined", "status", "form", "rules", "parentId", "required", "message", "trigger", "orderNum", "email", "type", "phone", "pattern", "created", "getList", "methods", "_this", "listDept", "then", "response", "handleTree", "normalizer", "node", "children", "length", "id", "deptId", "label", "cancel", "reset", "leader", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "_this2", "toggleExpandAll", "_this3", "$nextTick", "handleUpdate", "_this4", "getDept", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noResultsOptions", "parentName", "push", "submitForm", "_this5", "$refs", "validate", "valid", "updateDept", "$modal", "msgSuccess", "addDept", "handleDelete", "_this6", "confirm", "delDept", "catch"], "sources": ["src/views/system/dept/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n        <el-input\r\n          v-model=\"queryParams.deptName\"\r\n          placeholder=\"请输入部门名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"部门状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:dept:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"deptList\"\r\n      row-key=\"deptId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"deptName\" label=\"部门名称\" width=\"260\"></el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"200\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:dept:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:dept:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.parentId != 0\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:dept:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改部门对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"24\" v-if=\"form.parentId !== 0\">\r\n            <el-form-item label=\"上级部门\" prop=\"parentId\">\r\n              <treeselect v-model=\"form.parentId\" :options=\"deptOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级部门\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门名称\" prop=\"deptName\">\r\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入部门名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人\" prop=\"leader\">\r\n              <el-input v-model=\"form.leader\" placeholder=\"请输入负责人\" maxlength=\"20\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\" prop=\"phone\">\r\n              <el-input v-model=\"form.phone\" placeholder=\"请输入联系电话\" maxlength=\"11\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from \"@/api/system/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"Dept\",\r\n  dicts: ['sys_normal_disable'],\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 表格树数据\r\n      deptList: [],\r\n      // 部门树选项\r\n      deptOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: true,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        deptName: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: \"上级部门不能为空\", trigger: \"blur\" }\r\n        ],\r\n        deptName: [\r\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"显示排序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询部门列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDept(this.queryParams).then(response => {\r\n        this.deptList = this.handleTree(response.data, \"deptId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换部门数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.deptId,\r\n        label: node.deptName,\r\n        children: node.children\r\n      };\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        deptId: undefined,\r\n        parentId: undefined,\r\n        deptName: undefined,\r\n        orderNum: undefined,\r\n        leader: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row != undefined) {\r\n        this.form.parentId = row.deptId;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加部门\";\r\n      listDept().then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      getDept(row.deptId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改部门\";\r\n        listDeptExcludeChild(row.deptId).then(response => {\r\n          this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n          if (this.deptOptions.length == 0) {\r\n            const noResultsOptions = { deptId: this.form.parentId, deptName: this.form.parentName, children: [] };\r\n            this.deptOptions.push(noResultsOptions);\r\n          }\r\n        });\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.deptId != undefined) {\r\n            updateDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.deptName + '\"的数据项？').then(function() {\r\n        return delDept(row.deptId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AAgKA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD;MACA;MACA;MACAE,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,KAAA,GACA;UACAC,IAAA;UACAJ,OAAA;UACAC,OAAA;QACA,EACA;QACAI,KAAA,GACA;UACAC,OAAA;UACAN,OAAA;UACAC,OAAA;QACA;MAEA;IACA;EACA;EACAM,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,cAAA,OAAAnB,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAAwB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA9B,IAAA;QACA2B,KAAA,CAAA1B,OAAA;MACA;IACA;IACA,eACA+B,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAAvB,QAAA;QACAwB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA;IACAK,MAAA,WAAAA,OAAA;MACA,KAAAjC,IAAA;MACA,KAAAkC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA3B,IAAA;QACAwB,MAAA,EAAA1B,SAAA;QACAI,QAAA,EAAAJ,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAQ,QAAA,EAAAR,SAAA;QACA8B,MAAA,EAAA9B,SAAA;QACAW,KAAA,EAAAX,SAAA;QACAS,KAAA,EAAAT,SAAA;QACAC,MAAA;MACA;MACA,KAAA8B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlB,OAAA;IACA;IACA,aACAmB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,KAAA;MACA,IAAAM,GAAA,IAAAnC,SAAA;QACA,KAAAE,IAAA,CAAAE,QAAA,GAAA+B,GAAA,CAAAT,MAAA;MACA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;MACA,IAAAuB,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA3C,WAAA,GAAA2C,MAAA,CAAAhB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;MACA;IACA;IACA,cACAgD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAA2C,SAAA;QACAD,MAAA,CAAAzC,YAAA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAL,GAAA;MAAA,IAAAM,MAAA;MACA,KAAAZ,KAAA;MACA,IAAAa,aAAA,EAAAP,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAAvC,IAAA,GAAAiB,QAAA,CAAA9B,IAAA;QACAoD,MAAA,CAAA9C,IAAA;QACA8C,MAAA,CAAA/C,KAAA;QACA,IAAAiD,0BAAA,EAAAR,GAAA,CAAAT,MAAA,EAAAR,IAAA,WAAAC,QAAA;UACAsB,MAAA,CAAAhD,WAAA,GAAAgD,MAAA,CAAArB,UAAA,CAAAD,QAAA,CAAA9B,IAAA;UACA,IAAAoD,MAAA,CAAAhD,WAAA,CAAA+B,MAAA;YACA,IAAAoB,gBAAA;cAAAlB,MAAA,EAAAe,MAAA,CAAAvC,IAAA,CAAAE,QAAA;cAAAL,QAAA,EAAA0C,MAAA,CAAAvC,IAAA,CAAA2C,UAAA;cAAAtB,QAAA;YAAA;YACAkB,MAAA,CAAAhD,WAAA,CAAAqD,IAAA,CAAAF,gBAAA;UACA;QACA;MACA;IACA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9C,IAAA,CAAAwB,MAAA,IAAA1B,SAAA;YACA,IAAAoD,gBAAA,EAAAJ,MAAA,CAAA9C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAlC,OAAA;YACA;UACA;YACA,IAAAyC,aAAA,EAAAP,MAAA,CAAA9C,IAAA,EAAAgB,IAAA,WAAAC,QAAA;cACA6B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArD,IAAA;cACAqD,MAAA,CAAAlC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA0C,YAAA,WAAAA,aAAArB,GAAA;MAAA,IAAAsB,MAAA;MACA,KAAAJ,MAAA,CAAAK,OAAA,gBAAAvB,GAAA,CAAApC,QAAA,aAAAmB,IAAA;QACA,WAAAyC,aAAA,EAAAxB,GAAA,CAAAT,MAAA;MACA,GAAAR,IAAA;QACAuC,MAAA,CAAA3C,OAAA;QACA2C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAM,KAAA;IACA;EACA;AACA", "ignoreList": []}]}