from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class WarningRecordPageQueryModel(BaseModel):
    """
    预警记录分页查询模型
    """
    model_config = ConfigDict(from_attributes=True)

    page_num: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页条数")
    scheme_id: Optional[int] = Field(default=None, description="方案ID")
    warning_type: Optional[str] = Field(default=None, description="预警类型")
    content: Optional[str] = Field(default=None, description="内容")
    keywords: Optional[str] = Field(default=None, description="关键词")
    status: Optional[int] = Field(default=None, description="状态")
    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")

    @classmethod
    def as_query(cls, 
                 page_num: int = 1,
                 page_size: int = 10,
                 scheme_id: Optional[int] = None,
                 warning_type: Optional[str] = None,
                 content: Optional[str] = None,
                 keywords: Optional[str] = None,
                 status: Optional[int] = None,
                 begin_time: Optional[str] = None,
                 end_time: Optional[str] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            scheme_id=scheme_id,
            warning_type=warning_type,
            content=content,
            keywords=keywords,
            status=status,
            begin_time=begin_time,
            end_time=end_time
        )


class WarningSchemePageQueryModel(BaseModel):
    """
    预警方案分页查询模型
    """
    model_config = ConfigDict(from_attributes=True)

    page_num: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页条数")
    scheme_name: Optional[str] = Field(default=None, description="方案名称")
    scheme_type: Optional[str] = Field(default=None, description="方案类型")
    is_active: Optional[bool] = Field(default=None, description="是否启用")
    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")

    @classmethod
    def as_query(cls, 
                 page_num: int = 1,
                 page_size: int = 10,
                 scheme_name: Optional[str] = None,
                 scheme_type: Optional[str] = None,
                 is_active: Optional[bool] = None,
                 begin_time: Optional[str] = None,
                 end_time: Optional[str] = None):
        return cls(
            page_num=page_num,
            page_size=page_size,
            scheme_name=scheme_name,
            scheme_type=scheme_type,
            is_active=is_active,
            begin_time=begin_time,
            end_time=end_time
        )


class WarningRecordModel(BaseModel):
    """
    预警记录模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description="记录ID")
    scheme_id: int = Field(description="方案ID")
    warning_type: str = Field(description="预警类型")
    content: Optional[str] = Field(default=None, description="预警内容")
    keywords: Optional[str] = Field(default=None, description="关键词")
    status: Optional[int] = Field(default=0, description="状态：0-未处理，1-已处理，2-已忽略")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    create_by: Optional[str] = Field(default='', description="创建者")
    update_by: Optional[str] = Field(default='', description="更新者")
    remark: Optional[str] = Field(default=None, description="备注")


class WarningSchemeModel(BaseModel):
    """
    预警方案模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description="方案ID")
    scheme_name: str = Field(description="方案名称")
    scheme_type: Optional[str] = Field(default="default", description="方案类型")
    description: Optional[str] = Field(default=None, description="方案描述")
    is_active: Optional[bool] = Field(default=True, description="是否启用")
    create_by: Optional[str] = Field(default=None, description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")


class WarningSettingsModel(BaseModel):
    """
    预警设置模型
    """
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None, description="设置ID")
    scheme_id: int = Field(description="方案ID")
    platform_types: Optional[Dict[str, Any]] = Field(default=None, description="平台类型设置")
    content_property: Optional[str] = Field(default=None, description="内容属性")
    info_type: Optional[str] = Field(default=None, description="信息类型")
    match_objects: Optional[Dict[str, Any]] = Field(default=None, description="匹配对象")
    match_method: Optional[str] = Field(default=None, description="匹配方式")
    publish_regions: Optional[List[str]] = Field(default=None, description="发布地区")
    ip_areas: Optional[List[str]] = Field(default=None, description="IP属地")
    media_categories: Optional[List[str]] = Field(default=None, description="媒体类别")
    article_categories: Optional[List[str]] = Field(default=None, description="文章类别")
    create_by: Optional[str] = Field(default=None, description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")


class DeleteWarningRecordModel(BaseModel):
    """
    删除预警记录模型
    """
    model_config = ConfigDict(from_attributes=True)

    record_ids: str = Field(description="记录ID列表，用逗号分隔")


class DeleteWarningSchemeModel(BaseModel):
    """
    删除预警方案模型
    """
    model_config = ConfigDict(from_attributes=True)

    scheme_ids: str = Field(description="方案ID列表，用逗号分隔")


class WarningStatisticsModel(BaseModel):
    """
    预警统计模型
    """
    model_config = ConfigDict(from_attributes=True)

    total_records: int = Field(description="总记录数")
    unprocessed_count: int = Field(description="未处理数量")
    processed_count: int = Field(description="已处理数量")
    ignored_count: int = Field(description="已忽略数量")
    today_count: int = Field(description="今日新增数量")
    scheme_count: int = Field(description="方案总数")
    active_scheme_count: int = Field(description="启用方案数")
