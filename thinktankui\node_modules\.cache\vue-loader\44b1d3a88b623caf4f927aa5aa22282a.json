{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1749109381332}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBtYXBHZXR0ZXJzLCBtYXBTdGF0ZSB9IGZyb20gInZ1ZXgiOw0KaW1wb3J0IExvZ28gZnJvbSAiLi9Mb2dvIjsNCmltcG9ydCBTaWRlYmFySXRlbSBmcm9tICIuL1NpZGViYXJJdGVtIjsNCmltcG9ydCB2YXJpYWJsZXMgZnJvbSAiQC9hc3NldHMvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIGNvbXBvbmVudHM6IHsgU2lkZWJhckl0ZW0sIExvZ28gfSwNCiAgICBjb21wdXRlZDogew0KICAgICAgICAuLi5tYXBTdGF0ZShbInNldHRpbmdzIl0pLA0KICAgICAgICAuLi5tYXBHZXR0ZXJzKFsic2lkZWJhclJvdXRlcnMiLCAic2lkZWJhciJdKSwNCiAgICAgICAgYWN0aXZlTWVudSgpIHsNCiAgICAgICAgICAgIGNvbnN0IHJvdXRlID0gdGhpcy4kcm91dGU7DQogICAgICAgICAgICBjb25zdCB7IG1ldGEsIHBhdGggfSA9IHJvdXRlOw0KICAgICAgICAgICAgLy8gaWYgc2V0IHBhdGgsIHRoZSBzaWRlYmFyIHdpbGwgaGlnaGxpZ2h0IHRoZSBwYXRoIHlvdSBzZXQNCiAgICAgICAgICAgIGlmIChtZXRhLmFjdGl2ZU1lbnUpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gbWV0YS5hY3RpdmVNZW51Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgcmV0dXJuIHBhdGg7DQogICAgICAgIH0sDQogICAgICAgIHNob3dMb2dvKCkgew0KICAgICAgICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLnNpZGViYXJMb2dvOw0KICAgICAgICB9LA0KICAgICAgICB2YXJpYWJsZXMoKSB7DQogICAgICAgICAgICByZXR1cm4gdmFyaWFibGVzOw0KICAgICAgICB9LA0KICAgICAgICBpc0NvbGxhcHNlKCkgew0KICAgICAgICAgICAgcmV0dXJuICF0aGlzLnNpZGViYXIub3BlbmVkOw0KICAgICAgICB9DQogICAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n    <div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\r\n        <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\r\n        <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\r\n            <el-menu\r\n                :default-active=\"activeMenu\"\r\n                :collapse=\"isCollapse\"\r\n                :background-color=\"settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\r\n                :text-color=\"settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\r\n                :unique-opened=\"true\"\r\n                :active-text-color=\"settings.theme\"\r\n                :collapse-transition=\"false\"\r\n                mode=\"vertical\"\r\n            >\r\n                <sidebar-item\r\n                    v-for=\"(route, index) in sidebarRouters\"\r\n                    :key=\"route.path  + index\"\r\n                    :item=\"route\"\r\n                    :base-path=\"route.path\"\r\n                />\r\n            </el-menu>\r\n        </el-scrollbar>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters, mapState } from \"vuex\";\r\nimport Logo from \"./Logo\";\r\nimport SidebarItem from \"./SidebarItem\";\r\nimport variables from \"@/assets/styles/variables.scss\";\r\n\r\nexport default {\r\n    components: { SidebarItem, Logo },\r\n    computed: {\r\n        ...mapState([\"settings\"]),\r\n        ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\r\n        activeMenu() {\r\n            const route = this.$route;\r\n            const { meta, path } = route;\r\n            // if set path, the sidebar will highlight the path you set\r\n            if (meta.activeMenu) {\r\n                return meta.activeMenu;\r\n            }\r\n            return path;\r\n        },\r\n        showLogo() {\r\n            return this.$store.state.settings.sidebarLogo;\r\n        },\r\n        variables() {\r\n            return variables;\r\n        },\r\n        isCollapse() {\r\n            return !this.sidebar.opened;\r\n        }\r\n    }\r\n};\r\n</script>\r\n"]}]}