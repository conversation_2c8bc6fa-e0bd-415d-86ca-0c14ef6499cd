{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue?vue&type=style&index=0&id=106c86ed&lang=less&scoped=true", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\dashboard\\index.vue", "mtime": 1749109381343}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1749109530725}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1749109532622}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1749109531426}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749109531156}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAiLi9Xb3JrcGxhY2UubGVzcyI7DQoNCg0KDQoucGFnZS1oZWFkZXItY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm94LXNoYWRvdzogMCAxcHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgbWFyZ2luLWJvdHRvbTogMjRweDsNCn0NCg0KLnNldHRpbmctYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLnN3aXRjaC1idXR0b25zIHsNCiAgbWFyZ2luLXRvcDogMTZweDsNCn0NCg0KLmFudC1saXN0LWl0ZW0gew0KICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCg0KICAmOmhvdmVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI0LCAxNDQsIDI1NSwgMC4wNSk7DQogIH0NCn0NCg0KLmFudC1jYXJkIHsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zczsNCg0KICAmOmhvdmVyIHsNCiAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgfQ0KfQ0KDQouYW50LXRhZyB7DQogIG1hcmdpbi1yaWdodDogMDsNCn0NCg0KLmFudC1saXN0LWl0ZW0tbWV0YS10aXRsZSB7DQogIG1hcmdpbi1ib3R0b206IDRweDsNCg0KICBhIHsNCiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAwLjg1KTsNCg0KICAgICY6aG92ZXIgew0KICAgICAgY29sb3I6ICMxODkwZmY7DQogICAgfQ0KICB9DQp9DQoNCi5hbnQtbGlzdC1pdGVtLW1ldGEtZGVzY3JpcHRpb24gew0KICBjb2xvcjogcmdiYSgwLCAwLCAwLCAwLjQ1KTsNCn0NCg0KLm1vYmlsZSB7DQogIC5tb3JlLWluZm8gew0KICAgIGJvcmRlcjogMDsNCiAgICBwYWRkaW5nLXRvcDogMTZweDsNCiAgICBtYXJnaW46IDE2cHggMCAxNnB4Ow0KICB9DQoNCiAgLmhlYWRlckNvbnRlbnQgLnRpdGxlIC53ZWxjb21lLXRleHQgew0KICAgIGRpc3BsYXk6IG5vbmU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiiBA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"page-header-content\">\r\n      <div class=\"avatar\">\r\n        <a-avatar size=\"large\" :src=\"currentUser.avatar\" />\r\n      </div>\r\n      <div class=\"content\">\r\n        <div class=\"content-title\">\r\n          {{ currentUser.name }}<span class=\"welcome-text\">，欢迎使用舆情监控系统</span>\r\n        </div>\r\n        <div>{{ currentUser.title }} | {{ currentUser.group }}</div>\r\n      </div>\r\n      <div class=\"extra-content\">\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"今日热点\" :value=\"hotTopicsCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"负面信息\" :value=\"negativeCount\" />\r\n        </div>\r\n        <div class=\"stat-item\">\r\n          <a-statistic title=\"总监控量\" :value=\"totalMonitorCount\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div>\r\n      <a-row :gutter=\"24\">\r\n        <a-col :xl=\"16\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\r\n          <a-card\r\n            :loading=\"loading\"\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n            title=\"24小时传播趋势\"\r\n          >\r\n            <div style=\"height: 400px; position: relative;\">\r\n              <v-chart :forceFit=\"true\" height=\"400\" :data=\"trendData\" :scale=\"trendScale\">\r\n                <v-tooltip crosshairs></v-tooltip>\r\n                <v-axis dataKey=\"time\"></v-axis>\r\n                <v-axis dataKey=\"value\"></v-axis>\r\n                <v-legend></v-legend>\r\n                <v-line position=\"time*value\" color=\"type\" :size=\"2\"></v-line>\r\n                <v-point position=\"time*value\" color=\"type\" :size=\"4\" shape=\"circle\"></v-point>\r\n              </v-chart>\r\n            </div>\r\n          </a-card>\r\n\r\n          <a-row :gutter=\"24\">\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"平台来源占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"platformData\" :scale=\"platformScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-coord type=\"theta\" radius=\"0.75\" innerRadius=\"0.6\"></v-coord>\r\n                    <v-pie position=\"percent\" color=\"type\" :label=\"labelConfig\"></v-pie>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n            <a-col :span=\"12\">\r\n              <a-card\r\n                :loading=\"loading\"\r\n                style=\"margin-bottom: 24px\"\r\n                :bordered=\"false\"\r\n                title=\"情感分布占比\"\r\n              >\r\n                <div style=\"height: 300px;\">\r\n                  <v-chart :forceFit=\"true\" height=\"300\" :data=\"sentimentData\" :scale=\"sentimentScale\">\r\n                    <v-tooltip></v-tooltip>\r\n                    <v-axis dataKey=\"time\"></v-axis>\r\n                    <v-axis dataKey=\"value\"></v-axis>\r\n                    <v-legend dataKey=\"type\"></v-legend>\r\n                    <v-area position=\"time*value\" color=\"type\" :opacity=\"0.6\"></v-area>\r\n                  </v-chart>\r\n                </div>\r\n              </a-card>\r\n            </a-col>\r\n          </a-row>\r\n\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-hot\" style=\"color: #F5222D; margin-right: 8px;\"></i>\r\n              热门文章\r\n            </template>\r\n            <a-list>\r\n              <a-list-item :key=\"index\" v-for=\"(item, index) in hotArticles\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-tag :color=\"item.tagColor\">{{ item.tag }}</a-tag>\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">\r\n                    <span>{{ item.source }}</span>\r\n                    <span style=\"float: right;\">{{ item.publishTime }}</span>\r\n                  </div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n        <a-col\r\n          style=\"padding: 0 12px\"\r\n          :xl=\"8\"\r\n          :lg=\"24\"\r\n          :md=\"24\"\r\n          :sm=\"24\"\r\n          :xs=\"24\"\r\n        >\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning-outline\" style=\"color: #FA8C16; margin-right: 8px;\"></i>\r\n              预警方案设置\r\n            </template>\r\n            <div class=\"setting-buttons\">\r\n              <a-button type=\"primary\" icon=\"user\" style=\"margin-right: 8px;\">接收人设置</a-button>\r\n              <a-button type=\"primary\" icon=\"setting\" style=\"margin-right: 8px;\">预警设置</a-button>\r\n              <a-button type=\"primary\" icon=\"bell\" style=\"margin-right: 8px;\">关键词设置</a-button>\r\n              <a-button type=\"primary\" icon=\"search\" @click=\"goToSearch\">搜索测试</a-button>\r\n            </div>\r\n            <a-divider />\r\n            <div class=\"switch-buttons\">\r\n              <div style=\"margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center;\">\r\n                <span>预警开关</span>\r\n                <a-switch checked></a-switch>\r\n              </div>\r\n              <div style=\"display: flex; justify-content: space-between;\">\r\n                <a-button>自动预警</a-button>\r\n                <a-button type=\"primary\">人工预警</a-button>\r\n              </div>\r\n            </div>\r\n          </a-card>\r\n          <a-card\r\n            style=\"margin-bottom: 24px\"\r\n            :bordered=\"false\"\r\n          >\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-document\" style=\"color: #52C41A; margin-right: 8px;\"></i>\r\n              报告模板\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in reportTemplates\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <div slot=\"title\">{{ item.title }}</div>\r\n                  <div slot=\"description\">{{ item.createTime }}</div>\r\n                </a-list-item-meta>\r\n                <div>\r\n                  <a-button type=\"link\" icon=\"edit\"></a-button>\r\n                  <a-button type=\"link\" icon=\"copy\"></a-button>\r\n                  <a-button type=\"link\" icon=\"delete\"></a-button>\r\n                </div>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n          <a-card :loading=\"loading\" :bordered=\"false\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-warning\" style=\"color: #FF4D4F; margin-right: 8px;\"></i>\r\n              最新负面\r\n            </template>\r\n            <a-list>\r\n              <a-list-item v-for=\"(item, index) in negativeNews\" :key=\"index\">\r\n                <a-list-item-meta>\r\n                  <template slot=\"avatar\">\r\n                    <a-badge status=\"error\" />\r\n                  </template>\r\n                  <div slot=\"title\">\r\n                    <a :href=\"item.link\">{{ item.title }}</a>\r\n                  </div>\r\n                  <div slot=\"description\">{{ item.publishTime }}</div>\r\n                </a-list-item-meta>\r\n              </a-list-item>\r\n            </a-list>\r\n          </a-card>\r\n        </a-col>\r\n      </a-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Viser from 'viser-vue';\r\nimport {\r\n  Avatar,\r\n  Button,\r\n  Card,\r\n  Col,\r\n  List,\r\n  Row,\r\n  Statistic,\r\n  Tag,\r\n  Divider,\r\n  Switch,\r\n  Badge,\r\n} from \"ant-design-vue\";\r\nimport 'ant-design-vue/dist/antd.css';\r\nimport Vue from \"vue\";\r\n\r\nVue.use(Viser);\r\nVue.component(Avatar.name, Avatar);\r\nVue.component(Button.name, Button);\r\nVue.component(Card.name, Card);\r\nVue.component(Card.Grid.name, Card.Grid);\r\nVue.component(Card.Meta.name, Card.Meta);\r\nVue.component(Col.name, Col);\r\nVue.component(List.name, List);\r\nVue.component(List.Item.name, List.Item);\r\nVue.component(List.Item.Meta.name, List.Item.Meta);\r\nVue.component(Row.name, Row);\r\nVue.component(Statistic.name, Statistic);\r\nVue.component(Tag.name, Tag);\r\nVue.component(Divider.name, Divider);\r\nVue.component(Switch.name, Switch);\r\nVue.component(Badge.name, Badge);\r\n\r\nexport default {\r\n  name: \"DashBoard\",\r\n  methods: {\r\n    goToSearch() {\r\n      // 跳转到搜索结果页面，并传递测试搜索关键词\r\n      this.$router.push({\r\n        path: '/search-results',\r\n        query: {\r\n          q: '方太 厨电'\r\n        }\r\n      });\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      originalTopNav: undefined, // 存储原始的topNav状态\r\n      currentUser: {\r\n        avatar:\r\n          \"https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png\",\r\n        name: \"方太\",\r\n        userid: \"00000001\",\r\n        email: \"<EMAIL>\",\r\n        signature: \"舆情监控，实时把控\",\r\n        title: \"舆情分析师\",\r\n        group: \"舆情监控中心\",\r\n      },\r\n      // 统计数据\r\n      hotTopicsCount: 24,\r\n      negativeCount: 8,\r\n      totalMonitorCount: 1446,\r\n\r\n      // 24小时传播趋势数据\r\n      trendData: [\r\n        { time: \"00:00\", value: 300, type: \"总量\" },\r\n        { time: \"01:00\", value: 250, type: \"总量\" },\r\n        { time: \"02:00\", value: 200, type: \"总量\" },\r\n        { time: \"03:00\", value: 180, type: \"总量\" },\r\n        { time: \"04:00\", value: 150, type: \"总量\" },\r\n        { time: \"05:00\", value: 170, type: \"总量\" },\r\n        { time: \"06:00\", value: 220, type: \"总量\" },\r\n        { time: \"07:00\", value: 350, type: \"总量\" },\r\n        { time: \"08:00\", value: 500, type: \"总量\" },\r\n        { time: \"09:00\", value: 620, type: \"总量\" },\r\n        { time: \"10:00\", value: 550, type: \"总量\" },\r\n        { time: \"11:00\", value: 480, type: \"总量\" },\r\n        { time: \"12:00\", value: 400, type: \"总量\" },\r\n        { time: \"13:00\", value: 450, type: \"总量\" },\r\n        { time: \"14:00\", value: 500, type: \"总量\" },\r\n        { time: \"15:00\", value: 470, type: \"总量\" },\r\n        { time: \"16:00\", value: 460, type: \"总量\" },\r\n        { time: \"17:00\", value: 520, type: \"总量\" },\r\n        { time: \"18:00\", value: 580, type: \"总量\" },\r\n        { time: \"19:00\", value: 550, type: \"总量\" },\r\n        { time: \"20:00\", value: 500, type: \"总量\" },\r\n        { time: \"21:00\", value: 450, type: \"总量\" },\r\n        { time: \"22:00\", value: 400, type: \"总量\" },\r\n        { time: \"23:00\", value: 350, type: \"总量\" },\r\n\r\n        { time: \"00:00\", value: 100, type: \"微博\" },\r\n        { time: \"01:00\", value: 80, type: \"微博\" },\r\n        { time: \"02:00\", value: 60, type: \"微博\" },\r\n        { time: \"03:00\", value: 50, type: \"微博\" },\r\n        { time: \"04:00\", value: 40, type: \"微博\" },\r\n        { time: \"05:00\", value: 50, type: \"微博\" },\r\n        { time: \"06:00\", value: 70, type: \"微博\" },\r\n        { time: \"07:00\", value: 100, type: \"微博\" },\r\n        { time: \"08:00\", value: 150, type: \"微博\" },\r\n        { time: \"09:00\", value: 180, type: \"微博\" },\r\n        { time: \"10:00\", value: 160, type: \"微博\" },\r\n        { time: \"11:00\", value: 140, type: \"微博\" },\r\n        { time: \"12:00\", value: 120, type: \"微博\" },\r\n        { time: \"13:00\", value: 130, type: \"微博\" },\r\n        { time: \"14:00\", value: 150, type: \"微博\" },\r\n        { time: \"15:00\", value: 140, type: \"微博\" },\r\n        { time: \"16:00\", value: 130, type: \"微博\" },\r\n        { time: \"17:00\", value: 150, type: \"微博\" },\r\n        { time: \"18:00\", value: 170, type: \"微博\" },\r\n        { time: \"19:00\", value: 160, type: \"微博\" },\r\n        { time: \"20:00\", value: 150, type: \"微博\" },\r\n        { time: \"21:00\", value: 130, type: \"微博\" },\r\n        { time: \"22:00\", value: 120, type: \"微博\" },\r\n        { time: \"23:00\", value: 100, type: \"微博\" },\r\n\r\n        { time: \"00:00\", value: 80, type: \"视频\" },\r\n        { time: \"01:00\", value: 70, type: \"视频\" },\r\n        { time: \"02:00\", value: 60, type: \"视频\" },\r\n        { time: \"03:00\", value: 50, type: \"视频\" },\r\n        { time: \"04:00\", value: 40, type: \"视频\" },\r\n        { time: \"05:00\", value: 50, type: \"视频\" },\r\n        { time: \"06:00\", value: 60, type: \"视频\" },\r\n        { time: \"07:00\", value: 80, type: \"视频\" },\r\n        { time: \"08:00\", value: 100, type: \"视频\" },\r\n        { time: \"09:00\", value: 120, type: \"视频\" },\r\n        { time: \"10:00\", value: 110, type: \"视频\" },\r\n        { time: \"11:00\", value: 100, type: \"视频\" },\r\n        { time: \"12:00\", value: 90, type: \"视频\" },\r\n        { time: \"13:00\", value: 100, type: \"视频\" },\r\n        { time: \"14:00\", value: 110, type: \"视频\" },\r\n        { time: \"15:00\", value: 100, type: \"视频\" },\r\n        { time: \"16:00\", value: 90, type: \"视频\" },\r\n        { time: \"17:00\", value: 100, type: \"视频\" },\r\n        { time: \"18:00\", value: 110, type: \"视频\" },\r\n        { time: \"19:00\", value: 100, type: \"视频\" },\r\n        { time: \"20:00\", value: 90, type: \"视频\" },\r\n        { time: \"21:00\", value: 80, type: \"视频\" },\r\n        { time: \"22:00\", value: 70, type: \"视频\" },\r\n        { time: \"23:00\", value: 60, type: \"视频\" },\r\n      ],\r\n      trendScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 平台来源占比数据\r\n      platformData: [\r\n        { type: '微博', percent: 31.2 },\r\n        { type: '视频', percent: 17.9 },\r\n        { type: '头条号', percent: 15.3 },\r\n        { type: 'APP', percent: 12.7 },\r\n        { type: '微信', percent: 9.8 },\r\n        { type: '其他', percent: 13.1 },\r\n      ],\r\n      platformScale: [\r\n        {\r\n          dataKey: 'percent',\r\n          min: 0,\r\n          formatter: '.0%',\r\n        },\r\n      ],\r\n      labelConfig: {\r\n        offset: -20,\r\n        textStyle: {\r\n          fill: '#000',\r\n          fontSize: 12,\r\n          fontWeight: 'bold',\r\n        },\r\n        formatter: (text, item) => {\r\n          return `${item.point.type}: ${item.point.percent}%`;\r\n        },\r\n      },\r\n\r\n      // 情感分布占比数据\r\n      sentimentData: [\r\n        { time: '00:00', value: 300, type: '中性' },\r\n        { time: '02:00', value: 280, type: '中性' },\r\n        { time: '04:00', value: 250, type: '中性' },\r\n        { time: '06:00', value: 260, type: '中性' },\r\n        { time: '08:00', value: 280, type: '中性' },\r\n        { time: '10:00', value: 300, type: '中性' },\r\n        { time: '12:00', value: 280, type: '中性' },\r\n        { time: '14:00', value: 250, type: '中性' },\r\n        { time: '16:00', value: 260, type: '中性' },\r\n        { time: '18:00', value: 280, type: '中性' },\r\n        { time: '20:00', value: 300, type: '中性' },\r\n        { time: '22:00', value: 280, type: '中性' },\r\n\r\n        { time: '00:00', value: 100, type: '正面' },\r\n        { time: '02:00', value: 120, type: '正面' },\r\n        { time: '04:00', value: 140, type: '正面' },\r\n        { time: '06:00', value: 130, type: '正面' },\r\n        { time: '08:00', value: 120, type: '正面' },\r\n        { time: '10:00', value: 100, type: '正面' },\r\n        { time: '12:00', value: 120, type: '正面' },\r\n        { time: '14:00', value: 140, type: '正面' },\r\n        { time: '16:00', value: 130, type: '正面' },\r\n        { time: '18:00', value: 120, type: '正面' },\r\n        { time: '20:00', value: 100, type: '正面' },\r\n        { time: '22:00', value: 120, type: '正面' },\r\n\r\n        { time: '00:00', value: 50, type: '负面' },\r\n        { time: '02:00', value: 60, type: '负面' },\r\n        { time: '04:00', value: 70, type: '负面' },\r\n        { time: '06:00', value: 65, type: '负面' },\r\n        { time: '08:00', value: 60, type: '负面' },\r\n        { time: '10:00', value: 50, type: '负面' },\r\n        { time: '12:00', value: 60, type: '负面' },\r\n        { time: '14:00', value: 70, type: '负面' },\r\n        { time: '16:00', value: 65, type: '负面' },\r\n        { time: '18:00', value: 60, type: '负面' },\r\n        { time: '20:00', value: 50, type: '负面' },\r\n        { time: '22:00', value: 60, type: '负面' },\r\n      ],\r\n      sentimentScale: [\r\n        {\r\n          dataKey: 'value',\r\n          min: 0,\r\n        },\r\n      ],\r\n\r\n      // 热门文章列表\r\n      hotArticles: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器(Fotile)官方旗舰店 方太热水器(Fotile)-111 方太热水器(Fotile) 400.6808.655...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '电商平台',\r\n          publishTime: '2025-04-29 20:24:00',\r\n          link: 'https://example.com/article/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '近地铺30年地暖口碑，靠老字二全新设计住人。#成都二手房 #地暖电源',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/article/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '现在这个社会要靠脑袋吃饭，可能是一辈子的事，开玩笑 我一个实体人 更应该有品质。',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:29:09',\r\n          link: 'https://example.com/article/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '（无标题）S2 V b ## b ### 限定2人方太全球首创一代燃气灶全球首发 03 ALOQAV 0 13 图A-5986 59...',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 14:19:23',\r\n          link: 'https://example.com/article/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '你于过这事吗? 来看合了#方太官网 #南粤 #新桥口大厨的字写得 #同城姐妹的朋友看过来 #电影',\r\n          tag: '热门',\r\n          tagColor: '#f50',\r\n          source: '视频平台',\r\n          publishTime: '2025-04-29 12:48:04',\r\n          link: 'https://example.com/article/5',\r\n        },\r\n      ],\r\n\r\n      // 报告模板列表\r\n      reportTemplates: [\r\n        {\r\n          id: 1,\r\n          title: '舆情-周报表',\r\n          createTime: '2019-11-16 18:02:00',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '舆情-月报表',\r\n          createTime: '2019-11-18 18:06:52',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '舆情-季度表',\r\n          createTime: '2021-09-12 10:15:00',\r\n        },\r\n      ],\r\n\r\n      // 最新负面信息\r\n      negativeNews: [\r\n        {\r\n          id: 1,\r\n          title: '方太热水器出现质量问题，多名用户投诉无人处理',\r\n          publishTime: '2025-04-29 19:03:00',\r\n          link: 'https://example.com/negative/1',\r\n        },\r\n        {\r\n          id: 2,\r\n          title: '方太厨电安装电话无人接听，客户投诉服务态度差',\r\n          publishTime: '2025-04-29 18:22:43',\r\n          link: 'https://example.com/negative/2',\r\n        },\r\n        {\r\n          id: 3,\r\n          title: '航空女友偷拍日记被礼，网友后，我在床上看到了电视一幕的她',\r\n          publishTime: '2025-04-29 17:48:45',\r\n          link: 'https://example.com/negative/3',\r\n        },\r\n        {\r\n          id: 4,\r\n          title: '某品牌大型抽油烟机噪音问题引发用户不满',\r\n          publishTime: '2025-04-29 15:15:16',\r\n          link: 'https://example.com/negative/4',\r\n        },\r\n        {\r\n          id: 5,\r\n          title: '家电售后服务调查：多品牌服务质量参差不齐',\r\n          publishTime: '2025-04-29 12:26:04',\r\n          link: 'https://example.com/negative/5',\r\n        },\r\n      ],\r\n\r\n      loading: true\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 隐藏顶部导航栏\r\n    this.originalTopNav = this.$store.state.settings.topNav\r\n    this.$store.dispatch('settings/changeSetting', {\r\n      key: 'topNav',\r\n      value: false\r\n    })\r\n\r\n    setTimeout(() => {\r\n      this.loading = false;\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    // 恢复顶部导航栏设置\r\n    if (this.originalTopNav !== undefined) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'topNav',\r\n        value: this.originalTopNav\r\n      })\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n  <style lang=\"less\" scoped>\r\n@import \"./Workplace.less\";\r\n\r\n\r\n\r\n.page-header-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.setting-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.switch-buttons {\r\n  margin-top: 16px;\r\n}\r\n\r\n.ant-list-item {\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    background-color: rgba(24, 144, 255, 0.05);\r\n  }\r\n}\r\n\r\n.ant-card {\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  transition: all 0.3s;\r\n\r\n  &:hover {\r\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.ant-tag {\r\n  margin-right: 0;\r\n}\r\n\r\n.ant-list-item-meta-title {\r\n  margin-bottom: 4px;\r\n\r\n  a {\r\n    color: rgba(0, 0, 0, 0.85);\r\n\r\n    &:hover {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n}\r\n\r\n.ant-list-item-meta-description {\r\n  color: rgba(0, 0, 0, 0.45);\r\n}\r\n\r\n.mobile {\r\n  .more-info {\r\n    border: 0;\r\n    padding-top: 16px;\r\n    margin: 16px 0 16px;\r\n  }\r\n\r\n  .headerContent .title .welcome-text {\r\n    display: none;\r\n  }\r\n}\r\n</style>\r\n"]}]}