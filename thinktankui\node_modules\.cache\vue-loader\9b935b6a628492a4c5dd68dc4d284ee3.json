{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1749109381330}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgSW5uZXJMaW5rIGZyb20gIi4uL0lubmVyTGluay9pbmRleCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogeyBJbm5lckxpbmsgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpZnJhbWVWaWV3cygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy5pZnJhbWVWaWV3czsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpZnJhbWVVcmwodXJsLCBxdWVyeSkgew0KICAgICAgaWYgKE9iamVjdC5rZXlzKHF1ZXJ5KS5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBwYXJhbXMgPSBPYmplY3Qua2V5cyhxdWVyeSkubWFwKChrZXkpID0+IGtleSArICI9IiArIHF1ZXJ5W2tleV0pLmpvaW4oIiYiKTsNCiAgICAgICAgcmV0dXJuIHVybCArICI/IiArIHBhcmFtczsNCiAgICAgIH0NCiAgICAgIHJldHVybiB1cmw7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/IframeToggle", "sourcesContent": ["<template>\r\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\r\n    <inner-link\r\n      v-for=\"(item, index) in iframeViews\"\r\n      :key=\"item.path\"\r\n      :iframeId=\"'iframe' + index\"\r\n      v-show=\"$route.path === item.path\"\r\n      :src=\"iframeUrl(item.meta.link, item.query)\"\r\n    ></inner-link>\r\n  </transition-group>\r\n</template>\r\n\r\n<script>\r\nimport InnerLink from \"../InnerLink/index\";\r\n\r\nexport default {\r\n  components: { InnerLink },\r\n  computed: {\r\n    iframeViews() {\r\n      return this.$store.state.tagsView.iframeViews;\r\n    }\r\n  },\r\n  methods: {\r\n    iframeUrl(url, query) {\r\n      if (Object.keys(query).length > 0) {\r\n        let params = Object.keys(query).map((key) => key + \"=\" + query[key]).join(\"&\");\r\n        return url + \"?\" + params;\r\n      }\r\n      return url;\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}