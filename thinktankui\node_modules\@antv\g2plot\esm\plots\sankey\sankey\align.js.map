{"version": 3, "file": "align.js", "sourceRoot": "", "sources": ["../../../../src/plots/sankey/sankey/align.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAEnC,SAAS,WAAW,CAAC,CAAC;IACpB,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,IAAI,CAAC,IAAI;IACvB,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,IAAI,EAAE,CAAC;IAC3B,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,IAAI,EAAE,CAAC;IAC7B,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,IAAI;IACzB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvH,CAAC", "sourcesContent": ["import { minBy } from '@antv/util';\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth : node.sourceLinks.length ? minBy(node.sourceLinks, targetDepth) - 1 : 0;\n}\n"]}