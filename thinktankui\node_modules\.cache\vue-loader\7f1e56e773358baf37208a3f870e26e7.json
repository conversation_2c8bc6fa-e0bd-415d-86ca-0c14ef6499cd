{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\views\\tool\\build\\RightPanel.vue", "mtime": 1749109381356}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpc0FycmF5IH0gZnJvbSAndXRpbCcNCmltcG9ydCBkcmFnZ2FibGUgZnJvbSAndnVlZHJhZ2dhYmxlJw0KaW1wb3J0IFRyZWVOb2RlRGlhbG9nIGZyb20gJy4vVHJlZU5vZGVEaWFsb2cnDQppbXBvcnQgeyBpc051bWJlclN0ciB9IGZyb20gJ0AvdXRpbHMvaW5kZXgnDQppbXBvcnQgSWNvbnNEaWFsb2cgZnJvbSAnLi9JY29uc0RpYWxvZycNCmltcG9ydCB7DQogIGlucHV0Q29tcG9uZW50cywNCiAgc2VsZWN0Q29tcG9uZW50cywNCiAgbGF5b3V0Q29tcG9uZW50cw0KfSBmcm9tICdAL3V0aWxzL2dlbmVyYXRvci9jb25maWcnDQoNCmNvbnN0IGRhdGVUaW1lRm9ybWF0ID0gew0KICBkYXRlOiAneXl5eS1NTS1kZCcsDQogIHdlZWs6ICd5eXl5IOesrCBXVyDlkagnLA0KICBtb250aDogJ3l5eXktTU0nLA0KICB5ZWFyOiAneXl5eScsDQogIGRhdGV0aW1lOiAneXl5eS1NTS1kZCBISDptbTpzcycsDQogIGRhdGVyYW5nZTogJ3l5eXktTU0tZGQnLA0KICBtb250aHJhbmdlOiAneXl5eS1NTScsDQogIGRhdGV0aW1lcmFuZ2U6ICd5eXl5LU1NLWRkIEhIOm1tOnNzJw0KfQ0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBkcmFnZ2FibGUsDQogICAgVHJlZU5vZGVEaWFsb2csDQogICAgSWNvbnNEaWFsb2cNCiAgfSwNCiAgcHJvcHM6IFsnc2hvd0ZpZWxkJywgJ2FjdGl2ZURhdGEnLCAnZm9ybUNvbmYnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgY3VycmVudFRhYjogJ2ZpZWxkJywNCiAgICAgIGN1cnJlbnROb2RlOiBudWxsLA0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBpY29uc1Zpc2libGU6IGZhbHNlLA0KICAgICAgY3VycmVudEljb25Nb2RlbDogbnVsbCwNCiAgICAgIGRhdGVUeXBlT3B0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfml6UoZGF0ZSknLA0KICAgICAgICAgIHZhbHVlOiAnZGF0ZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5ZGoKHdlZWspJywNCiAgICAgICAgICB2YWx1ZTogJ3dlZWsnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aciChtb250aCknLA0KICAgICAgICAgIHZhbHVlOiAnbW9udGgnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+W5tCh5ZWFyKScsDQogICAgICAgICAgdmFsdWU6ICd5ZWFyJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfml6XmnJ/ml7bpl7QoZGF0ZXRpbWUpJywNCiAgICAgICAgICB2YWx1ZTogJ2RhdGV0aW1lJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgZGF0ZVJhbmdlVHlwZU9wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5pel5pyf6IyD5Zu0KGRhdGVyYW5nZSknLA0KICAgICAgICAgIHZhbHVlOiAnZGF0ZXJhbmdlJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfmnIjojIPlm7QobW9udGhyYW5nZSknLA0KICAgICAgICAgIHZhbHVlOiAnbW9udGhyYW5nZScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5pel5pyf5pe26Ze06IyD5Zu0KGRhdGV0aW1lcmFuZ2UpJywNCiAgICAgICAgICB2YWx1ZTogJ2RhdGV0aW1lcmFuZ2UnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBjb2xvckZvcm1hdE9wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAnaGV4JywNCiAgICAgICAgICB2YWx1ZTogJ2hleCcNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAncmdiJywNCiAgICAgICAgICB2YWx1ZTogJ3JnYicNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAncmdiYScsDQogICAgICAgICAgdmFsdWU6ICdyZ2JhJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICdoc3YnLA0KICAgICAgICAgIHZhbHVlOiAnaHN2Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICdoc2wnLA0KICAgICAgICAgIHZhbHVlOiAnaHNsJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAganVzdGlmeU9wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAnc3RhcnQnLA0KICAgICAgICAgIHZhbHVlOiAnc3RhcnQnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ2VuZCcsDQogICAgICAgICAgdmFsdWU6ICdlbmQnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ2NlbnRlcicsDQogICAgICAgICAgdmFsdWU6ICdjZW50ZXInDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ3NwYWNlLWFyb3VuZCcsDQogICAgICAgICAgdmFsdWU6ICdzcGFjZS1hcm91bmQnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ3NwYWNlLWJldHdlZW4nLA0KICAgICAgICAgIHZhbHVlOiAnc3BhY2UtYmV0d2VlbicNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGxheW91dFRyZWVQcm9wczogew0KICAgICAgICBsYWJlbChkYXRhLCBub2RlKSB7DQogICAgICAgICAgcmV0dXJuIGRhdGEuY29tcG9uZW50TmFtZSB8fCBgJHtkYXRhLmxhYmVsfTogJHtkYXRhLnZNb2RlbH1gDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgZG9jdW1lbnRMaW5rKCkgew0KICAgICAgcmV0dXJuICgNCiAgICAgICAgdGhpcy5hY3RpdmVEYXRhLmRvY3VtZW50DQogICAgICAgIHx8ICdodHRwczovL2VsZW1lbnQuZWxlbWUuY24vIy96aC1DTi9jb21wb25lbnQvaW5zdGFsbGF0aW9uJw0KICAgICAgKQ0KICAgIH0sDQogICAgZGF0ZU9wdGlvbnMoKSB7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMuYWN0aXZlRGF0YS50eXBlICE9PSB1bmRlZmluZWQNCiAgICAgICAgJiYgdGhpcy5hY3RpdmVEYXRhLnRhZyA9PT0gJ2VsLWRhdGUtcGlja2VyJw0KICAgICAgKSB7DQogICAgICAgIGlmICh0aGlzLmFjdGl2ZURhdGFbJ3N0YXJ0LXBsYWNlaG9sZGVyJ10gPT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIHJldHVybiB0aGlzLmRhdGVUeXBlT3B0aW9ucw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLmRhdGVSYW5nZVR5cGVPcHRpb25zDQogICAgICB9DQogICAgICByZXR1cm4gW10NCiAgICB9LA0KICAgIHRhZ0xpc3QoKSB7DQogICAgICByZXR1cm4gWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfovpPlhaXlnovnu4Tku7YnLA0KICAgICAgICAgIG9wdGlvbnM6IGlucHV0Q29tcG9uZW50cw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfpgInmi6nlnovnu4Tku7YnLA0KICAgICAgICAgIG9wdGlvbnM6IHNlbGVjdENvbXBvbmVudHMNCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFkZFJlZygpIHsNCiAgICAgIHRoaXMuYWN0aXZlRGF0YS5yZWdMaXN0LnB1c2goew0KICAgICAgICBwYXR0ZXJuOiAnJywNCiAgICAgICAgbWVzc2FnZTogJycNCiAgICAgIH0pDQogICAgfSwNCiAgICBhZGRTZWxlY3RJdGVtKCkgew0KICAgICAgdGhpcy5hY3RpdmVEYXRhLm9wdGlvbnMucHVzaCh7DQogICAgICAgIGxhYmVsOiAnJywNCiAgICAgICAgdmFsdWU6ICcnDQogICAgICB9KQ0KICAgIH0sDQogICAgYWRkVHJlZUl0ZW0oKSB7DQogICAgICArK3RoaXMuaWRHbG9iYWwNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSB0aGlzLmFjdGl2ZURhdGEub3B0aW9ucw0KICAgIH0sDQogICAgcmVuZGVyQ29udGVudChoLCB7IG5vZGUsIGRhdGEsIHN0b3JlIH0pIHsNCiAgICAgIHJldHVybiAoDQogICAgICAgIDxkaXYgY2xhc3M9ImN1c3RvbS10cmVlLW5vZGUiPg0KICAgICAgICAgIDxzcGFuPntub2RlLmxhYmVsfTwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0ibm9kZS1vcGVyYXRpb24iPg0KICAgICAgICAgICAgPGkgb24tY2xpY2s9eygpID0+IHRoaXMuYXBwZW5kKGRhdGEpfQ0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1wbHVzIg0KICAgICAgICAgICAgICB0aXRsZT0i5re75YqgIg0KICAgICAgICAgICAgPjwvaT4NCiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLnJlbW92ZShub2RlLCBkYXRhKX0NCiAgICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tZGVsZXRlIg0KICAgICAgICAgICAgICB0aXRsZT0i5Yig6ZmkIg0KICAgICAgICAgICAgPjwvaT4NCiAgICAgICAgICA8L3NwYW4+DQogICAgICAgIDwvZGl2Pg0KICAgICAgKQ0KICAgIH0sDQogICAgYXBwZW5kKGRhdGEpIHsNCiAgICAgIGlmICghZGF0YS5jaGlsZHJlbikgew0KICAgICAgICB0aGlzLiRzZXQoZGF0YSwgJ2NoaWxkcmVuJywgW10pDQogICAgICB9DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLmN1cnJlbnROb2RlID0gZGF0YS5jaGlsZHJlbg0KICAgIH0sDQogICAgcmVtb3ZlKG5vZGUsIGRhdGEpIHsNCiAgICAgIGNvbnN0IHsgcGFyZW50IH0gPSBub2RlDQogICAgICBjb25zdCBjaGlsZHJlbiA9IHBhcmVudC5kYXRhLmNoaWxkcmVuIHx8IHBhcmVudC5kYXRhDQogICAgICBjb25zdCBpbmRleCA9IGNoaWxkcmVuLmZpbmRJbmRleChkID0+IGQuaWQgPT09IGRhdGEuaWQpDQogICAgICBjaGlsZHJlbi5zcGxpY2UoaW5kZXgsIDEpDQogICAgfSwNCiAgICBhZGROb2RlKGRhdGEpIHsNCiAgICAgIHRoaXMuY3VycmVudE5vZGUucHVzaChkYXRhKQ0KICAgIH0sDQogICAgc2V0T3B0aW9uVmFsdWUoaXRlbSwgdmFsKSB7DQogICAgICBpdGVtLnZhbHVlID0gaXNOdW1iZXJTdHIodmFsKSA/ICt2YWwgOiB2YWwNCiAgICB9LA0KICAgIHNldERlZmF1bHRWYWx1ZSh2YWwpIHsNCiAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbCkpIHsNCiAgICAgICAgcmV0dXJuIHZhbC5qb2luKCcsJykNCiAgICAgIH0NCiAgICAgIGlmIChbJ3N0cmluZycsICdudW1iZXInXS5pbmRleE9mKHZhbCkgPiAtMSkgew0KICAgICAgICByZXR1cm4gdmFsDQogICAgICB9DQogICAgICBpZiAodHlwZW9mIHZhbCA9PT0gJ2Jvb2xlYW4nKSB7DQogICAgICAgIHJldHVybiBgJHt2YWx9YA0KICAgICAgfQ0KICAgICAgcmV0dXJuIHZhbA0KICAgIH0sDQogICAgb25EZWZhdWx0VmFsdWVJbnB1dChzdHIpIHsNCiAgICAgIGlmIChpc0FycmF5KHRoaXMuYWN0aXZlRGF0YS5kZWZhdWx0VmFsdWUpKSB7DQogICAgICAgIC8vIOaVsOe7hA0KICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLA0KICAgICAgICAgICdkZWZhdWx0VmFsdWUnLA0KICAgICAgICAgIHN0ci5zcGxpdCgnLCcpLm1hcCh2YWwgPT4gKGlzTnVtYmVyU3RyKHZhbCkgPyArdmFsIDogdmFsKSkNCiAgICAgICAgKQ0KICAgICAgfSBlbHNlIGlmIChbJ3RydWUnLCAnZmFsc2UnXS5pbmRleE9mKHN0cikgPiAtMSkgew0KICAgICAgICAvLyDluIPlsJQNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YSwgJ2RlZmF1bHRWYWx1ZScsIEpTT04ucGFyc2Uoc3RyKSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWtl+espuS4suWSjOaVsOWtlw0KICAgICAgICB0aGlzLiRzZXQoDQogICAgICAgICAgdGhpcy5hY3RpdmVEYXRhLA0KICAgICAgICAgICdkZWZhdWx0VmFsdWUnLA0KICAgICAgICAgIGlzTnVtYmVyU3RyKHN0cikgPyArc3RyIDogc3RyDQogICAgICAgICkNCiAgICAgIH0NCiAgICB9LA0KICAgIG9uU3dpdGNoVmFsdWVJbnB1dCh2YWwsIG5hbWUpIHsNCiAgICAgIGlmIChbJ3RydWUnLCAnZmFsc2UnXS5pbmRleE9mKHZhbCkgPiAtMSkgew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCBuYW1lLCBKU09OLnBhcnNlKHZhbCkpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCBuYW1lLCBpc051bWJlclN0cih2YWwpID8gK3ZhbCA6IHZhbCkNCiAgICAgIH0NCiAgICB9LA0KICAgIHNldFRpbWVWYWx1ZSh2YWwsIHR5cGUpIHsNCiAgICAgIGNvbnN0IHZhbHVlRm9ybWF0ID0gdHlwZSA9PT0gJ3dlZWsnID8gZGF0ZVRpbWVGb3JtYXQuZGF0ZSA6IHZhbA0KICAgICAgdGhpcy4kc2V0KHRoaXMuYWN0aXZlRGF0YSwgJ2RlZmF1bHRWYWx1ZScsIG51bGwpDQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAndmFsdWUtZm9ybWF0JywgdmFsdWVGb3JtYXQpDQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAnZm9ybWF0JywgdmFsKQ0KICAgIH0sDQogICAgc3BhbkNoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybUNvbmYuc3BhbiA9IHZhbA0KICAgIH0sDQogICAgbXVsdGlwbGVDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAnZGVmYXVsdFZhbHVlJywgdmFsID8gW10gOiAnJykNCiAgICB9LA0KICAgIGRhdGVUeXBlQ2hhbmdlKHZhbCkgew0KICAgICAgdGhpcy5zZXRUaW1lVmFsdWUoZGF0ZVRpbWVGb3JtYXRbdmFsXSwgdmFsKQ0KICAgIH0sDQogICAgcmFuZ2VDaGFuZ2UodmFsKSB7DQogICAgICB0aGlzLiRzZXQoDQogICAgICAgIHRoaXMuYWN0aXZlRGF0YSwNCiAgICAgICAgJ2RlZmF1bHRWYWx1ZScsDQogICAgICAgIHZhbCA/IFt0aGlzLmFjdGl2ZURhdGEubWluLCB0aGlzLmFjdGl2ZURhdGEubWF4XSA6IHRoaXMuYWN0aXZlRGF0YS5taW4NCiAgICAgICkNCiAgICB9LA0KICAgIHJhdGVUZXh0Q2hhbmdlKHZhbCkgew0KICAgICAgaWYgKHZhbCkgdGhpcy5hY3RpdmVEYXRhWydzaG93LXNjb3JlJ10gPSBmYWxzZQ0KICAgIH0sDQogICAgcmF0ZVNjb3JlQ2hhbmdlKHZhbCkgew0KICAgICAgaWYgKHZhbCkgdGhpcy5hY3RpdmVEYXRhWydzaG93LXRleHQnXSA9IGZhbHNlDQogICAgfSwNCiAgICBjb2xvckZvcm1hdENoYW5nZSh2YWwpIHsNCiAgICAgIHRoaXMuYWN0aXZlRGF0YS5kZWZhdWx0VmFsdWUgPSBudWxsDQogICAgICB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctYWxwaGEnXSA9IHZhbC5pbmRleE9mKCdhJykgPiAtMQ0KICAgICAgdGhpcy5hY3RpdmVEYXRhLnJlbmRlcktleSA9ICtuZXcgRGF0ZSgpIC8vIOabtOaWsHJlbmRlcktleSzph43mlrDmuLLmn5Por6Xnu4Tku7YNCiAgICB9LA0KICAgIG9wZW5JY29uc0RpYWxvZyhtb2RlbCkgew0KICAgICAgdGhpcy5pY29uc1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLmN1cnJlbnRJY29uTW9kZWwgPSBtb2RlbA0KICAgIH0sDQogICAgc2V0SWNvbih2YWwpIHsNCiAgICAgIHRoaXMuYWN0aXZlRGF0YVt0aGlzLmN1cnJlbnRJY29uTW9kZWxdID0gdmFsDQogICAgfSwNCiAgICB0YWdDaGFuZ2UodGFnSWNvbikgew0KICAgICAgbGV0IHRhcmdldCA9IGlucHV0Q29tcG9uZW50cy5maW5kKGl0ZW0gPT4gaXRlbS50YWdJY29uID09PSB0YWdJY29uKQ0KICAgICAgaWYgKCF0YXJnZXQpIHRhcmdldCA9IHNlbGVjdENvbXBvbmVudHMuZmluZChpdGVtID0+IGl0ZW0udGFnSWNvbiA9PT0gdGFnSWNvbikNCiAgICAgIHRoaXMuJGVtaXQoJ3RhZy1jaGFuZ2UnLCB0YXJnZXQpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["RightPanel.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8jBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RightPanel.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\r\n  <div class=\"right-board\">\r\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\r\n      <el-tab-pane label=\"组件属性\" name=\"field\" />\r\n      <el-tab-pane label=\"表单属性\" name=\"form\" />\r\n    </el-tabs>\r\n    <div class=\"field-box\">\r\n      <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\r\n        <i class=\"el-icon-link\" />\r\n      </a>\r\n      <el-scrollbar class=\"right-scrollbar\">\r\n        <!-- 组件属性 -->\r\n        <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\r\n          <el-form-item v-if=\"activeData.changeTag\" label=\"组件类型\">\r\n            <el-select\r\n              v-model=\"activeData.tagIcon\"\r\n              placeholder=\"请选择组件类型\"\r\n              :style=\"{width: '100%'}\"\r\n              @change=\"tagChange\"\r\n            >\r\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\r\n                <el-option\r\n                  v-for=\"item in group.options\"\r\n                  :key=\"item.label\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.tagIcon\"\r\n                >\r\n                  <svg-icon class=\"node-icon\" :icon-class=\"item.tagIcon\" />\r\n                  <span> {{ item.label }}</span>\r\n                </el-option>\r\n              </el-option-group>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"字段名\">\r\n            <el-input v-model=\"activeData.vModel\" placeholder=\"请输入字段名（v-model）\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.componentName!==undefined\" label=\"组件名\">\r\n            {{ activeData.componentName }}\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.label!==undefined\" label=\"标题\">\r\n            <el-input v-model=\"activeData.label\" placeholder=\"请输入标题\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\r\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\r\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\r\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.span!==undefined\" label=\"表单栅格\">\r\n            <el-slider v-model=\"activeData.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"栅格间隔\">\r\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"布局模式\">\r\n            <el-radio-group v-model=\"activeData.type\">\r\n              <el-radio-button label=\"default\" />\r\n              <el-radio-button label=\"flex\" />\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\r\n            <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\r\n              <el-option\r\n                v-for=\"(item, index) in justifyOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\r\n            <el-radio-group v-model=\"activeData.align\">\r\n              <el-radio-button label=\"top\" />\r\n              <el-radio-button label=\"middle\" />\r\n              <el-radio-button label=\"bottom\" />\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.labelWidth!==undefined\" label=\"标签宽度\">\r\n            <el-input v-model.number=\"activeData.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\r\n            <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"默认值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData.defaultValue)\"\r\n              placeholder=\"请输入默认值\"\r\n              @input=\"onDefaultValueInput\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"至少应选\">\r\n            <el-input-number\r\n              :value=\"activeData.min\"\r\n              :min=\"0\"\r\n              placeholder=\"至少应选\"\r\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"最多可选\">\r\n            <el-input-number\r\n              :value=\"activeData.max\"\r\n              :min=\"0\"\r\n              placeholder=\"最多可选\"\r\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.prepend!==undefined\" label=\"前缀\">\r\n            <el-input v-model=\"activeData.prepend\" placeholder=\"请输入前缀\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.append!==undefined\" label=\"后缀\">\r\n            <el-input v-model=\"activeData.append\" placeholder=\"请输入后缀\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\r\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\r\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"选项分隔符\">\r\n            <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\r\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\r\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.min !== undefined\" label=\"最小值\">\r\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.max !== undefined\" label=\"最大值\">\r\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.step !== undefined\" label=\"步长\">\r\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"精度\">\r\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"按钮位置\">\r\n            <el-radio-group v-model=\"activeData['controls-position']\">\r\n              <el-radio-button label=\"\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右侧\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\r\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\r\n              <template slot=\"append\">\r\n                个字符\r\n              </template>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\r\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\r\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['active-value'])\"\r\n              placeholder=\"请输入开启值\"\r\n              @input=\"onSwitchValueInput($event, 'active-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\r\n              placeholder=\"请输入关闭值\"\r\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.tag\"\r\n            label=\"时间类型\"\r\n          >\r\n            <el-select\r\n              v-model=\"activeData.type\"\r\n              placeholder=\"请选择时间类型\"\r\n              :style=\"{ width: '100%' }\"\r\n              @change=\"dateTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in dateOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\r\n            <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.accept !== undefined\" label=\"文件类型\">\r\n            <el-select\r\n              v-model=\"activeData.accept\"\r\n              placeholder=\"请选择文件类型\"\r\n              :style=\"{ width: '100%' }\"\r\n              clearable\r\n            >\r\n              <el-option label=\"图片\" value=\"image/*\" />\r\n              <el-option label=\"视频\" value=\"video/*\" />\r\n              <el-option label=\"音频\" value=\"audio/*\" />\r\n              <el-option label=\"excel\" value=\".xls,.xlsx\" />\r\n              <el-option label=\"word\" value=\".doc,.docx\" />\r\n              <el-option label=\"pdf\" value=\".pdf\" />\r\n              <el-option label=\"txt\" value=\".txt\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.fileSize !== undefined\" label=\"文件大小\">\r\n            <el-input v-model.number=\"activeData.fileSize\" placeholder=\"请输入文件大小\">\r\n              <el-select slot=\"append\" v-model=\"activeData.sizeUnit\" :style=\"{ width: '66px' }\">\r\n                <el-option label=\"KB\" value=\"KB\" />\r\n                <el-option label=\"MB\" value=\"MB\" />\r\n                <el-option label=\"GB\" value=\"GB\" />\r\n              </el-select>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\r\n            <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\r\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\r\n              <el-radio-button label=\"text\">\r\n                text\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture\">\r\n                picture\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture-card\">\r\n                picture-card\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.buttonText !== undefined\"\r\n            v-show=\"'picture-card' !== activeData['list-type']\"\r\n            label=\"按钮文字\"\r\n          >\r\n            <el-input v-model=\"activeData.buttonText\" placeholder=\"请输入按钮文字\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\r\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\r\n            <el-input\r\n              v-model=\"activeData['picker-options'].selectableRange\"\r\n              placeholder=\"请输入时间段\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\r\n            <el-input\r\n              :value=\"activeData.format\"\r\n              placeholder=\"请输入时间格式\"\r\n              @input=\"setTimeValue($event)\"\r\n            />\r\n          </el-form-item>\r\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <draggable\r\n              :list=\"activeData.options\"\r\n              :animation=\"340\"\r\n              group=\"selectItem\"\r\n              handle=\".option-drag\"\r\n            >\r\n              <div v-for=\"(item, index) in activeData.options\" :key=\"index\" class=\"select-item\">\r\n                <div class=\"select-line-icon option-drag\">\r\n                  <i class=\"el-icon-s-operation\" />\r\n                </div>\r\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\r\n                <el-input\r\n                  placeholder=\"选项值\"\r\n                  size=\"small\"\r\n                  :value=\"item.value\"\r\n                  @input=\"setOptionValue(item, $event)\"\r\n                />\r\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.options.splice(index, 1)\">\r\n                  <i class=\"el-icon-remove-outline\" />\r\n                </div>\r\n              </div>\r\n            </draggable>\r\n            <div style=\"margin-left: 20px;\">\r\n              <el-button\r\n                style=\"padding-bottom: 0\"\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                type=\"text\"\r\n                @click=\"addSelectItem\"\r\n              >\r\n                添加选项\r\n              </el-button>\r\n            </div>\r\n            <el-divider />\r\n          </template>\r\n\r\n          <template v-if=\"['el-cascader'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <el-form-item label=\"数据类型\">\r\n              <el-radio-group v-model=\"activeData.dataType\" size=\"small\">\r\n                <el-radio-button label=\"dynamic\">\r\n                  动态数据\r\n                </el-radio-button>\r\n                <el-radio-button label=\"static\">\r\n                  静态数据\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <template v-if=\"activeData.dataType === 'dynamic'\">\r\n              <el-form-item label=\"标签键名\">\r\n                <el-input v-model=\"activeData.labelKey\" placeholder=\"请输入标签键名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"值键名\">\r\n                <el-input v-model=\"activeData.valueKey\" placeholder=\"请输入值键名\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"子级键名\">\r\n                <el-input v-model=\"activeData.childrenKey\" placeholder=\"请输入子级键名\" />\r\n              </el-form-item>\r\n            </template>\r\n\r\n            <el-tree\r\n              v-if=\"activeData.dataType === 'static'\"\r\n              draggable\r\n              :data=\"activeData.options\"\r\n              node-key=\"id\"\r\n              :expand-on-click-node=\"false\"\r\n              :render-content=\"renderContent\"\r\n            />\r\n            <div v-if=\"activeData.dataType === 'static'\" style=\"margin-left: 20px\">\r\n              <el-button\r\n                style=\"padding-bottom: 0\"\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                type=\"text\"\r\n                @click=\"addTreeItem\"\r\n              >\r\n                添加父级\r\n              </el-button>\r\n            </div>\r\n            <el-divider />\r\n          </template>\r\n\r\n          <el-form-item v-if=\"activeData.optionType !== undefined\" label=\"选项样式\">\r\n            <el-radio-group v-model=\"activeData.optionType\">\r\n              <el-radio-button label=\"default\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"button\">\r\n                按钮\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\r\n            <el-color-picker v-model=\"activeData['active-color']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\r\n            <el-color-picker v-model=\"activeData['inactive-color']\" />\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\r\n            <el-switch v-model=\"activeData['allow-half']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\r\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\r\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\r\n            <el-switch v-model=\"activeData['show-stops']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\r\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.border !== undefined && activeData.optionType === 'default'\"\r\n            label=\"是否带边框\"\r\n          >\r\n            <el-switch v-model=\"activeData.border\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-color-picker'\" label=\"颜色格式\">\r\n            <el-select\r\n              v-model=\"activeData['color-format']\"\r\n              placeholder=\"请选择颜色格式\"\r\n              :style=\"{ width: '100%' }\"\r\n              @change=\"colorFormatChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in colorFormatOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.size !== undefined &&\r\n              (activeData.optionType === 'button' ||\r\n                activeData.border ||\r\n                activeData.tag === 'el-color-picker')\"\r\n            label=\"选项尺寸\"\r\n          >\r\n            <el-radio-group v-model=\"activeData.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\r\n            <el-switch v-model=\"activeData['show-word-limit']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"严格步数\">\r\n            <el-switch v-model=\"activeData['step-strictly']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.props.props.multiple\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"展示全路径\">\r\n            <el-switch v-model=\"activeData['show-all-levels']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"可否筛选\">\r\n            <el-switch v-model=\"activeData.filterable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\r\n            <el-switch v-model=\"activeData.clearable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.showTip !== undefined\" label=\"显示提示\">\r\n            <el-switch v-model=\"activeData.showTip\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.multiple !== undefined\" label=\"多选文件\">\r\n            <el-switch v-model=\"activeData.multiple\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\r\n            <el-switch v-model=\"activeData['auto-upload']\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\r\n            <el-switch v-model=\"activeData.readonly\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\r\n            <el-switch v-model=\"activeData.disabled\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否可搜索\">\r\n            <el-switch v-model=\"activeData.filterable\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.required !== undefined\" label=\"是否必填\">\r\n            <el-switch v-model=\"activeData.required\" />\r\n          </el-form-item>\r\n\r\n          <template v-if=\"activeData.layoutTree\">\r\n            <el-divider>布局结构树</el-divider>\r\n            <el-tree\r\n              :data=\"[activeData]\"\r\n              :props=\"layoutTreeProps\"\r\n              node-key=\"renderKey\"\r\n              default-expand-all\r\n              draggable\r\n            >\r\n              <span slot-scope=\"{ node, data }\">\r\n                <span class=\"node-label\">\r\n                  <svg-icon class=\"node-icon\" :icon-class=\"data.tagIcon\" />\r\n                  {{ node.label }}\r\n                </span>\r\n              </span>\r\n            </el-tree>\r\n          </template>\r\n\r\n          <template v-if=\"activeData.layout === 'colFormItem' && activeData.tag !== 'el-button'\">\r\n            <el-divider>正则校验</el-divider>\r\n            <div\r\n              v-for=\"(item, index) in activeData.regList\"\r\n              :key=\"index\"\r\n              class=\"reg-item\"\r\n            >\r\n              <span class=\"close-btn\" @click=\"activeData.regList.splice(index, 1)\">\r\n                <i class=\"el-icon-close\" />\r\n              </span>\r\n              <el-form-item label=\"表达式\">\r\n                <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\r\n                <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\r\n              </el-form-item>\r\n            </div>\r\n            <div style=\"margin-left: 20px\">\r\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\r\n                添加规则\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-form>\r\n        <!-- 表单属性 -->\r\n        <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\r\n          <el-form-item label=\"表单名\">\r\n            <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单模型\">\r\n            <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"校验模型\">\r\n            <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单尺寸\">\r\n            <el-radio-group v-model=\"formConf.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签对齐\">\r\n            <el-radio-group v-model=\"formConf.labelPosition\">\r\n              <el-radio-button label=\"left\">\r\n                左对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"top\">\r\n                顶部对齐\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签宽度\">\r\n            <el-input-number v-model=\"formConf.labelWidth\" placeholder=\"标签宽度\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"栅格间隔\">\r\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"禁用表单\">\r\n            <el-switch v-model=\"formConf.disabled\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"表单按钮\">\r\n            <el-switch v-model=\"formConf.formBtns\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"显示未选中组件边框\">\r\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\r\n    <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { isArray } from 'util'\r\nimport draggable from 'vuedraggable'\r\nimport TreeNodeDialog from './TreeNodeDialog'\r\nimport { isNumberStr } from '@/utils/index'\r\nimport IconsDialog from './IconsDialog'\r\nimport {\r\n  inputComponents,\r\n  selectComponents,\r\n  layoutComponents\r\n} from '@/utils/generator/config'\r\n\r\nconst dateTimeFormat = {\r\n  date: 'yyyy-MM-dd',\r\n  week: 'yyyy 第 WW 周',\r\n  month: 'yyyy-MM',\r\n  year: 'yyyy',\r\n  datetime: 'yyyy-MM-dd HH:mm:ss',\r\n  daterange: 'yyyy-MM-dd',\r\n  monthrange: 'yyyy-MM',\r\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    TreeNodeDialog,\r\n    IconsDialog\r\n  },\r\n  props: ['showField', 'activeData', 'formConf'],\r\n  data() {\r\n    return {\r\n      currentTab: 'field',\r\n      currentNode: null,\r\n      dialogVisible: false,\r\n      iconsVisible: false,\r\n      currentIconModel: null,\r\n      dateTypeOptions: [\r\n        {\r\n          label: '日(date)',\r\n          value: 'date'\r\n        },\r\n        {\r\n          label: '周(week)',\r\n          value: 'week'\r\n        },\r\n        {\r\n          label: '月(month)',\r\n          value: 'month'\r\n        },\r\n        {\r\n          label: '年(year)',\r\n          value: 'year'\r\n        },\r\n        {\r\n          label: '日期时间(datetime)',\r\n          value: 'datetime'\r\n        }\r\n      ],\r\n      dateRangeTypeOptions: [\r\n        {\r\n          label: '日期范围(daterange)',\r\n          value: 'daterange'\r\n        },\r\n        {\r\n          label: '月范围(monthrange)',\r\n          value: 'monthrange'\r\n        },\r\n        {\r\n          label: '日期时间范围(datetimerange)',\r\n          value: 'datetimerange'\r\n        }\r\n      ],\r\n      colorFormatOptions: [\r\n        {\r\n          label: 'hex',\r\n          value: 'hex'\r\n        },\r\n        {\r\n          label: 'rgb',\r\n          value: 'rgb'\r\n        },\r\n        {\r\n          label: 'rgba',\r\n          value: 'rgba'\r\n        },\r\n        {\r\n          label: 'hsv',\r\n          value: 'hsv'\r\n        },\r\n        {\r\n          label: 'hsl',\r\n          value: 'hsl'\r\n        }\r\n      ],\r\n      justifyOptions: [\r\n        {\r\n          label: 'start',\r\n          value: 'start'\r\n        },\r\n        {\r\n          label: 'end',\r\n          value: 'end'\r\n        },\r\n        {\r\n          label: 'center',\r\n          value: 'center'\r\n        },\r\n        {\r\n          label: 'space-around',\r\n          value: 'space-around'\r\n        },\r\n        {\r\n          label: 'space-between',\r\n          value: 'space-between'\r\n        }\r\n      ],\r\n      layoutTreeProps: {\r\n        label(data, node) {\r\n          return data.componentName || `${data.label}: ${data.vModel}`\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    documentLink() {\r\n      return (\r\n        this.activeData.document\r\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\r\n      )\r\n    },\r\n    dateOptions() {\r\n      if (\r\n        this.activeData.type !== undefined\r\n        && this.activeData.tag === 'el-date-picker'\r\n      ) {\r\n        if (this.activeData['start-placeholder'] === undefined) {\r\n          return this.dateTypeOptions\r\n        }\r\n        return this.dateRangeTypeOptions\r\n      }\r\n      return []\r\n    },\r\n    tagList() {\r\n      return [\r\n        {\r\n          label: '输入型组件',\r\n          options: inputComponents\r\n        },\r\n        {\r\n          label: '选择型组件',\r\n          options: selectComponents\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    addReg() {\r\n      this.activeData.regList.push({\r\n        pattern: '',\r\n        message: ''\r\n      })\r\n    },\r\n    addSelectItem() {\r\n      this.activeData.options.push({\r\n        label: '',\r\n        value: ''\r\n      })\r\n    },\r\n    addTreeItem() {\r\n      ++this.idGlobal\r\n      this.dialogVisible = true\r\n      this.currentNode = this.activeData.options\r\n    },\r\n    renderContent(h, { node, data, store }) {\r\n      return (\r\n        <div class=\"custom-tree-node\">\r\n          <span>{node.label}</span>\r\n          <span class=\"node-operation\">\r\n            <i on-click={() => this.append(data)}\r\n              class=\"el-icon-plus\"\r\n              title=\"添加\"\r\n            ></i>\r\n            <i on-click={() => this.remove(node, data)}\r\n              class=\"el-icon-delete\"\r\n              title=\"删除\"\r\n            ></i>\r\n          </span>\r\n        </div>\r\n      )\r\n    },\r\n    append(data) {\r\n      if (!data.children) {\r\n        this.$set(data, 'children', [])\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentNode = data.children\r\n    },\r\n    remove(node, data) {\r\n      const { parent } = node\r\n      const children = parent.data.children || parent.data\r\n      const index = children.findIndex(d => d.id === data.id)\r\n      children.splice(index, 1)\r\n    },\r\n    addNode(data) {\r\n      this.currentNode.push(data)\r\n    },\r\n    setOptionValue(item, val) {\r\n      item.value = isNumberStr(val) ? +val : val\r\n    },\r\n    setDefaultValue(val) {\r\n      if (Array.isArray(val)) {\r\n        return val.join(',')\r\n      }\r\n      if (['string', 'number'].indexOf(val) > -1) {\r\n        return val\r\n      }\r\n      if (typeof val === 'boolean') {\r\n        return `${val}`\r\n      }\r\n      return val\r\n    },\r\n    onDefaultValueInput(str) {\r\n      if (isArray(this.activeData.defaultValue)) {\r\n        // 数组\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\r\n        )\r\n      } else if (['true', 'false'].indexOf(str) > -1) {\r\n        // 布尔\r\n        this.$set(this.activeData, 'defaultValue', JSON.parse(str))\r\n      } else {\r\n        // 字符串和数字\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          isNumberStr(str) ? +str : str\r\n        )\r\n      }\r\n    },\r\n    onSwitchValueInput(val, name) {\r\n      if (['true', 'false'].indexOf(val) > -1) {\r\n        this.$set(this.activeData, name, JSON.parse(val))\r\n      } else {\r\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\r\n      }\r\n    },\r\n    setTimeValue(val, type) {\r\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\r\n      this.$set(this.activeData, 'defaultValue', null)\r\n      this.$set(this.activeData, 'value-format', valueFormat)\r\n      this.$set(this.activeData, 'format', val)\r\n    },\r\n    spanChange(val) {\r\n      this.formConf.span = val\r\n    },\r\n    multipleChange(val) {\r\n      this.$set(this.activeData, 'defaultValue', val ? [] : '')\r\n    },\r\n    dateTypeChange(val) {\r\n      this.setTimeValue(dateTimeFormat[val], val)\r\n    },\r\n    rangeChange(val) {\r\n      this.$set(\r\n        this.activeData,\r\n        'defaultValue',\r\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\r\n      )\r\n    },\r\n    rateTextChange(val) {\r\n      if (val) this.activeData['show-score'] = false\r\n    },\r\n    rateScoreChange(val) {\r\n      if (val) this.activeData['show-text'] = false\r\n    },\r\n    colorFormatChange(val) {\r\n      this.activeData.defaultValue = null\r\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\r\n      this.activeData.renderKey = +new Date() // 更新renderKey,重新渲染该组件\r\n    },\r\n    openIconsDialog(model) {\r\n      this.iconsVisible = true\r\n      this.currentIconModel = model\r\n    },\r\n    setIcon(val) {\r\n      this.activeData[this.currentIconModel] = val\r\n    },\r\n    tagChange(tagIcon) {\r\n      let target = inputComponents.find(item => item.tagIcon === tagIcon)\r\n      if (!target) target = selectComponents.find(item => item.tagIcon === tagIcon)\r\n      this.$emit('tag-change', target)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.right-board {\r\n  width: 350px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  padding-top: 3px;\r\n  .field-box {\r\n    position: relative;\r\n    height: calc(100vh - 42px);\r\n    box-sizing: border-box;\r\n    overflow: hidden;\r\n  }\r\n  .el-scrollbar {\r\n    height: 100%;\r\n  }\r\n}\r\n.select-item {\r\n  display: flex;\r\n  border: 1px dashed #fff;\r\n  box-sizing: border-box;\r\n  & .close-btn {\r\n    cursor: pointer;\r\n    color: #f56c6c;\r\n  }\r\n  & .el-input + .el-input {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n.select-item + .select-item {\r\n  margin-top: 4px;\r\n}\r\n.select-item.sortable-chosen {\r\n  border: 1px dashed #409eff;\r\n}\r\n.select-line-icon {\r\n  line-height: 32px;\r\n  font-size: 22px;\r\n  padding: 0 4px;\r\n  color: #777;\r\n}\r\n.option-drag {\r\n  cursor: move;\r\n}\r\n.time-range {\r\n  .el-date-editor {\r\n    width: 227px;\r\n  }\r\n  ::v-deep .el-icon-time {\r\n    display: none;\r\n  }\r\n}\r\n.document-link {\r\n  position: absolute;\r\n  display: block;\r\n  width: 26px;\r\n  height: 26px;\r\n  top: 0;\r\n  left: 0;\r\n  cursor: pointer;\r\n  background: #409eff;\r\n  z-index: 1;\r\n  border-radius: 0 0 6px 0;\r\n  text-align: center;\r\n  line-height: 26px;\r\n  color: #fff;\r\n  font-size: 18px;\r\n}\r\n.node-label{\r\n  font-size: 14px;\r\n}\r\n.node-icon{\r\n  color: #bebfc3;\r\n}\r\n</style>\r\n"]}]}