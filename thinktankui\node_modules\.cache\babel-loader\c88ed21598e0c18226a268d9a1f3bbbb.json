{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js!D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\components\\Screenfull\\index.vue", "mtime": 1749109381327}, {"path": "D:\\thinktank\\thinktankui\\babel.config.js", "mtime": 1749109381288}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi90aGlua3RhbmsvdGhpbmt0YW5rdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfc2NyZWVuZnVsbCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgic2NyZWVuZnVsbCIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdTY3JlZW5mdWxsJywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNGdWxsc2NyZWVuOiBmYWxzZQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXQoKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLmRlc3Ryb3koKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgaWYgKCFfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRW5hYmxlZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+S9oOeahOa1j+iniOWZqOS4jeaUr+aMgeWFqOWxjycsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgX3NjcmVlbmZ1bGwuZGVmYXVsdC50b2dnbGUoKTsKICAgIH0sCiAgICBjaGFuZ2U6IGZ1bmN0aW9uIGNoYW5nZSgpIHsKICAgICAgdGhpcy5pc0Z1bGxzY3JlZW4gPSBfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRnVsbHNjcmVlbjsKICAgIH0sCiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICBpZiAoX3NjcmVlbmZ1bGwuZGVmYXVsdC5pc0VuYWJsZWQpIHsKICAgICAgICBfc2NyZWVuZnVsbC5kZWZhdWx0Lm9uKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0sCiAgICBkZXN0cm95OiBmdW5jdGlvbiBkZXN0cm95KCkgewogICAgICBpZiAoX3NjcmVlbmZ1bGwuZGVmYXVsdC5pc0VuYWJsZWQpIHsKICAgICAgICBfc2NyZWVuZnVsbC5kZWZhdWx0Lm9mZignY2hhbmdlJywgdGhpcy5jaGFuZ2UpOwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_screenfull", "_interopRequireDefault", "require", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "screenfull", "isEnabled", "$message", "message", "type", "toggle", "change", "on", "off"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      if (!screenfull.isEnabled) {\r\n        this.$message({ message: '你的浏览器不支持全屏', type: 'warning' })\r\n        return false\r\n      }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAOA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,mBAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UAAAC,OAAA;UAAAC,IAAA;QAAA;QACA;MACA;MACAJ,mBAAA,CAAAK,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAb,YAAA,GAAAO,mBAAA,CAAAP,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAK,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAO,EAAA,gBAAAD,MAAA;MACA;IACA;IACAT,OAAA,WAAAA,QAAA;MACA,IAAAG,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAQ,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}