{"version": 3, "file": "highlight-by-x.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/element/highlight-by-x.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AAC1E,OAAO,SAAS,EAAE,EAAE,aAAa,EAAE,eAAe,EAAY,MAAM,aAAa,CAAC;AAElF;;;GAGG;AACH;IAAyB,8BAAS;IAAlC;;IAqCA,CAAC;IApCC,QAAQ;IACE,wCAAmB,GAA7B,UAA8B,EAAW,EAAE,QAAkB;QAC3D,IAAI,QAAQ,CAAC,EAAE,CAAC,EAAE;YAChB,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;gBAChC,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SAClC;aAAM;YACL,EAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YACnC,IAAI,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;gBAC9B,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;aACnC;SACF;IACH,CAAC;IAES,sCAAiB,GAA3B,UAA4B,OAAgB,EAAE,MAAe;QAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/B,IAAM,KAAK,GAAG,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QACpD,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACnC,IAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,EAAE;YAC3C,OAAO,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QACpD,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAC,EAAE,IAAK,OAAA,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,EAA9B,CAA8B,EAAE,MAAM,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,2BAAM,GAAb;QACE,IAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE;YACX,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;SAC5C;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AArCD,CAAyB,SAAS,GAqCjC;AAED,eAAe,UAAU,CAAC", "sourcesContent": ["import Element from '../../../geometry/element/';\nimport { getElements, getElementValue, getCurrentElement } from '../util';\nimport Highlight, { STATUS_ACTIVE, STATUS_UNACTIVE, Callback } from './highlight';\n\n/**\n * Highlight x\n * @ignore\n */\nclass HighlightX extends Highlight {\n  // 不允许多选\n  protected setElementHighlight(el: Element, callback: Callback) {\n    if (callback(el)) {\n      if (el.hasState(STATUS_UNACTIVE)) {\n        el.setState(STATUS_UNACTIVE, false);\n      }\n      el.setState(STATUS_ACTIVE, true);\n    } else {\n      el.setState(STATUS_UNACTIVE, true);\n      if (el.hasState(STATUS_ACTIVE)) {\n        el.setState(STATUS_ACTIVE, false);\n      }\n    }\n  }\n\n  protected setStateByElement(element: Element, enable: boolean) {\n    const view = this.context.view;\n    const scale = view.getXScale();\n    const value = getElementValue(element, scale.field);\n    const elements = getElements(view);\n    const highlightElements = elements.filter((el) => {\n      return getElementValue(el, scale.field) === value;\n    });\n    this.setHighlightBy(elements, (el) => highlightElements.includes(el), enable);\n  }\n\n  /**\n   * 切换状态\n   */\n  public toggle() {\n    const element = getCurrentElement(this.context);\n    if (element) {\n      const hasState = element.hasState(this.stateName);\n      this.setStateByElement(element, !hasState);\n    }\n  }\n}\n\nexport default HighlightX;\n"]}