{"version": 3, "file": "adaptor.js", "sourceRoot": "", "sources": ["../../../src/plots/scatter/adaptor.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC3G,OAAO,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAEjD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAEnF,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,wBAAwB,EAAE,MAAM,QAAQ,CAAC;AAEpE;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAuB;IAC9C,IAAA,KAA8B,OAAO,KAA5B,EAAT,IAAI,mBAAG,EAAE,KAAA,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAE9C,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,8BAA8B;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,IAAI,SAAA,CAAC;QAET,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEf,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;gBACjC,MAAM,GAAG,KAAK,CAAC;aAChB;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE;gBACjC,MAAM,GAAG,KAAK,CAAC;aAChB;YAED,2BAA2B;YAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;gBACtB,MAAM;aACP;YAED,IAAI,GAAG,IAAI,CAAC;SACb;QAED,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE5B,IAAM,MAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;QAE1C,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,QAAA,EAAE,CAAC,CAAC;KAC1C;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,SAAS,QAAQ,CAAC,MAA8B;IACtC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,IAAI,GAAwF,OAAO,KAA/F,EAAE,IAAI,GAAkF,OAAO,KAAzF,EAAE,KAAK,GAA2E,OAAO,MAAlF,EAAE,KAAK,GAAoE,OAAO,MAA3E,EAAE,UAAU,GAAwD,OAAO,WAA/D,EAAE,UAAU,GAA4C,OAAO,WAAnD,EAAE,UAAU,GAAgC,OAAO,WAAvC,EAAE,MAAM,GAAwB,OAAO,OAA/B,EAAE,MAAM,GAAgB,OAAO,OAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IACtG,IAAA,IAAI,GAAK,OAAO,KAAZ,CAAa;IAEjB,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE1B,IAAI,SAAS,EAAE;QACb,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACf;QACD,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClB,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACrB;KACF;IAED,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QAC9B,OAAO,yBACF,OAAO,KACV,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,GAC5D,CAAC;KACH;IACD,KAAK;IACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEjB,WAAW;IACX,KAAK,CACH,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE;QACrB,OAAO,EAAE;YACP,WAAW,EAAE,UAAU;YACvB,KAAK,EAAE;gBACL,KAAK,OAAA;gBACL,KAAK,OAAA;gBACL,IAAI,MAAA;gBACJ,KAAK,EAAE,UAAU;aAClB;YACD,OAAO,SAAA;SACR;KACF,CAAC,CACH,CAAC;IAEF,IAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE9C,OAAO;IACP,IAAI,IAAI,EAAE;QACR,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACvB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAC,MAA8B;;IACzC,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,IAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC7C,OAAO,IAAI,CACT,KAAK;QACH,GAAC,MAAM,IAAG,KAAK;QACf,GAAC,MAAM,IAAG,KAAK;YACf,CACH,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AACrD,CAAC;AAED;;;GAGG;AACH,SAAS,IAAI,CAAC,MAA8B;IAClC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAA4B,OAAO,MAAnC,EAAE,KAAK,GAAqB,OAAO,MAA5B,EAAE,MAAM,GAAa,OAAO,OAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAEjD,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE1B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,MAAM,CAAC,MAA8B;IACpC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,MAAM,GAAiE,OAAO,OAAxE,EAAE,UAAU,GAAqD,OAAO,WAA5D,EAAE,UAAU,GAAyC,OAAO,WAAhD,EAAE,SAAS,GAA8B,OAAO,UAArC,EAAE,WAAW,GAAiB,OAAO,YAAxB,EAAE,UAAU,GAAK,OAAO,WAAZ,CAAa;IAEvF,8CAA8C;IAC9C,IAAM,UAAU,GAAG,MAAM,KAAK,KAAK,CAAC;IAEpC,IAAI,UAAU,EAAE;QACd,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KACvD;IAED,8BAA8B;IAC9B,IAAI,UAAU,EAAE;QACd,IAAI,WAAW,EAAE;YACf,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;SACvC;aAAM;YACL,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;SAClE;KACF;IAED,IAAI,SAAS,EAAE;QACb,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KAC1D;IAED,sDAAsD;IACtD,6BAA6B;IAC7B,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE;QAC9C,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACrB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAS,KAAK,CAAC,MAA8B;IACnC,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,KAAK,GAAa,OAAO,MAApB,EAAE,MAAM,GAAK,OAAO,OAAZ,CAAa;IAElC,IAAM,eAAe,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAErD,8BAA8B;IAC9B,IAAI,CAAC,KAAK,EAAE;QACV,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC9B;SAAM;QACG,IAAA,QAAQ,GAAa,KAAK,SAAlB,EAAK,GAAG,UAAK,KAAK,EAA5B,YAAoB,CAAF,CAAW;QACnC,eAAe,CAAC,KAAK,CAAC;YACpB,MAAM,EAAE,CAAC,MAAM,CAAC;YAChB,QAAQ,UAAA;YACR,GAAG,EAAE,cAAc,CAAC,GAAG,CAAC;SACzB,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAS,iBAAiB,CAAC,MAA8B;IAC/C,IAAA,OAAO,GAAK,MAAM,QAAX,CAAY;IACnB,IAAA,QAAQ,GAAK,OAAO,SAAZ,CAAa;IAE7B,IAAM,iBAAiB,GAAG,EAAE,CAAC;IAE7B,IAAI,QAAQ,EAAE;QACJ,IAAA,KAAiE,QAAQ,UAA5D,EAAb,SAAS,mBAAG,CAAC,KAAA,EAAE,KAAkD,QAAQ,UAA7C,EAAb,SAAS,mBAAG,CAAC,KAAA,EAAE,QAAM,GAA6B,QAAQ,OAArC,EAAE,aAAW,GAAgB,QAAQ,YAAxB,EAAE,SAAS,GAAK,QAAQ,UAAb,CAAc;QAClF,IAAM,eAAa,GAAG,wBAAwB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACrE,SAAS;QACT,IAAM,SAAS,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,SAAS,CAAC,OAAO,CAAC,UAAC,CAAS,EAAE,KAAa;YACzC,iBAAiB,CAAC,IAAI,qBAElB,IAAI,EAAE,QAAQ,EACd,GAAG,EAAE,KAAK,IACP,eAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,KAC5C,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,eAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,aAAW,aAAX,aAAW,uBAAX,aAAW,CAAG,KAAK,CAAC,CAAC,gBAGnF,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,IAAI,IACN,UAAU,CAAC,EAAE,EAAE,eAAa,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,QAAM,aAAN,QAAM,uBAAN,QAAM,CAAG,KAAK,CAAC,CAAC,EAEtE,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,QAAQ;QACR,iBAAiB,CAAC,IAAI,CACpB;YACE,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,KAAK;YACV,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;YACzB,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;YACvB,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,eAAa,CAAC,SAAS,EAAE,SAAS,CAAC;SAC1D,EACD;YACE,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,KAAK;YACV,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;YACzB,GAAG,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;YACvB,KAAK,EAAE,UAAU,CAAC,EAAE,EAAE,eAAa,CAAC,SAAS,EAAE,SAAS,CAAC;SAC1D,CACF,CAAC;KACH;IAED,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACrD,CAAC;AAED,MAAM;AACN,SAAS,cAAc,CAAC,MAA8B;IAC5C,IAAA,OAAO,GAAY,MAAM,QAAlB,EAAE,KAAK,GAAK,MAAM,MAAX,CAAY;IAC1B,IAAA,cAAc,GAAK,OAAO,eAAZ,CAAa;IACnC,IAAI,cAAc,EAAE;QACV,IAAA,OAAK,GAA4D,cAAc,MAA1E,EAAE,KAA0D,cAAc,cAAtD,EAAlB,eAAa,mBAAG,EAAE,KAAA,EAAE,KAAsC,cAAc,IAAzC,EAAX,KAAG,mBAAG,KAAK,KAAA,EAAE,KAAyB,cAAc,aAAnB,EAApB,cAAY,mBAAG,KAAK,KAAA,CAAoB;QACxF,IAAM,cAAY,GAAG;YACnB,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,GAAG;SACb,CAAC;QACF,IAAM,qBAAmB,GAAG;YAC1B,CAAC,EAAE,EAAE;YACL,CAAC,EAAE,EAAE;YACL,SAAS,EAAE,MAAe;YAC1B,YAAY,EAAE,QAAiB;YAC/B,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,GAAG;SACjB,CAAC;QACF,KAAK,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC;YACvB,GAAG,OAAA;YACH,MAAM,EAAE,UAAC,SAAS,EAAE,IAAI;gBACtB,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC;oBAC/B,EAAE,EAAE,UAAG,KAAK,CAAC,EAAE,qBAAkB;oBACjC,IAAI,EAAE,uBAAuB;iBAC9B,CAAC,CAAC;gBACG,IAAA,KAAmB,OAAO,CAAC;oBAC/B,IAAI,MAAA;oBACJ,OAAO,SAAA;iBACR,CAAC,EAHK,IAAI,QAAA,EAAE,QAAQ,QAGnB,CAAC;gBACH,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACrB,IAAI,EAAE,iBAAiB;oBACvB,KAAK,sBACH,IAAI,MAAA,IACD,cAAY,GACZ,OAAK,CACT;iBACF,CAAC,CAAC;gBACH,IAAI,cAAY,EAAE;oBAChB,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE;wBACrB,IAAI,EAAE,qBAAqB;wBAC3B,KAAK,iCACA,qBAAmB,GACnB,eAAa,KAChB,IAAI,EAAE,QAAQ,GACf;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAA8B;IAC5C,IAAA,KAAK,GAAc,MAAM,MAApB,EAAE,OAAO,GAAK,MAAM,QAAX,CAAY;IAC1B,IAAA,OAAO,GAAK,OAAO,QAAZ,CAAa;IAE5B,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KACxB;SAAM,IAAI,OAAO,KAAK,KAAK,EAAE;QAC5B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KACtB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,MAA8B;IACpD,0BAA0B;IAC1B,OAAO,IAAI,CACT,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,OAAO,EACP,KAAK;IACL,qBAAqB;IACrB,gBAAgB,EAChB,MAAM,EACN,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,SAAS,EACT,KAAK,EACL,cAAc,CACf,CAAC,MAAM,CAAC,CAAC;AACZ,CAAC", "sourcesContent": ["import { isNumber } from '@antv/util';\nimport { brushInteraction } from '../../adaptor/brush';\nimport { animation, annotation, interaction, scale, scrollbar, slider, theme } from '../../adaptor/common';\nimport { point } from '../../adaptor/geometries';\nimport { Params } from '../../core/adaptor';\nimport { deepAssign, findGeometry, flow, pick, transformLabel } from '../../utils';\nimport { ScatterOptions } from './types';\nimport { getMeta, getPath, getQuadrantDefaultConfig } from './util';\n\n/**\n * 散点图默认美观\n * ① data.length === 1 ② 所有数据 y 值相等 ③ 所有数据 x 值相等\n * @param params\n * @returns params\n */\nexport function transformOptions(options: ScatterOptions): ScatterOptions {\n  const { data = [], xField, yField } = options;\n\n  if (data.length) {\n    // x y 字段知否只有一个值，如果只有一个值，则进行优化\n    let isOneX = true;\n    let isOneY = true;\n\n    let prev = data[0];\n    let curr;\n\n    for (let i = 1; i < data.length; i++) {\n      curr = data[i];\n\n      if (prev[xField] !== curr[xField]) {\n        isOneX = false;\n      }\n\n      if (prev[yField] !== curr[yField]) {\n        isOneY = false;\n      }\n\n      // 如果都不是 oneValue，那么可提前跳出循环\n      if (!isOneX && !isOneY) {\n        break;\n      }\n\n      prev = curr;\n    }\n\n    const keys = [];\n    isOneX && keys.push(xField);\n    isOneY && keys.push(yField);\n\n    const meta = pick(getMeta(options), keys);\n\n    return deepAssign({}, options, { meta });\n  }\n\n  return options;\n}\n\n/**\n * 字段\n * @param params\n */\nfunction geometry(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { chart, options } = params;\n  const { data, type, color, shape, pointStyle, shapeField, colorField, xField, yField, sizeField } = options;\n  let { size } = options;\n\n  let { tooltip } = options;\n\n  if (sizeField) {\n    if (!size) {\n      size = [2, 8];\n    }\n    if (isNumber(size)) {\n      size = [size, size];\n    }\n  }\n\n  if (tooltip && !tooltip.fields) {\n    tooltip = {\n      ...tooltip,\n      fields: [xField, yField, colorField, sizeField, shapeField],\n    };\n  }\n  // 数据\n  chart.data(data);\n\n  // geometry\n  point(\n    deepAssign({}, params, {\n      options: {\n        seriesField: colorField,\n        point: {\n          color,\n          shape,\n          size,\n          style: pointStyle,\n        },\n        tooltip,\n      },\n    })\n  );\n\n  const geometry = findGeometry(chart, 'point');\n\n  // 数据调整\n  if (type) {\n    geometry.adjust(type);\n  }\n\n  return params;\n}\n\n/**\n * meta 配置\n * @param params\n */\nexport function meta(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  const newOptions = transformOptions(options);\n  return flow(\n    scale({\n      [xField]: xAxis,\n      [yField]: yAxis,\n    })\n  )(deepAssign({}, params, { options: newOptions }));\n}\n\n/**\n * axis 配置\n * @param params\n */\nfunction axis(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { chart, options } = params;\n  const { xAxis, yAxis, xField, yField } = options;\n\n  chart.axis(xField, xAxis);\n  chart.axis(yField, yAxis);\n\n  return params;\n}\n\n/**\n * legend 配置\n * @param params\n */\nfunction legend(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { chart, options } = params;\n  const { legend, colorField, shapeField, sizeField, shapeLegend, sizeLegend } = options;\n\n  /** legend 不为 false, 则展示图例, 优先展示 color 分类图例 */\n  const showLegend = legend !== false;\n\n  if (colorField) {\n    chart.legend(colorField, showLegend ? legend : false);\n  }\n\n  // 优先取 shapeLegend, 否则取 legend\n  if (shapeField) {\n    if (shapeLegend) {\n      chart.legend(shapeField, shapeLegend);\n    } else {\n      chart.legend(shapeField, shapeLegend === false ? false : legend);\n    }\n  }\n\n  if (sizeField) {\n    chart.legend(sizeField, sizeLegend ? sizeLegend : false);\n  }\n\n  /** 默认不展示 shape 图例，当 shapeLegend 为 undefined 也不展示图例 */\n  /** 默认没有 sizeField，则隐藏连续图例 */\n  if (!showLegend && !shapeLegend && !sizeLegend) {\n    chart.legend(false);\n  }\n\n  return params;\n}\n\n/**\n * 数据标签\n * @param params\n */\nfunction label(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { chart, options } = params;\n  const { label, yField } = options;\n\n  const scatterGeometry = findGeometry(chart, 'point');\n\n  // label 为 false, 空 则不显示 label\n  if (!label) {\n    scatterGeometry.label(false);\n  } else {\n    const { callback, ...cfg } = label;\n    scatterGeometry.label({\n      fields: [yField],\n      callback,\n      cfg: transformLabel(cfg),\n    });\n  }\n\n  return params;\n}\n\n/**\n * annotation 配置\n * - 特殊 annotation: quadrant(四象限)\n * @param params\n */\nfunction scatterAnnotation(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { options } = params;\n  const { quadrant } = options;\n\n  const annotationOptions = [];\n\n  if (quadrant) {\n    const { xBaseline = 0, yBaseline = 0, labels, regionStyle, lineStyle } = quadrant;\n    const defaultConfig = getQuadrantDefaultConfig(xBaseline, yBaseline);\n    // 仅支持四象限\n    const quadrants = new Array(4).join(',').split(',');\n    quadrants.forEach((_: string, index: number) => {\n      annotationOptions.push(\n        {\n          type: 'region',\n          top: false,\n          ...defaultConfig.regionStyle[index].position,\n          style: deepAssign({}, defaultConfig.regionStyle[index].style, regionStyle?.[index]),\n        },\n        {\n          type: 'text',\n          top: true,\n          ...deepAssign({}, defaultConfig.labelStyle[index], labels?.[index]),\n        }\n      );\n    });\n    // 生成坐标轴\n    annotationOptions.push(\n      {\n        type: 'line',\n        top: false,\n        start: ['min', yBaseline],\n        end: ['max', yBaseline],\n        style: deepAssign({}, defaultConfig.lineStyle, lineStyle),\n      },\n      {\n        type: 'line',\n        top: false,\n        start: [xBaseline, 'min'],\n        end: [xBaseline, 'max'],\n        style: deepAssign({}, defaultConfig.lineStyle, lineStyle),\n      }\n    );\n  }\n\n  return flow(annotation(annotationOptions))(params);\n}\n\n// 趋势线\nfunction regressionLine(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { options, chart } = params;\n  const { regressionLine } = options;\n  if (regressionLine) {\n    const { style, equationStyle = {}, top = false, showEquation = false } = regressionLine;\n    const defaultStyle = {\n      stroke: '#9ba29a',\n      lineWidth: 2,\n      opacity: 0.5,\n    };\n    const defaulEquationStyle = {\n      x: 20,\n      y: 20,\n      textAlign: 'left' as const,\n      textBaseline: 'middle' as const,\n      fontSize: 14,\n      fillOpacity: 0.5,\n    };\n    chart.annotation().shape({\n      top,\n      render: (container, view) => {\n        const group = container.addGroup({\n          id: `${chart.id}-regression-line`,\n          name: 'regression-line-group',\n        });\n        const [path, equation] = getPath({\n          view,\n          options,\n        });\n        group.addShape('path', {\n          name: 'regression-line',\n          attrs: {\n            path,\n            ...defaultStyle,\n            ...style,\n          },\n        });\n        if (showEquation) {\n          group.addShape('text', {\n            name: 'regression-equation',\n            attrs: {\n              ...defaulEquationStyle,\n              ...equationStyle,\n              text: equation,\n            },\n          });\n        }\n      },\n    });\n  }\n\n  return params;\n}\n\n/**\n * tooltip 配置\n * @param params\n */\nexport function tooltip(params: Params<ScatterOptions>): Params<ScatterOptions> {\n  const { chart, options } = params;\n  const { tooltip } = options;\n\n  if (tooltip) {\n    chart.tooltip(tooltip);\n  } else if (tooltip === false) {\n    chart.tooltip(false);\n  }\n\n  return params;\n}\n\n/**\n * 散点图适配器\n * @param chart\n * @param options\n */\nexport function adaptor(params: Params<ScatterOptions>) {\n  // flow 的方式处理所有的配置到 G2 API\n  return flow(\n    geometry,\n    meta,\n    axis,\n    legend,\n    tooltip,\n    label,\n    // 需要在 interaction 前面\n    brushInteraction,\n    slider,\n    scrollbar,\n    interaction,\n    scatterAnnotation,\n    animation,\n    theme,\n    regressionLine\n  )(params);\n}\n"]}