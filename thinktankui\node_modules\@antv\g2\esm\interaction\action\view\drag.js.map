{"version": 3, "file": "drag.js", "sourceRoot": "", "sources": ["../../../../src/interaction/action/view/drag.ts"], "names": [], "mappings": ";AACA,OAAO,MAAM,MAAM,SAAS,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEnC,IAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,UAAU;AAE9B;;;GAGG;AACH;IAAmB,wBAAM;IAAzB;QAAA,qEA4DC;QA3DC,gCAAgC;QACtB,cAAQ,GAAG,KAAK,CAAC;QAC3B,OAAO;QACG,eAAS,GAAG,KAAK,CAAC;;IAwD9B,CAAC;IApDC;;OAEG;IACI,oBAAK,GAAZ;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,mBAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,OAAO;SACR;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAC/C,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,EAAE;gBACjD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,CAAC,EAAE,KAAK,CAAC,CAAC;oBACV,CAAC,EAAE,KAAK,CAAC,CAAC;iBACX,CAAC,CAAC;gBACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;aACvB;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC;aACX,CAAC,CAAC;SACJ;IACH,CAAC;IAED;;OAEG;IACI,kBAAG,GAAV;QACE,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAC/B,IAAM,OAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,MAAM,EAAE,OAAK,CAAC,MAAM;gBACpB,CAAC,EAAE,OAAK,CAAC,CAAC;gBACV,CAAC,EAAE,OAAK,CAAC,CAAC;aACX,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IACzB,CAAC;IACH,WAAC;AAAD,CAAC,AA5DD,CAAmB,MAAM,GA4DxB;AAED,eAAe,IAAI,CAAC", "sourcesContent": ["import { Point } from '../../../dependents';\nimport Action from '../base';\nimport { distance } from '../util';\n\nconst DISTANCE = 4; // 移动的最小距离\n\n/**\n * @ignore\n * View 支持 Drag 的 Action\n */\nclass Drag extends Action {\n  // Action 开始，不等同于 拖拽开始，需要判定移动的范围\n  protected starting = false;\n  // 拖拽开始\n  protected dragStart = false;\n  // 开始的节点\n  protected startPoint: Point;\n\n  /**\n   * 开始\n   */\n  public start() {\n    this.starting = true;\n    this.startPoint = this.context.getCurrentPoint();\n  }\n\n  /**\n   * 拖拽\n   */\n  public drag() {\n    if (!this.startPoint) {\n      return;\n    }\n    const current = this.context.getCurrentPoint();\n    const view = this.context.view;\n    const event = this.context.event;\n    if (!this.dragStart) {\n      if (distance(current, this.startPoint) > DISTANCE) {\n        view.emit('dragstart', {\n          target: event.target,\n          x: event.x,\n          y: event.y,\n        });\n        this.dragStart = true;\n      }\n    } else {\n      view.emit('drag', {\n        target: event.target,\n        x: event.x,\n        y: event.y,\n      });\n    }\n  }\n\n  /**\n   * 结束\n   */\n  public end() {\n    if (this.dragStart) {\n      const view = this.context.view;\n      const event = this.context.event;\n      view.emit('dragend', {\n        target: event.target,\n        x: event.x,\n        y: event.y,\n      });\n    }\n    this.starting = false;\n    this.dragStart = false;\n  }\n}\n\nexport default Drag;\n"]}