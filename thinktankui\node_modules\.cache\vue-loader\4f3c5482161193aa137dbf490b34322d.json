{"remainingRequest": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\thinktank\\thinktankui\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\thinktank\\thinktankui\\src\\layout\\components\\Navbar.vue", "mtime": 1749109381330}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749109531429}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749109530128}, {"path": "D:\\thinktank\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1749109532005}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <div class=\"navbar\">\r\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\r\n\r\n    <breadcrumb id=\"breadcrumb-container\" class=\"breadcrumb-container\" v-if=\"!topNav\"/>\r\n    <top-nav id=\"topmenu-container\" class=\"topmenu-container\" v-if=\"topNav\"/>\r\n\r\n    <div class=\"right-menu\">\r\n      <template v-if=\"device!=='mobile'\">\r\n        <search id=\"header-search\" class=\"right-menu-item\" />\r\n\r\n        <el-tooltip content=\"源码地址\" effect=\"dark\" placement=\"bottom\">\r\n          <ruo-yi-git id=\"ruoyi-git\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n        <el-tooltip content=\"文档地址\" effect=\"dark\" placement=\"bottom\">\r\n          <ruo-yi-doc id=\"ruoyi-doc\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\" />\r\n\r\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\r\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\" />\r\n        </el-tooltip>\r\n\r\n      </template>\r\n\r\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\r\n        <div class=\"avatar-wrapper\">\r\n          <img :src=\"avatar\" class=\"user-avatar\">\r\n          <i class=\"el-icon-caret-bottom\" />\r\n        </div>\r\n        <el-dropdown-menu slot=\"dropdown\">\r\n          <router-link to=\"/user/profile\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n          </router-link>\r\n          <el-dropdown-item @click.native=\"setting = true\">\r\n            <span>布局设置</span>\r\n          </el-dropdown-item>\r\n          <el-dropdown-item divided @click.native=\"logout\">\r\n            <span>退出登录</span>\r\n          </el-dropdown-item>\r\n        </el-dropdown-menu>\r\n      </el-dropdown>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport RuoYiGit from '@/components/RuoYi/Git'\r\nimport RuoYiDoc from '@/components/RuoYi/Doc'\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    async logout() {\r\n      this.$confirm('确定注销并退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/index';\r\n        })\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color:transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 30px;\r\n\r\n      .avatar-wrapper {\r\n        // margin-top: 5px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}